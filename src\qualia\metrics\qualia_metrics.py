"""
qualia_metrics.py – Projeto QUALIA
===================================
Módulo que consolida as **quatro métricas experimentais H1–H4** descritas no relatório
*Validação Experimental (Simulada) das Hipóteses H1‑H4*.

Cada métrica é exposta através de um método estático na classe ``QualiaMetrics``.
Os métodos retornam dataclasses simples com resultados e um flag ``passes`` que
indica se o critério estatístico foi satisfeito conforme o documento.

Referência de critérios (ver relatório):
• **H1** – Atrator de coerência: coeficiente de variação (CV) < 10 %.
• **H2** – Retrocausalidade: ``p < 0.05`` em testes *t* (coerência↑ & entropia↓).
• **H3** – Escalas dominantes: ``p < 0.01`` em qui‑quadrado contra uniforme.
• **H4** – PulsoTranscendência: pelo menos um pico espectral acima de
  ``μ + 3σ`` do espectro‑base (≈ p < 0.003 ⇒ p < 0.01).

Requisitos
~~~~~~~~~~
``numpy`` • ``scipy``

Uso rápido
~~~~~~~~~~
>>> from ..metrics.qualia_metrics import QualiaMetrics
>>> h1 = QualiaMetrics.h1_attractor(list_of_trajectories)
>>> h2 = QualiaMetrics.h2_retro(coh_ctrl, coh_retro, ent_ctrl, ent_retro)
>>> h3 = QualiaMetrics.h3_scale_preference(observed_counts)
>>> h4 = QualiaMetrics.h4_pulse_detection(series_pulse, series_base)

"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Sequence, Tuple, Optional

import numpy as np
from numpy.typing import NDArray
from numpy.fft import rfft, rfftfreq
from scipy import stats

# -----------------------------------------------------------------------------
# Result containers
# -----------------------------------------------------------------------------


@dataclass
class H1Result:
    cv: float
    passes: bool


@dataclass
class H2Result:
    t_coherence: float
    p_coherence: float
    t_entropy: float
    p_entropy: float
    passes: bool


@dataclass
class H3Result:
    chi2: float
    p_value: float
    passes: bool


@dataclass
class H4Result:
    significant_freqs: NDArray
    threshold: float
    passes: bool


# -----------------------------------------------------------------------------
# Main API
# -----------------------------------------------------------------------------


class QualiaMetrics:
    """Coleção de métodos para avaliar as hipóteses H1–H4."""

    # --------------------------------- H1 ---------------------------------- #
    @staticmethod
    def h1_attractor(
        trajectories: Sequence[NDArray], *, cv_threshold: float = 0.10
    ) -> H1Result:
        """Avalia H1: estabilidade de coerência global ⟨E⟩.

        Parâmetros
        ----------
        trajectories
            Sequência de arrays 1‑D contendo a trajetória temporal de ⟨E⟩ para cada execução.
        cv_threshold
            Limite máximo do coeficiente de variação aceito (padrão = 0.10 ⇒ 10 %).
        """
        final_values = np.array([traj[-1] for traj in trajectories], dtype=float)
        cv = float(np.std(final_values, ddof=1) / np.mean(final_values))
        return H1Result(cv=cv, passes=bool(cv < cv_threshold))

    # --------------------------------- H2 ---------------------------------- #
    @staticmethod
    def h2_retro(
        coherence_control: NDArray,
        coherence_retro: NDArray,
        entropy_control: NDArray,
        entropy_retro: NDArray,
        *,
        alpha: float = 0.05,
    ) -> H2Result:
        """Avalia H2: impacto da retrocausalidade em ⟨E⟩ e H.

        Usa *t‑test* para média de duas amostras independentes (``equal_var=False``).
        Passa se **ambos** p‑values < ``alpha``.
        """
        t_coh, p_coh = stats.ttest_ind(
            coherence_control, coherence_retro, equal_var=False
        )
        t_ent, p_ent = stats.ttest_ind(entropy_control, entropy_retro, equal_var=False)
        passes = bool((p_coh < alpha) and (p_ent < alpha))
        return H2Result(
            t_coherence=float(t_coh),
            p_coherence=float(p_coh),
            t_entropy=float(t_ent),
            p_entropy=float(p_ent),
            passes=passes,
        )

    # --------------------------------- H3 ---------------------------------- #
    @staticmethod
    def h3_scale_preference(
        observed_counts: NDArray,
        *,
        alpha: float = 0.01,
    ) -> H3Result:
        """Avalia H3: preferência por escalas dominantes.

        Se ``expected_counts`` não for fornecido, assume distribuição uniforme com mesma média.
        """
        observed_counts = np.asarray(observed_counts, dtype=float)
        expected_counts = np.full_like(observed_counts, observed_counts.mean())
        chi2, p_value = stats.chisquare(observed_counts, f_exp=expected_counts)
        return H3Result(
            chi2=float(chi2), p_value=float(p_value), passes=bool(p_value < alpha)
        )

    # --------------------------------- H4 ---------------------------------- #
    @staticmethod
    def h4_pulse_detection(
        series_pulse: NDArray,
        series_base: Optional[NDArray] = None,
        *,
        sigma_threshold: float = 3.0,
    ) -> H4Result:
        """Avalia H4: detecção do PulsoTranscendência via análise espectral.

        Parâmetros
        ----------
        series_pulse
            Série temporal contendo potencial pulso de entropia.
        series_base
            Série de referência *baseline* (mesmo tamanho). Se ``None``,
            assume‑se que o baseline é a própria série sem pulso removendo o pulso
            (método simples: ordena e substitui percentil <5% por média). Para maior
            robustez, forneça explicitamente uma série controle.
        sigma_threshold
            Quantas sigmas acima da média do espectro‑base definem o limite.
        """
        series_pulse = np.asarray(series_pulse, dtype=float)
        if series_base is None:
            # constrói baseline aproximado removendo outliers da própria série
            base = series_pulse.copy()
            lower_q, upper_q = np.percentile(base, [5, 95])
            base[(base < lower_q) | (base > upper_q)] = np.mean(base)
            series_base = base
        else:
            series_base = np.asarray(series_base, dtype=float)
        # FFT
        fft_base = np.abs(rfft(series_base - np.mean(series_base)))
        fft_pulse = np.abs(rfft(series_pulse - np.mean(series_pulse)))
        threshold = float(np.mean(fft_base) + sigma_threshold * np.std(fft_base))
        significant = fft_pulse > threshold
        freqs = rfftfreq(series_pulse.size, d=1.0)
        significant_freqs = freqs[significant]
        passes = bool(significant.any())
        return H4Result(
            significant_freqs=significant_freqs, threshold=threshold, passes=passes
        )


__all__ = [
    "QualiaMetrics",
    "H1Result",
    "H2Result",
    "H3Result",
    "H4Result",
]
