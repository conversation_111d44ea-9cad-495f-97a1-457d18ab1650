#!/usr/bin/env python3
from __future__ import annotations

"""
QUALIA Data Warm-up Mo<PERSON><PERSON> responsável por garantir o pré-carregamento de dados históricos
suficientes antes do início das operações de trading.

YAA TASK-01: Implementação do warm-up de dados para prevenir falhas
por histórico insuficiente durante a inicialização.
"""

import asyncio
import math
import traceback
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Callable

import pandas as pd
try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()

from ..utils.logger import get_logger
from ..common.specs import MarketSpec
from ..market.base_integration import CryptoDataFetcher
from ..utils.timeframe import timeframe_to_minutes

logger = get_logger(__name__)


class DataWarmupManager:
    """
    Gerenciador de warm-up de dados históricos.

    YAA TASK-01: Refatorado para trabalhar com StrategyFactory em vez de estratégias pré-criadas.
    
    Responsável por:
    - Aceitar uma factory de estratégias que cria sob demanda
    - Identificar requisitos de dados usando estratégias criadas temporariamente
    - Coletar dados históricos suficientes antes da operação
    - Validar a integridade dos dados coletados
    - Armazenar dados em cache para uso pelas estratégias
    """

    # Flag de escopo de processo para indicar que o warm-up já foi realizado.
    _global_cache_populated: bool = False
    # Cache de classe para compartilhar dados entre instâncias.
    _global_historical_data_cache: Dict[MarketSpec, pd.DataFrame] = {}

    def __init__(
        self,
        symbols: List[str],
        timeframes: List[str],
        strategy_factory: Optional[Callable] = None,
        strategy_config: Optional[Dict[str, Any]] = None,
        market_integration: CryptoDataFetcher = None,
        force_warmup: bool = False,
        config: Optional[Dict[str, Any]] = None,
    ):
        """
        Inicializa o gerenciador de warm-up.

        Args:
            symbols: Lista de símbolos para coletar dados
            timeframes: Lista de timeframes necessários
            strategy_factory: Factory function para criar estratégias sob demanda
            strategy_config: Configuração das estratégias (nome, params, etc.)
            market_integration: Instância opcional de CryptoDataFetcher para conexão com exchange
            force_warmup: Se True, força o warm-up mesmo se já foi executado
            config: Dicionário de configurações adicionais
        """
        self.symbols = symbols
        self.timeframes = timeframes
        self.strategy_factory = strategy_factory
        self.strategy_config = strategy_config or {}
        self.market_integration = market_integration
        self.force_warmup = force_warmup

        if isinstance(config, str):
            try:
                with open(config, "r", encoding="utf-8") as f:
                    config = yaml.safe_load(f) or {}
            except Exception as exc:  # pragma: no cover - file issues
                logger.warning(f"Falha ao ler config YAML {config}: {exc}")
                config = {}

        self.config = config or {}

        self.cache_dir = Path(self.config.get("cache_dir", "data/warmup_cache"))
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        self.max_candles_per_fetch = int(self.config.get("max_candles_per_fetch", 1500))
        self.timeout = float(self.config.get("timeout", 30))
        self.incremental = bool(self.config.get("incremental", False))

        # Limite de reintentos ao coletar dados por par
        self.retry_limit = int(self.config.get("retry_limit", 3))

        # Cache de dados históricos
        # A instância agora usa o cache global compartilhado.
        self.historical_data_cache: Dict[MarketSpec, pd.DataFrame] = (
            DataWarmupManager._global_historical_data_cache
        )

        # Requisitos de dados por símbolo/timeframe
        self.data_requirements: Dict[MarketSpec, int] = {}

        # Status do warm-up
        self.warmup_completed = False
        self.warmup_errors: List[str] = []

        # Dados carregados
        self.loaded_data: Dict[MarketSpec, pd.DataFrame] = {}

        # Chaves de cache usadas
        self.cache_keys_used: List[str] = []

        # Métricas do processo
        self.metrics = {"duration": 0.0, "loaded_candles": 0}

        logger.info(
            f"📊 DataWarmupManager inicializado para {len(symbols)} símbolos "
            f"e {len(timeframes)} timeframes"
        )

    # ------------------------------------------------------------------
    # YAA: Função de parsing centralizada
    # ------------------------------------------------------------------
    def _parse_strategy_key(self, strategy_key: str) -> Optional[MarketSpec]:
        """
        Analisa uma chave de estratégia para extrair o símbolo e o timeframe.
        Ex: "QualiaTSVFStrategy_BTCUSDT_5m" -> MarketSpec(symbol="BTCUSDT", timeframe="5m")
        """
        try:
            parts = strategy_key.split("_")
            if len(parts) < 2:
                logger.warning(
                    f"Chave de estratégia '{strategy_key}' com formato inválido, ignorando."
                )
                return None

            # Tentativa 1: Timeframe é o último elemento
            timeframe_candidate = parts[-1]
            try:
                timeframe_to_minutes(timeframe_candidate)
                # Remove o prefixo "Qualia...Strategy" se houver
                symbol_parts = [p for p in parts[:-1] if "strategy" not in p.lower()]
                symbol = "".join(symbol_parts)
                return MarketSpec(symbol=symbol, timeframe=timeframe_candidate)
            except ValueError:
                pass  # Prossiga para a próxima tentativa

            # Tentativa 2: Timeframe é o penúltimo e o símbolo não contém "strategy"
            if len(parts) >= 2:
                timeframe_candidate = parts[-2]
                try:
                    timeframe_to_minutes(timeframe_candidate)
                    symbol_parts = [p for p in parts[:-2] if "strategy" not in p.lower()]
                    symbol = "".join(symbol_parts)
                    if symbol:  # Garante que o símbolo não seja vazio
                        return MarketSpec(symbol=symbol, timeframe=timeframe_candidate)
                except ValueError:
                    pass  # Falha final

            logger.warning(
                f"Não foi possível extrair um símbolo/timeframe válido da chave '{strategy_key}'"
            )
            return None
        except Exception as e:
            logger.error(
                f"Erro inesperado ao analisar a chave '{strategy_key}': {e}",
                exc_info=True,
            )
            return None

    # ------------------------------------------------------------------
    # Persistence helpers
    # ------------------------------------------------------------------
    def _cache_path(self, spec: MarketSpec) -> Path:
        symbol = spec.symbol.replace("/", "_")
        return self.cache_dir / f"{symbol}_{spec.timeframe}.csv"

    def _save_to_disk(self, spec: MarketSpec, df: pd.DataFrame) -> None:
        try:
            path = self._cache_path(spec)
            df.to_csv(path)
        except Exception as exc:  # pragma: no cover - disk issues
            logger.warning(f"Falha ao salvar cache {spec}: {exc}")

    def _load_from_disk(self, spec: MarketSpec) -> Optional[pd.DataFrame]:
        path = self._cache_path(spec)
        if path.exists():
            try:
                return pd.read_csv(path, index_col=0, parse_dates=True)
            except Exception as exc:  # pragma: no cover - disk issues
                logger.warning(f"Falha ao carregar cache {spec}: {exc}")
        return None

    def _create_temporary_strategy(self, symbol: str, timeframe: str, context: Dict[str, Any]) -> Any:
        """
        Cria uma estratégia temporária para análise de requisitos.
        
        YAA TASK-01: Esta é a função central que substitui a pré-inicialização.
        Usa a factory injetada para criar estratégias sob demanda com todas as dependências.
        
        YAA FIX-CRITICAL: Corrigido para injetar qualia_config e outras dependências críticas.
        """
        if not self.strategy_factory:
            logger.warning("StrategyFactory não fornecida, usando requisitos padrão")
            return None
            
        try:
            strategy_name = self.strategy_config.get("name", "NovaEstrategiaQUALIA")
            strategy_params = self.strategy_config.get("params", {})
            
            # YAA FIX-CRITICAL: Mescla contexto fornecido com informações da estratégia
            # Agora incluindo TODAS as dependências críticas para evitar erros de inicialização
            strategy_context = context.copy()
            strategy_context.update({
                "symbol": symbol,
                "timeframe": timeframe,
                # ✅ CRÍTICO: Garantir que qualia_config sempre esteja presente
                "qualia_config": context.get("qualia_config", {}),
                # ✅ CRÍTICO: Incluir risk_manager para controle de risco dinâmico
                "risk_manager": context.get("risk_manager"),
                "dynamic_risk_controller": context.get("dynamic_risk_controller"),
                # ✅ CRÍTICO: Perfil de risco para inicialização adequada
                "risk_profile": context.get("risk_profile", "moderate"),
                # ✅ Outras dependências importantes
                "market_integration": context.get("market_integration"),
                "qpm_instance": context.get("qpm_instance"),
                "holographic_universe": context.get("holographic_universe"),
                "consciousness": context.get("consciousness"),
                # ✅ Flag para indicar que está sendo usado para análise de requisitos apenas
                "temporary_strategy": True,
                "freeze_requirements": context.get("freeze_requirements", False),
            })
            
            # YAA FIX-CRITICAL: Log detalhado para debugging
            logger.debug(
                f"🔧 FIX-CRITICAL: Criando estratégia temporária para {symbol}@{timeframe} "
                f"com qualia_config={'✅' if strategy_context.get('qualia_config') else '❌'}, "
                f"risk_manager={'✅' if strategy_context.get('risk_manager') else '❌'}"
            )
            
            # Usa a factory para criar a estratégia com todas as dependências
            strategy = self.strategy_factory(
                alias=strategy_name,
                params=strategy_params,
                context=strategy_context,
            )
            
            logger.debug(f"✅ FIX-CRITICAL: Estratégia temporária criada com sucesso para {symbol}@{timeframe}")
            return strategy
            
        except Exception as e:
            logger.error(
                f"❌ FIX-CRITICAL: Erro criando estratégia temporária para {symbol}@{timeframe}: {e}",
                exc_info=True
            )
            return None

    def analyze_data_requirements(self, context: Optional[Dict[str, Any]] = None) -> Dict[MarketSpec, int]:
        """
        Analisa todas as estratégias para determinar os requisitos de dados.
        
        YAA TASK-01: Modificado para criar estratégias temporariamente sob demanda.

        Args:
            context: Contexto com dependências para injetar nas estratégias (risk_manager, etc.)

        Returns:
            Dicionário com requisitos de velas por MarketSpec (símbolo/timeframe).
        """
        logger.info("🔍 Analisando requisitos de dados das estratégias...")

        requirements: Dict[MarketSpec, int] = {}
        context = context or {}

        for symbol in self.symbols:
            for timeframe in self.timeframes:
                try:
                    # YAA TASK-01: Cria estratégia temporária com todas as dependências
                    strategy = self._create_temporary_strategy(symbol, timeframe, context)
                    if not strategy:
                        # Fallback para requisitos padrão
                        logger.warning(f"Usando requisitos padrão para {symbol}@{timeframe}")
                        spec = MarketSpec(symbol=symbol, timeframe=timeframe)
                        requirements[spec] = 200  # Valor padrão conservador
                        continue

                    spec = MarketSpec(symbol=symbol, timeframe=timeframe)

                    # Obter requisito de dados base da estratégia
                    base_required = 100  # Default
                    if hasattr(strategy, "required_initial_data_length"):
                        base_required = strategy.required_initial_data_length
                    elif hasattr(strategy, "_calculate_required_initial_data_length"):
                        base_required = strategy._calculate_required_initial_data_length()

                    # --- YAA: Lógica aprimorada para considerar resampling ---
                    final_required = base_required
                    if hasattr(strategy, "params_obj") and hasattr(
                        strategy.params_obj, "s3_resample_period"
                    ):
                        try:
                            base_tf_minutes = timeframe_to_minutes(timeframe)
                            s3_tf_minutes = timeframe_to_minutes(
                                strategy.params_obj.s3_resample_period
                            )
                            s3_window = strategy.params_obj.s3_tsvf_window

                            if base_tf_minutes > 0 and s3_tf_minutes > base_tf_minutes:
                                resample_factor = s3_tf_minutes / base_tf_minutes
                                # Requisito para S3 + margem de segurança de 20%
                                s3_required = math.ceil(
                                    resample_factor * (s3_window + 1) * 1.2
                                )
                                final_required = max(base_required, s3_required)
                                logger.debug(
                                    f"Estratégia {symbol}@{timeframe} com resampling: "
                                    f"Base Req: {base_required}, S3 Req: {s3_required}, "
                                    f"Final Req: {final_required}"
                                )
                        except Exception as e:
                            logger.warning(
                                f"Falha ao calcular requisito de resampling para {symbol}@{timeframe}: {e}"
                            )
                    # --- Fim da lógica aprimorada ---

                    # YAA TASK-02: Remover limitação artificial e implementar paginação inteligente
                    # A limitação anterior forçava estratégias a operar com dados insuficientes
                    # Agora o DataWarmupManager vai usar paginação para obter dados completos
                    if final_required > self.max_candles_per_fetch:
                        logger.info(
                            f"🔄 TASK-02: Estratégia {symbol}@{timeframe} requisita {final_required} candles. "
                            f"Paginação será usada (limite por chamada: {self.max_candles_per_fetch})"
                        )
                    else:
                        logger.debug(
                            f"✅ Estratégia {symbol}@{timeframe}: requisito de {final_required} candles "
                            f"pode ser atendido em uma única chamada à API"
                        )

                    # Manter o maior requisito para cada par símbolo/timeframe
                    if spec not in requirements or final_required > requirements[spec]:
                        requirements[spec] = final_required

                    logger.debug(
                        f"Estratégia {symbol}@{timeframe}: requisito final de {final_required} candles"
                    )

                except Exception as e:
                    logger.error(
                        f"❌ Erro analisando requisitos da estratégia {symbol}@{timeframe}: {e}"
                    )
                    # Fallback para requisito padrão em caso de erro
                    spec = MarketSpec(symbol=symbol, timeframe=timeframe)
                    requirements[spec] = 200

        self.data_requirements = requirements

        # YAA: Proteção contra falha se nenhuma estratégia for encontrada/válida
        if not requirements:
            logger.warning(
                "Nenhum requisito de dados de estratégia válido foi encontrado após a análise."
            )
            return requirements

        logger.info(
            f"✅ Análise concluída: {len(requirements)} pares símbolo/timeframe "
            f"com requisitos variando de {min(requirements.values())} a "
            f"{max(requirements.values())} candles"
        )

        return requirements

    async def perform_warmup(self) -> bool:
        """
        Executa o processo de warm-up coletando dados históricos em chunks para
        contornar os limites da API.
        """
        # Evita duplicação: se já foi concluído anteriormente e não é forçado.
        if DataWarmupManager._global_cache_populated and not self.force_warmup:
            logger.info("♻️ Warm-up global já concluído – reutilizando dados em cache.")
            self.warmup_completed = True
            # Garante que a instância atual reflita o estado do cache global
            self.loaded_data = self.historical_data_cache.copy()
            return True

        logger.info("🚀 Iniciando processo de warm-up de dados...")

        start_ts = asyncio.get_event_loop().time()

        if not self.data_requirements:
            self.analyze_data_requirements()

        if not self.data_requirements:
            logger.warning("⚠️ Nenhum requisito de dados identificado")
            self.warmup_completed = True
            return True

        # Verificar se temos integração com market
        if not self.market_integration:
            logger.error("❌ Sem integração com exchange para coletar dados")
            self.warmup_errors.append("Sem integração com exchange configurada")
            return False

        # YAA FIX: Inicializar variáveis de controle
        total_pairs = len(self.data_requirements)
        successful_pairs = 0

        for idx, (spec, required_candles) in enumerate(self.data_requirements.items()):
            all_chunks = []
            end_time = datetime.now(timezone.utc)
            candles_to_fetch = required_candles
            existing_df = None

            if self.incremental:
                existing_df = self._load_from_disk(spec)
                # YAA FIX: Validar DataFrame carregado antes de usar
                if existing_df is not None and not existing_df.empty:
                    # YAA: Garantir que o índice seja do tipo datetime
                    if not isinstance(existing_df.index, pd.DatetimeIndex):
                        try:
                            existing_df.index = pd.to_datetime(existing_df.index)
                        except Exception as e:
                            logger.warning(
                                f"Cache para {spec} tem índice inválido e será ignorado: {e}"
                            )
                            existing_df = None  # Invalida o cache
                    
                    if existing_df is not None:
                        candles_to_fetch = required_candles - len(existing_df)
                        end_time = existing_df.index[0]
                else:
                    existing_df = None # Garante que df inválido não seja usado
            
            if candles_to_fetch <= 0 and existing_df is not None:
                self.historical_data_cache[spec] = existing_df
                self.loaded_data[spec] = existing_df
                successful_pairs += 1
                self.metrics["loaded_candles"] += len(existing_df)
                logger.info(
                    f"✅ Cache existente para {spec} possui {len(existing_df)} velas."
                )
                continue

            retries = self.retry_limit
            chunk_number = 1
            total_chunks_estimated = max(1, math.ceil(candles_to_fetch / self.max_candles_per_fetch))
            
            # YAA TASK-02: Log de planejamento da paginação
            if candles_to_fetch > self.max_candles_per_fetch:
                logger.info(
                    f"📋 TASK-02: Planejamento de paginação para {spec.symbol}@{spec.timeframe}:"
                    f"\n  🎯 Requisito total: {candles_to_fetch} candles"
                    f"\n  📦 Limite por chunk: {self.max_candles_per_fetch} candles"
                    f"\n  🔢 Chunks estimados: {total_chunks_estimated}"
                )
            
            while candles_to_fetch > 0:
                chunk_size = min(candles_to_fetch, self.max_candles_per_fetch)
                
                # YAA TASK-02: Log detalhado do progresso
                logger.info(
                    f"📥 [{idx+1}/{total_pairs}] Chunk {chunk_number}/{total_chunks_estimated} "
                    f"para {spec.symbol}@{spec.timeframe}: "
                    f"Coletando {chunk_size} candles (restam {candles_to_fetch})..."
                )

                tf_minutes = timeframe_to_minutes(spec.timeframe)
                # YAA TASK-02: Margem de segurança otimizada baseada no tamanho do chunk
                safety_margin = 1.2 if chunk_size <= 500 else 1.15  # Menos margem para chunks grandes
                start_time = end_time - timedelta(
                    minutes=int(chunk_size * tf_minutes * safety_margin)
                )

                try:
                    chunk_data = await asyncio.wait_for(
                        self.market_integration.fetch_historical_data(
                            spec=spec,
                            start_date=start_time,
                            end_date=end_time,
                            use_cache=False,
                        ),
                        timeout=self.timeout,
                    )
                    
                    if chunk_data is not None and not chunk_data.empty:
                        all_chunks.append(chunk_data)
                        actual_candles_retrieved = len(chunk_data)
                        candles_to_fetch -= actual_candles_retrieved
                        end_time = pd.to_datetime(chunk_data.index[0])
                        retries = self.retry_limit  # Reset retries on success
                        
                        # YAA TASK-02: Log de sucesso do chunk
                        logger.info(
                            f"✅ Chunk {chunk_number} concluído: "
                            f"{actual_candles_retrieved} candles obtidos. "
                            f"Progresso: {len(all_chunks)}/{total_chunks_estimated} chunks"
                        )
                        
                        chunk_number += 1
                        
                        # YAA TASK-02: Pequeno delay entre chunks para respeitar rate limits
                        if candles_to_fetch > 0:  # Só faz delay se há mais chunks
                            await asyncio.sleep(0.5)
                    else:
                        # YAA TASK-02: Tratamento melhorado para chunks vazios
                        logger.warning(
                            f"⚠️ Chunk {chunk_number} para {spec} retornou vazio. "
                            f"Tentativas restantes: {retries-1}"
                        )
                        retries -= 1
                        if retries <= 0:
                            logger.error(
                                f"❌ TASK-02: Falha ao buscar chunk para {spec} após {self.retry_limit} tentativas. "
                                f"Chunks obtidos: {len(all_chunks)}/{total_chunks_estimated}"
                            )
                            break
                        await asyncio.sleep(2)  # Delay maior em caso de erro

                except asyncio.TimeoutError:
                    logger.warning(
                        f"⏰ TASK-02: Timeout no chunk {chunk_number} para {spec}. "
                        f"Tentativas restantes: {retries-1}"
                    )
                    retries -= 1
                    if retries <= 0:
                        logger.error(
                            f"❌ TASK-02: Timeout persistente para {spec} após {self.retry_limit} tentativas."
                        )
                        break
                    await asyncio.sleep(3)  # Delay maior para timeouts
                    
                except Exception as e:
                    logger.error(
                        f"❌ TASK-02: Erro no chunk {chunk_number} para {spec}: {e}", 
                        exc_info=True
                    )
                    retries -= 1
                    if retries <= 0:
                        logger.error(
                            f"❌ TASK-02: Falha definitiva para {spec} após {self.retry_limit} tentativas."
                        )
                        break
                    await asyncio.sleep(1)

            if all_chunks or existing_df is not None:
                # YAA TASK-02: Consolidação inteligente de dados paginados
                if all_chunks:
                    logger.info(
                        f"🔗 TASK-02: Consolidando {len(all_chunks)} chunks para {spec.symbol}@{spec.timeframe}..."
                    )
                    
                    # Verificar se os chunks têm sobreposição temporal
                    chunk_ranges = []
                    for i, chunk in enumerate(all_chunks):
                        start_ts = chunk.index[0]
                        end_ts = chunk.index[-1]
                        chunk_ranges.append((start_ts, end_ts, len(chunk)))
                        logger.debug(f"  Chunk {i+1}: {start_ts} a {end_ts} ({len(chunk)} candles)")
                    
                    # Concatenar e ordenar por timestamp
                    new_df = pd.concat(all_chunks).sort_index()
                    
                    # YAA TASK-02: Remover duplicatas mantendo a última entrada (mais recente)
                    initial_count = len(new_df)
                    new_df = new_df[~new_df.index.duplicated(keep="last")]
                    deduped_count = len(new_df)
                    
                    if initial_count != deduped_count:
                        logger.info(
                            f"🧹 TASK-02: Removidas {initial_count - deduped_count} duplicatas na consolidação"
                        )
                else:
                    new_df = pd.DataFrame()
                
                # YAA TASK-02: Combinar com dados existentes se houver cache
                if existing_df is not None:
                    logger.info(f"🔄 TASK-02: Combinando novos dados com cache existente ({len(existing_df)} candles)")
                    combined = pd.concat([new_df, existing_df]).sort_index()
                else:
                    combined = new_df
                    
                # YAA TASK-02: Remoção final de duplicatas
                combined = combined[~combined.index.duplicated(keep="last")]
                full_df = combined

                # YAA TASK-02: Validação de integridade dos dados
                integrity_metrics = self._validate_data_integrity(full_df, spec, required_candles)
                
                if len(full_df) >= required_candles:
                    self.historical_data_cache[spec] = full_df
                    self.loaded_data[spec] = full_df
                    self._save_to_disk(spec, full_df)
                    self.metrics["loaded_candles"] += len(full_df)
                    successful_pairs += 1
                    
                    logger.info(
                        f"✅ TASK-02: Coleta para {spec.symbol}@{spec.timeframe} concluída com SUCESSO!"
                        f"\n  📊 Dados coletados: {len(full_df)} candles (requisito: {required_candles})"
                        f"\n  📈 Período: {full_df.index[0]} a {full_df.index[-1]}"
                        f"\n  🎯 Taxa de sucesso: {(len(full_df)/required_candles)*100:.1f}%"
                        f"\n  🔍 Integridade: {integrity_metrics['quality_score']:.1f}%"
                    )
                else:
                    # YAA TASK-02: Log detalhado de falha
                    shortage = required_candles - len(full_df)
                    self.warmup_errors.append(
                        f"TASK-02: Dados insuficientes para {spec} - obtidos {len(full_df)}/{required_candles} "
                        f"(faltam {shortage} candles, {(shortage/required_candles)*100:.1f}% incompleto)"
                    )
                    
                    logger.warning(
                        f"⚠️ TASK-02: Dados INSUFICIENTES para {spec.symbol}@{spec.timeframe}"
                        f"\n  📊 Obtidos: {len(full_df)} candles"
                        f"\n  🎯 Requisito: {required_candles} candles"
                        f"\n  ❌ Faltam: {shortage} candles ({(shortage/required_candles)*100:.1f}%)"
                        f"\n  🔍 Integridade: {integrity_metrics['quality_score']:.1f}%"
                    )

        # Verificar resultado do warm-up
        self.warmup_completed = successful_pairs == total_pairs

        if self.warmup_completed:
            logger.info(
                f"🎉 Warm-up concluído com sucesso! "
                f"{successful_pairs}/{total_pairs} pares carregados"
            )
            # Marca flag global para evitar execuções subsequentes
            DataWarmupManager._global_cache_populated = True
        else:
            logger.warning(
                f"⚠️ Warm-up parcialmente concluído: "
                f"{successful_pairs}/{total_pairs} pares carregados"
            )
            for error in self.warmup_errors:
                logger.warning(f"  - {error}")

        self.metrics["duration"] = asyncio.get_event_loop().time() - start_ts
        return self.warmup_completed

    def get_cached_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """
        Retorna dados do cache para um símbolo/timeframe específico.

        Args:
            symbol: Símbolo desejado
            timeframe: Timeframe desejado

        Returns:
            DataFrame com dados ou None se não estiver no cache
        """
        spec = MarketSpec(symbol=symbol, timeframe=timeframe)
        df = self.historical_data_cache.get(spec)
        if df is None:
            df = self._load_from_disk(spec)
        return df

    def get_warmup_summary(self) -> Dict[str, Any]:
        """
        Retorna um resumo do status do warm-up.
        
        YAA TASK-02: Inclui métricas de qualidade de dados da paginação.

        Returns:
            Dicionário com informações sobre o warm-up
        """
        total_candles = sum(len(df) for df in self.historical_data_cache.values())
        total_pairs = len(self.data_requirements)
        loaded_pairs = len(self.loaded_data)
        
        # YAA TASK-02: Calcular métricas agregadas de qualidade
        quality_metrics = []
        total_gaps = 0
        total_missing_candles = 0
        
        for spec, df in self.loaded_data.items():
            required = self.data_requirements.get(spec, 0)
            if required > 0:
                integrity = self._validate_data_integrity(df, spec, required)
                quality_metrics.append(integrity["quality_score"])
                total_gaps += integrity["gaps_detected"]
                total_missing_candles += integrity["missing_candles"]
        
        avg_quality = sum(quality_metrics) / len(quality_metrics) if quality_metrics else 0.0
        
        # YAA TASK-02: Estatísticas de paginação
        paginated_pairs = sum(
            1 for spec, required in self.data_requirements.items() 
            if required > self.max_candles_per_fetch
        )
        
        summary = {
            "completed": self.warmup_completed,
            "partial_success": loaded_pairs > 0 and loaded_pairs < total_pairs,
            "total_pairs": total_pairs,
            "loaded_pairs": loaded_pairs,
            "total_candles": total_candles,
            "errors": self.warmup_errors,
            "cache_keys": list(self.historical_data_cache.keys()),
            "duration": self.metrics.get("duration", 0.0),
            "loaded_candles": self.metrics.get("loaded_candles", 0),
            
            # YAA TASK-02: Métricas de qualidade e paginação
            "task02_metrics": {
                "average_data_quality": round(avg_quality, 1),
                "total_gaps_detected": total_gaps,
                "total_missing_candles": total_missing_candles,
                "paginated_pairs": paginated_pairs,
                "max_candles_per_fetch": self.max_candles_per_fetch,
                "pagination_efficiency": round((loaded_pairs / max(1, paginated_pairs)) * 100, 1) if paginated_pairs > 0 else 100.0,
            },
        }
        
        return summary

    def inject_data_into_strategies(self, strategies: Optional[Dict[str, Any]] = None) -> None:
        """
        Injeta dados do cache nas estratégias correspondentes.
        
        YAA TASK-01: Modificado para aceitar estratégias criadas externamente.

        Args:
            strategies: Dicionário de estratégias onde injetar os dados.
                       Se None, usa self.strategies (compatibilidade retroativa).
        """
        if strategies is None:
            logger.warning("Nenhuma estratégia fornecida para injeção de dados")
            return
            
        logger.info("💉 Injetando dados pré-carregados nas estratégias...")

        injected_count = 0

        for strategy_key, strategy in strategies.items():
            try:
                spec = self._parse_strategy_key(strategy_key)
                if spec and spec in self.loaded_data:
                    data = self.loaded_data[spec]

                    # Injetar no shared_context da estratégia
                    if hasattr(strategy, "shared_context"):
                        if strategy.shared_context is None:
                            strategy.shared_context = {}

                        # Usar a chave esperada pela estratégia
                        preload_key = f"preloaded_candles_{spec.timeframe}"
                        strategy.shared_context[preload_key] = data

                        # YAA: Ativar proteção de integridade TSVF após warm-up bem-sucedido
                        strategy.shared_context["freeze_requirements"] = True
                        if hasattr(strategy, "_tsvf_integrity_protected"):
                            strategy._tsvf_integrity_protected = True
                            logger.info(
                                f"🔒 Proteção TSVF ativada para {strategy_key} "
                                f"(preservando integridade do canal retrocausal)"
                            )

                        injected_count += 1
                        logger.debug(
                            f"✅ Dados injetados em {strategy_key}: "
                            f"{len(data)} candles"
                        )
                elif spec:
                    logger.warning(
                        f"⚠️ Sem dados no cache para {strategy_key} ({spec})"
                    )

            except Exception as e:
                logger.error(f"❌ Erro injetando dados em {strategy_key}: {e}")

        logger.info(
            f"✅ Injeção concluída: {injected_count}/{len(strategies)} "
            f"estratégias receberam dados e proteção TSVF"
        )

    def _validate_data_integrity(self, df: pd.DataFrame, spec: MarketSpec, required_candles: int) -> Dict[str, float]:
        """
        Valida a integridade dos dados coletados.
        
        YAA TASK-02: Implementação robusta de validação de integridade.

        Args:
            df: DataFrame com dados coletados
            spec: Especificação do símbolo e timeframe
            required_candles: Número de velas necessárias

        Returns:
            Dicionário com métricas de qualidade dos dados
        """
        if df.empty:
            return {
                "quality_score": 0.0,
                "completeness": 0.0,
                "temporal_continuity": 0.0,
                "data_validity": 0.0,
                "missing_candles": required_candles,
                "gaps_detected": 0,
            }
            
        # Cálculo da completude
        completeness = min(100.0, (len(df) / required_candles) * 100)
        
        # Validação temporal: verifica gaps entre candles
        tf_minutes = timeframe_to_minutes(spec.timeframe)
        expected_interval = timedelta(minutes=tf_minutes)
        
        gaps_detected = 0
        missing_candles = 0
        
        if len(df) > 1:
            # Verificar continuidade temporal
            time_diffs = df.index.to_series().diff().dropna()
            expected_diff = expected_interval
            
            # Identificar gaps (diferenças maiores que o esperado + tolerância)
            tolerance = timedelta(minutes=tf_minutes * 0.1)  # 10% de tolerância
            large_gaps = time_diffs > (expected_diff + tolerance)
            gaps_detected = large_gaps.sum()
            
            # Estimar candles perdidos nos gaps
            if gaps_detected > 0:
                gap_durations = time_diffs[large_gaps]
                for gap_duration in gap_durations:
                    missing_in_gap = max(0, int(gap_duration.total_seconds() / (tf_minutes * 60)) - 1)
                    missing_candles += missing_in_gap
        
        # Calcular continuidade temporal
        if required_candles > 0:
            expected_total = required_candles + missing_candles
            temporal_continuity = max(0.0, ((expected_total - missing_candles) / expected_total) * 100)
        else:
            temporal_continuity = 100.0
            
        # Validação de colunas obrigatórias
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        missing_columns = [col for col in required_columns if col not in df.columns]
        data_validity = 100.0 if not missing_columns else 0.0
        
        # Validação de valores nulos/inválidos
        if not missing_columns:
            null_counts = df[required_columns].isnull().sum()
            total_values = len(df) * len(required_columns)
            valid_values = total_values - null_counts.sum()
            data_validity = (valid_values / total_values) * 100 if total_values > 0 else 0.0
            
            # Verificar se open <= high, low <= close, etc.
            invalid_ohlc = (
                (df['open'] > df['high']) | 
                (df['low'] > df['close']) |
                (df['high'] < df['low']) |
                (df['volume'] < 0)
            ).sum()
            
            if invalid_ohlc > 0:
                data_validity *= (1 - (invalid_ohlc / len(df)))
        
        # Score final de qualidade (média ponderada)
        quality_score = (
            completeness * 0.4 +           # 40% peso para completude
            temporal_continuity * 0.4 +    # 40% peso para continuidade
            data_validity * 0.2             # 20% peso para validade dos dados
        )
        
        metrics = {
            "quality_score": round(quality_score, 1),
            "completeness": round(completeness, 1),
            "temporal_continuity": round(temporal_continuity, 1), 
            "data_validity": round(data_validity, 1),
            "missing_candles": missing_candles,
            "gaps_detected": gaps_detected,
        }
        
        # Log detalhado se houver problemas
        if quality_score < 95.0:
            logger.warning(
                f"🔍 TASK-02: Qualidade dos dados para {spec.symbol}@{spec.timeframe}:"
                f"\n  📊 Score geral: {quality_score:.1f}%"
                f"\n  ✅ Completude: {completeness:.1f}%"
                f"\n  🔗 Continuidade: {temporal_continuity:.1f}%"
                f"\n  ✔️ Validade: {data_validity:.1f}%"
                f"\n  ❌ Gaps detectados: {gaps_detected}"
                f"\n  📉 Candles perdidos: {missing_candles}"
            )
        
        return metrics


async def perform_system_warmup(
    trading_system: Any,
    force_warmup: bool = False,
    config: Optional[Dict[str, Any]] = None,
) -> bool:
    """
    Função de conveniência para executar warm-up em um sistema de trading.
    
    YAA TASK-01: Modificado para trabalhar com StrategyFactory em vez de estratégias pré-criadas.

    Args:
        trading_system: Instância do QUALIATradingSystem
        force_warmup: Se True, força o warm-up mesmo se já foi executado
        config: Configurações adicionais ou caminho para YAML

    Returns:
        True se o warm-up foi bem-sucedido
    """
    logger.info("🔥 Iniciando warm-up do sistema de trading...")

    # Verificar se o sistema já tem warm-up manager
    if not hasattr(trading_system, "warmup_manager") or force_warmup:
        # Criar warmup manager com factory em vez de estratégias
        from ..strategies.strategy_factory import StrategyFactory
        
        strategy_factory_instance = StrategyFactory()
        strategy_config = trading_system.config.get("strategy_config", {})
        
        trading_system.warmup_manager = DataWarmupManager(
            symbols=trading_system.symbols,
            timeframes=trading_system.timeframes,
            strategy_factory=strategy_factory_instance.create_strategy,
            strategy_config=strategy_config,
            market_integration=getattr(trading_system, "market_integration", None),
            config=config,
        )

    # Contexto com dependências para injetar nas estratégias
    warmup_context = {
        "risk_manager": getattr(trading_system, "risk_manager", None),
    }

    # Analisar requisitos usando estratégias temporárias
    trading_system.warmup_manager.analyze_data_requirements(context=warmup_context)

    # Executar warm-up
    success = await trading_system.warmup_manager.perform_warmup()

    # Log do resumo
    summary = trading_system.warmup_manager.get_warmup_summary()
    logger.info(
        f"📊 Resumo do warm-up: "
        f"Pares carregados: {summary['loaded_pairs']}/{summary['total_pairs']}, "
        f"Total de candles: {summary['total_candles']}"
    )

    return success
