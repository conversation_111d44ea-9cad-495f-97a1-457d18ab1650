import numpy as np
from typing import List


def apply_cptp(rho: np.ndarray, kraus_ops: List[np.ndarray]) -> np.ndarray:
    """
    Aplica um canal CPTP (Completely Positive Trace-Preserving) a uma matriz de densidade rho,
    dada uma lista de operadores de Kraus.

    Args:
        rho (np.ndarray): A matriz de densidade do estado inicial.
        kraus_ops (List[np.ndarray]): Uma lista de operadores de Kraus {K_i} que definem o canal.
                                      O canal é trace-preserving se sum(K_i^dagger K_i) = I.

    Returns:
        np.ndarray: A matriz de densidade do estado final após a aplicação do canal.
    """
    if not isinstance(rho, np.ndarray):
        raise TypeError("A matriz de densidade 'rho' deve ser um numpy.ndarray.")
    if not rho.ndim == 2 or rho.shape[0] != rho.shape[1]:
        raise ValueError("A matriz de densidade 'rho' deve ser uma matriz quadrada.")
    if not kraus_ops:
        # Considerar se deve retornar rho ou levantar erro se não houver operadores.
        # Para um canal identidade, a lista conteria apenas [np.eye(rho.shape[0])].
        # Retornar rho pode ser perigoso se a intenção era aplicar um canal não trivial.
        raise ValueError("A lista 'kraus_ops' não pode estar vazia.")

    # Inicializa a nova matriz de densidade com zeros e tipo complexo
    new_rho = np.zeros_like(rho, dtype=np.complex128)

    # Validação da condição de trace-preserving (opcional, mas bom para debug)
    # I = np.eye(rho.shape[0], dtype=np.complex128)
    # sum_KdagK = np.zeros_like(I, dtype=np.complex128)

    for K in kraus_ops:
        if not isinstance(K, np.ndarray):
            raise TypeError(
                f"Cada operador de Kraus K deve ser um numpy.ndarray. Recebido: {type(K)}"
            )
        # Verifica se as dimensões de K são compatíveis para K @ rho @ K.conj().T
        if K.shape[1] != rho.shape[0] or K.shape[0] != rho.shape[0]:
            raise ValueError(
                f"Dimensões do operador de Kraus K ({K.shape}) são incompatíveis com rho ({rho.shape})."
            )

        new_rho += K @ rho @ K.conj().T
        # sum_KdagK += K.conj().T @ K # Para verificar trace-preserving

    # if not np.allclose(sum_KdagK, I):
    #     # logger.warning("O conjunto de operadores de Kraus fornecido pode não ser trace-preserving.")
    #     # Dependendo da criticidade, pode-se levantar um erro ou apenas logar.
    #     pass

    return new_rho


def get_amplitude_decay_kraus(gamma: float) -> List[np.ndarray]:
    """
    Retorna os operadores de Kraus para o canal de decaimento de amplitude de um único qubit.
    Este canal descreve a perda de energia de um qubit, e.g., |1> -> |0> com probabilidade gamma.

    Os operadores de Kraus são:
    K0 = [[1, 0], [0, sqrt(1-gamma)]]
    K1 = [[0, sqrt(gamma)], [0, 0]]

    Args:
        gamma (float): A probabilidade de decaimento. Deve estar no intervalo [0, 1].

    Returns:
        List[np.ndarray]: Uma lista contendo os dois operadores de Kraus, K0 e K1.
                         Cada operador é uma matriz numpy 2x2 com dtype complex128.

    Raises:
        ValueError: Se gamma não estiver entre 0 e 1.
    """
    if not (0 <= gamma <= 1):
        raise ValueError("O parâmetro gamma deve estar no intervalo [0, 1].")

    K0 = np.array([[1, 0], [0, np.sqrt(1 - gamma)]], dtype=np.complex128)

    K1 = np.array([[0, np.sqrt(gamma)], [0, 0]], dtype=np.complex128)

    return [K0, K1]


# Outros exemplos de canais podem ser adicionados aqui no futuro, como:
# def get_phase_damping_kraus(lambda_val: float) -> List[np.ndarray]:
#     """Canal de phase damping (dephasing)."""
#     K0 = np.array([[1, 0], [0, np.sqrt(1 - lambda_val)]], dtype=np.complex128)
#     K1 = np.array([[0, 0], [0, np.sqrt(lambda_val)]], dtype=np.complex128) # Erro comum, K1 aqui deveria ser [[0,0],[0,sqrt(lambda)]]
# Correto é: K1 = [[sqrt(lambda_val),0],[0,0]] ou [[0,0],[0,sqrt(lambda_val)]]
# para Z-dephasing
#     # Para Z-dephasing:
#     # K0 = sqrt(1-p) * I
#     # K1 = sqrt(p) * Z
#     # Se lambda_val é a probabilidade p:
#     # K0 = np.sqrt(1-lambda_val) * np.eye(2, dtype=np.complex128)
#     # K1 = np.sqrt(lambda_val) * np.array([[1,0],[0,-1]], dtype=np.complex128)
#     pass

# def get_depolarizing_kraus(p: float, num_qubits: int = 1) -> List[np.ndarray]:
#     """Canal de depolarização."""
#     # Para 1 qubit:
#     # K0 = sqrt(1-3p/4) * I
#     # K1 = sqrt(p/4) * X
#     # K2 = sqrt(p/4) * Y
#     # K3 = sqrt(p/4) * Z
#     pass
