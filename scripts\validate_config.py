#!/usr/bin/env python3
"""
Script para validar configuração YAML do sistema QUALIA
Verifica se o arquivo de configuração está correto antes de executar o sistema
"""

import sys
import yaml
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem

def validate_yaml_syntax(config_path: Path) -> dict:
    """Valida sintaxe YAML e carrega configuração"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        if not config or not isinstance(config, dict):
            raise ValueError("Arquivo YAML vazio ou formato inválido")
        
        print(f"✅ YAML SYNTAX: Arquivo {config_path} carregado com sucesso")
        return config
        
    except yaml.YAMLError as e:
        print(f"❌ YAML SYNTAX ERROR: {e}")
        raise
    except Exception as e:
        print(f"❌ FILE ERROR: {e}")
        raise

def validate_assets_section(config: dict):
    """Valida seção de ativos especificamente"""
    print("\n📊 VALIDANDO SEÇÃO DE ATIVOS...")
    print("-" * 50)
    
    try:
        QualiaBinanceCorrectedSystem.validate_assets_configuration(config)
        
        # Mostrar estatísticas detalhadas
        assets_config = config['assets']
        total_assets = 0
        
        for tier_name, tier_assets in assets_config.items():
            if isinstance(tier_assets, list):
                print(f"✅ {tier_name}: {len(tier_assets)} ativos")
                for asset in tier_assets[:3]:  # Mostrar primeiros 3
                    print(f"   - {asset}")
                if len(tier_assets) > 3:
                    print(f"   ... e mais {len(tier_assets) - 3} ativos")
                total_assets += len(tier_assets)
        
        print(f"\n✅ TOTAL: {total_assets} ativos únicos validados")
        
    except ValueError as e:
        print(f"❌ ASSETS VALIDATION ERROR: {e}")
        raise

def validate_required_sections(config: dict):
    """Valida todas as seções obrigatórias"""
    print("\n🔧 VALIDANDO SEÇÕES OBRIGATÓRIAS...")
    print("-" * 50)
    
    required_sections = [
        'trading', 'quantum_thresholds', 'risk_management',
        'assets', 'asset_correlations', 'asset_health',
        'performance_calibration'
    ]
    
    missing_sections = []
    for section in required_sections:
        if section in config:
            print(f"✅ {section}: Presente")
        else:
            print(f"❌ {section}: AUSENTE")
            missing_sections.append(section)
    
    if missing_sections:
        raise ValueError(f"Seções obrigatórias ausentes: {missing_sections}")

def validate_critical_values(config: dict):
    """Valida valores críticos da configuração"""
    print("\n⚙️ VALIDANDO VALORES CRÍTICOS...")
    print("-" * 50)
    
    # Validar quantum_thresholds
    qt = config.get('quantum_thresholds', {})
    critical_thresholds = ['consciousness', 'coherence', 'confidence', 'momentum_min', 'volume_surge_min']
    
    for threshold in critical_thresholds:
        if threshold in qt:
            value = qt[threshold]
            if isinstance(value, (int, float)) and 0 <= value <= 1:
                print(f"✅ quantum_thresholds.{threshold}: {value}")
            else:
                print(f"❌ quantum_thresholds.{threshold}: {value} (deve ser 0-1)")
        else:
            print(f"❌ quantum_thresholds.{threshold}: AUSENTE")
    
    # Validar trading parameters
    trading = config.get('trading', {})
    if 'profit_target_pct' in trading and 'stop_loss_pct' in trading:
        profit = trading['profit_target_pct']
        stop_loss = trading['stop_loss_pct']
        ratio = profit / stop_loss if stop_loss > 0 else 0
        print(f"✅ R/R Ratio: {ratio:.1f}:1 (Profit: {profit:.1%}, SL: {stop_loss:.1%})")
    
    # Validar intelligent_adaptation se presente
    ia = qt.get('intelligent_adaptation', {})
    if ia:
        enabled = ia.get('enabled', False)
        print(f"✅ Intelligent Adaptation: {'HABILITADO' if enabled else 'DESABILITADO'}")

def main():
    """Função principal de validação"""
    print("VALIDADOR DE CONFIGURAÇÃO QUALIA")
    print("=" * 60)
    
    config_path = Path('config/qualia_config.yaml')
    
    if not config_path.exists():
        print(f"❌ ERRO: Arquivo não encontrado: {config_path}")
        return 1
    
    try:
        # 1. Validar sintaxe YAML
        config = validate_yaml_syntax(config_path)
        
        # 2. Validar seções obrigatórias
        validate_required_sections(config)
        
        # 3. Validar seção de ativos (mais rigoroso)
        validate_assets_section(config)
        
        # 4. Validar valores críticos
        validate_critical_values(config)
        
        # 5. Resumo final
        print("\n" + "=" * 60)
        print("✅ VALIDAÇÃO COMPLETA: CONFIGURAÇÃO VÁLIDA!")
        print("=" * 60)
        print("O sistema pode ser iniciado com segurança.")
        print("Todas as validações passaram com sucesso.")
        
        # Mostrar informações do arquivo
        file_size = config_path.stat().st_size
        print(f"\nINFORMAÇÕES DO ARQUIVO:")
        print(f"  Caminho: {config_path.absolute()}")
        print(f"  Tamanho: {file_size:,} bytes")
        print(f"  Seções: {len(config)} seções principais")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ VALIDAÇÃO FALHOU: {e}")
        print("\n" + "=" * 60)
        print("❌ CONFIGURAÇÃO INVÁLIDA!")
        print("=" * 60)
        print("Corrija os erros acima antes de executar o sistema.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
