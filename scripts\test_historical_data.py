#!/usr/bin/env python3
"""
Script de Teste para Verificar Obtenção de Dados Históricos REAIS
Testa se o sistema consegue obter dados históricos da API Binance
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime, timedelta, timezone

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.geometric_metrics_calibrator import QualiaMetricsCalibrator
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def test_historical_data_fetch():
    """Testa a obtenção de dados históricos"""
    
    print("TESTE - Obtencao de Dados Historicos REAIS")
    print("=" * 60)

    try:
        # 1. Inicializar sistema
        logger.info("Inicializando sistema QUALIA...")
        trading_system = QualiaBinanceCorrectedSystem()

        # Inicializar conexão com Binance
        connected = await trading_system.initialize_binance_connection()
        if not connected:
            logger.warning("Nao foi possivel conectar com Binance, continuando em modo simulacao")

        # 2. Criar calibrador
        calibrator = QualiaMetricsCalibrator(trading_system)

        # 3. Testar obtenção de dados para alguns ativos
        test_symbols = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
        days_back = 7  # Testar com 7 dias primeiro

        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=days_back)

        print(f"Periodo de teste: {start_time.date()} a {end_time.date()}")
        print(f"Simbolos de teste: {test_symbols}")
        print("-" * 60)
        
        results = {}
        
        for symbol in test_symbols:
            print(f"\nTestando {symbol}...")

            try:
                # Testar obtenção de dados históricos
                df = await calibrator._fetch_historical_data(symbol, start_time, end_time)

                if df is not None and not df.empty:
                    results[symbol] = {
                        'success': True,
                        'candles': len(df),
                        'start_date': df.index[0],
                        'end_date': df.index[-1],
                        'timespan_hours': (df.index[-1] - df.index[0]).total_seconds() / 3600,
                        'sample_data': {
                            'first_close': float(df['close'].iloc[0]),
                            'last_close': float(df['close'].iloc[-1]),
                            'avg_volume': float(df['volume'].mean())
                        }
                    }

                    print(f"OK {symbol}: {len(df)} candles obtidos")
                    print(f"   Periodo: {df.index[0]} a {df.index[-1]}")
                    print(f"   Duracao: {results[symbol]['timespan_hours']:.1f} horas")
                    print(f"   Preco inicial: ${df['close'].iloc[0]:.2f}")
                    print(f"   Preco final: ${df['close'].iloc[-1]:.2f}")
                    print(f"   Volume medio: {df['volume'].mean():.0f}")

                else:
                    results[symbol] = {'success': False, 'error': 'DataFrame vazio ou None'}
                    print(f"ERRO {symbol}: Falha - DataFrame vazio ou None")

            except Exception as e:
                results[symbol] = {'success': False, 'error': str(e)}
                print(f"ERRO {symbol}: Erro - {e}")
        
        # 4. Resumo dos resultados
        print("\n" + "=" * 60)
        print("RESUMO DOS TESTES")
        print("=" * 60)

        successful = [s for s, r in results.items() if r['success']]
        failed = [s for s, r in results.items() if not r['success']]

        print(f"Sucessos: {len(successful)}/{len(test_symbols)}")
        print(f"Falhas: {len(failed)}/{len(test_symbols)}")

        if successful:
            print(f"\nATIVOS COM SUCESSO:")
            for symbol in successful:
                r = results[symbol]
                print(f"   {symbol}: {r['candles']} candles, {r['timespan_hours']:.1f}h")

        if failed:
            print(f"\nATIVOS COM FALHA:")
            for symbol in failed:
                r = results[symbol]
                print(f"   {symbol}: {r['error']}")
        
        # 5. Teste de calibração se houver dados
        if len(successful) >= 1:
            print(f"\nTESTE DE CALIBRACAO RAPIDA")
            print("-" * 40)

            test_symbol = successful[0]
            print(f"Testando calibracao para {test_symbol}...")

            try:
                # Testar calibração com dados reduzidos
                calibration_result = await calibrator._calibrate_single_asset(
                    symbol=test_symbol,
                    days_back=days_back,
                    profit_threshold=0.02,  # 2%
                    time_horizon_hours=4
                )

                print(f"OK Calibracao bem-sucedida!")
                print(f"   Pontos analisados: {calibration_result.total_points}")
                print(f"   Pontos lucrativos: {calibration_result.profitable_points}")
                print(f"   Taxa de sucesso: {calibration_result.success_rate:.1%}")

                if calibration_result.recommended_thresholds:
                    print(f"   Thresholds recomendados:")
                    for metric, value in calibration_result.recommended_thresholds.items():
                        print(f"      {metric}: {value:.3f}")

            except Exception as e:
                print(f"ERRO na calibracao: {e}")
        
        # 6. Verificações do sistema
        print(f"\nVERIFICACOES DO SISTEMA")
        print("-" * 40)

        print(f"Exchange conectada: {trading_system.exchange is not None}")
        if trading_system.exchange:
            print(f"Exchange ID: {getattr(trading_system.exchange, 'id', 'Unknown')}")

        print(f"Market integration: {hasattr(trading_system, 'market_integration')}")
        print(f"Ativos configurados: {len(getattr(trading_system, 'all_assets', []))}")

        return results

    except Exception as e:
        logger.error(f"Erro no teste: {e}")
        raise

    finally:
        # Cleanup (sistema nao tem metodo cleanup especifico)
        if 'trading_system' in locals():
            logger.info("Finalizando sistema...")

async def main():
    """Função principal"""
    try:
        results = await test_historical_data_fetch()
        
        # Salvar resultados
        import json
        from pathlib import Path
        
        results_dir = Path('data/tests')
        results_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = results_dir / f'historical_data_test_{timestamp}.json'
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)

        print(f"\nResultados salvos em: {results_file}")

    except Exception as e:
        print(f"\nErro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
