from __future__ import annotations

"""Advanced risk manager with drawdown and volatility limits."""

from collections import deque
from typing import Any, Deque, Dict, Optional, Tuple

from ..memory.event_bus import SimpleEventBus
from ..events import RiskUpdateEvent
from ..config.feature_flags import feature_toggle

from ..config.settings import get_env
from ..core.qualia_logger import log_event
from ..utils.logger import get_logger
from .risk_manager_base import (
    QUALIARiskManagerBase,
    CapitalUpdateResult,
    PositionSizingResult,
)

HISTORY_LIMIT = 1000
"""Fallback history length for internal deques."""

logger = get_logger(__name__)

try:
    HISTORY_LIMIT = int(
        get_env("QUALIA_RISK_HISTORY_LIMIT", str(HISTORY_LIMIT), warn=False)
        or HISTORY_LIMIT
    )
except ValueError:
    logger.warning("Invalid QUALIA_RISK_HISTORY_LIMIT; using default %s", HISTORY_LIMIT)


class AdvancedRiskManager(QUALIARiskManagerBase):
    """Risk manager implementing basic drawdown and volatility controls.

    The maximum length of ``position_history`` is defined by the
    ``QUALIA_RISK_HISTORY_LIMIT`` environment variable. When the variable is
    absent or invalid, the default value ``%s`` is used.
    """ % HISTORY_LIMIT

    def __init__(
        self,
        initial_capital: float,
        risk_profile: str = "balanced",
        risk_per_trade_pct: float = 1.0,
        max_position_size_pct: float = 10.0,
        max_drawdown_pct: float = 20.0,
        max_volatility: float = 5.0,
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        """Instantiate the advanced risk manager.

        Parameters
        ----------
        initial_capital
            Starting capital available for trading.
        risk_profile
            Qualitative risk appetite label.
        risk_per_trade_pct
            Percentage of capital allocated to each trade.
        max_position_size_pct
            Maximum fraction of capital allowed per position.
        max_drawdown_pct
            Stop trading when drawdown exceeds this percentage.
        max_volatility
            Maximum tolerated volatility for opening positions.
        """

        super().__init__(initial_capital, risk_profile, event_bus=event_bus)
        self.risk_per_trade_pct = risk_per_trade_pct
        self.max_position_size_pct = max_position_size_pct
        self.max_drawdown_pct = max_drawdown_pct
        self.max_volatility = max_volatility

        self.peak_capital = initial_capital
        self.current_drawdown_pct = 0.0
        self.position_history: Deque[Dict[str, Any]] = deque(maxlen=HISTORY_LIMIT)

    def _update_drawdown(self) -> None:
        """Recompute drawdown percentage based on capital evolution."""

        if self.peak_capital <= 0:
            logger.warning("Peak capital is non-positive; drawdown set to 0")
            self.current_drawdown_pct = 0.0
            return

        self.current_drawdown_pct = (
            (self.peak_capital - self.current_capital) / self.peak_capital
        ) * 100

    def calculate_dynamic_stop_loss(
        self,
        symbol: str,
        current_price: float,
        volatility: Optional[float],
        confidence: float,
    ) -> float:
        """Calcula stop loss baseado em volatilidade e confidence.

        Este método implementa um stop loss adaptativo que:
        - Usa volatilidade para determinar a distância base do stop
        - Ajusta baseado na confidence: maior confidence = stop mais próximo
        - Aplica limites mínimos e máximos para segurança

        Parameters
        ----------
        symbol
            Símbolo do ativo para logging
        current_price
            Preço atual do ativo
        volatility
            Volatilidade medida do ativo (pode ser None)
        confidence
            Nível de confiança da estratégia (0.0 a 1.0)

        Returns
        -------
        float
            Preço do stop loss calculado
        """
        if volatility is None or volatility == 0:
            volatility = 0.02  # 2% padrão
            logger.debug(f"Usando volatilidade padrão 2% para {symbol}")

        # Stop loss adaptativo: mais apertado com maior confidence
        base_stop_pct = volatility * 2.0
        confidence_factor = max(0.5, confidence)  # Mínimo 50%
        adjusted_stop_pct = base_stop_pct / confidence_factor

        # Aplicar limites de segurança
        min_stop_pct = 0.005  # Mínimo 0.5%
        max_stop_pct = 0.10  # Máximo 10%
        adjusted_stop_pct = max(min_stop_pct, min(max_stop_pct, adjusted_stop_pct))

        stop_loss_price = current_price * (1 - adjusted_stop_pct)

        logger.debug(
            f"Stop loss dinâmico para {symbol}: preço={current_price:.4f}, "
            f"volatilidade={volatility:.3f}, confidence={confidence:.3f}, "
            f"stop_pct={adjusted_stop_pct:.3f}, stop_price={stop_loss_price:.4f}"
        )

        return stop_loss_price

    def calculate_position_size(
        self,
        symbol: str,
        current_price: float,
        stop_loss_price: Optional[float],
        confidence: float = 0.5,
        volatility: Optional[float] = None,
        volume: Optional[float] = None,
        min_lot_size: Optional[float] = None,
        informational_mass: Optional[float] = None,
        initial_informational_mass: Optional[float] = None,
        lambda_factor: Optional[float] = None,
        *,
        trace_id: Optional[str] = None,
    ) -> PositionSizingResult:
        """Return sizing information for a potential trade.

        Parameters
        ----------
        symbol
            Trading pair identifier.
        current_price
            Latest price in quote currency.
        stop_loss_price
            Level used for risk calculation. If None, will calculate dynamic stop loss.
        confidence
            Strategy confidence score in ``[0,1]``.
        volatility
            Measured volatility of the asset.
        volume
            Observed trading volume for the period.
        min_lot_size
            Exchange lot size restriction.
        informational_mass
            Optional mass factor from the quantum layer.
        initial_informational_mass
            Baseline mass value for scaling.

        Returns
        -------
        Dict[str, Any]
            ``position_allowed`` flag plus sizing metrics.
        """

        if current_price <= 0:
            logger.warning(
                "Invalid current price %.2f for %s | confidence=%.2f volume=%s volatility=%s",
                current_price,
                symbol,
                confidence,
                volume if volume is not None else "n/a",
                volatility if volatility is not None else "n/a",
            )
            return {
                "position_allowed": False,
                "reason": "invalid_price",
                "position_size": 0.0,
                "quantity": 0.0,
                "risk_amount": 0.0,
            }

        if volatility is not None and volatility > self.max_volatility:
            logger.warning(
                "Volatility limit hit for %s: %.2f > %.2f | confidence=%.2f volume=%s",
                symbol,
                volatility,
                self.max_volatility,
                confidence,
                volume if volume is not None else "n/a",
            )
            return {
                "position_allowed": False,
                "reason": "volatility_limit",
                "position_size": 0.0,
                "quantity": 0.0,
                "risk_amount": 0.0,
            }

        if self.current_drawdown_pct > self.max_drawdown_pct:
            logger.warning(
                "Drawdown %.2f%% exceeds limit %.2f%% | confidence=%.2f volume=%s volatility=%s",
                self.current_drawdown_pct,
                self.max_drawdown_pct,
                confidence,
                volume if volume is not None else "n/a",
                volatility if volatility is not None else "n/a",
            )
            return {
                "position_allowed": False,
                "reason": "drawdown_limit",
                "position_size": 0.0,
                "quantity": 0.0,
                "risk_amount": 0.0,
            }

        # YAA: Usar stop loss dinâmico quando não fornecido
        if stop_loss_price is None:
            stop_loss_price = self.calculate_dynamic_stop_loss(
                symbol=symbol,
                current_price=current_price,
                volatility=volatility,
                confidence=confidence,
            )
            logger.info(
                f"Stop loss dinâmico calculado para {symbol}: {stop_loss_price:.4f}"
            )

        factor = lambda_factor if lambda_factor is not None else 1.0
        risk_amount = self.current_capital * (self.risk_per_trade_pct * factor / 100)
        stop_distance_pct = (
            abs((stop_loss_price - current_price) / current_price) * 100
            if stop_loss_price is not None
            else 1.0
        )
        if stop_distance_pct == 0:
            stop_distance_pct = 1.0
        position_size = risk_amount / (stop_distance_pct / 100)
        max_size = self.current_capital * (self.max_position_size_pct * factor / 100)
        if position_size > max_size:
            position_size = max_size
        quantity = position_size / current_price if current_price > 0 else 0
        if min_lot_size and quantity < min_lot_size:
            quantity = min_lot_size
            position_size = quantity * current_price
        self.position_history.append(
            {
                "symbol": symbol,
                "size": position_size,
                "quantity": quantity,
                "stop_loss_price": stop_loss_price,  # YAA: Armazenar stop loss para referência
                "confidence": confidence,
                "volatility": volatility,
            }
        )
        return {
            "position_allowed": True,
            "position_size": position_size,
            "quantity": quantity,
            "risk_amount": risk_amount,
            "stop_loss_price": stop_loss_price,  # YAA: Retornar stop loss calculado
        }

    def update_capital(self, new_capital: float) -> CapitalUpdateResult:
        """Record new capital and recalculate drawdown.

        Parameters
        ----------
        new_capital
            Latest account balance after trade settlement.

        Returns
        -------
        Dict[str, Any]
            Updated capital and drawdown metrics.
        """

        self.current_capital = new_capital
        if new_capital > self.peak_capital:
            logger.info("New peak capital %.2f", new_capital)
            self.peak_capital = new_capital
        self._update_drawdown()
        log_event(
            event_type="risk.update",
            payload={"new_capital": self.current_capital},
            source="risk_management",
            level="info",
        )
        if self.event_bus:
            self.event_bus.publish(
                "risk.update",
                RiskUpdateEvent(new_capital=self.current_capital),
            )
            from ..events import RiskRecalibratedEvent

            self.event_bus.publish(
                "risk.recalibrated",
                RiskRecalibratedEvent(
                    current_capital=self.current_capital,
                    drawdown_pct=self.current_drawdown_pct,
                ),
            )
            log_event(
                event_type="risk.recalibrated",
                payload={
                    "current_capital": self.current_capital,
                    "drawdown_pct": self.current_drawdown_pct,
                },
                source="risk_management",
            )
        return {
            "current_capital": self.current_capital,
            "drawdown_pct": self.current_drawdown_pct,
        }

    def can_open_new_position(
        self, current_positions: int, *, trace_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """Decide whether a new trade can be initiated."""

        if self.current_drawdown_pct > self.max_drawdown_pct:
            return False, "drawdown_limit"
        return True, "ok"

    def process_trade_result(self, trade_info: Dict[str, Any]) -> CapitalUpdateResult:
        """Update metrics after a trade closes."""

        pnl = trade_info.get("realized_pnl", 0.0)
        return self.update_capital(self.current_capital + pnl)
