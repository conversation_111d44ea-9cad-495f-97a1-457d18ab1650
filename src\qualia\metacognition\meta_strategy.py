"""Reinforcement learning policy for metacognitive parameter tuning."""

from __future__ import annotations

from typing import Any, Dict

import numpy as np

from ..memory import ExperienceReplay
from ..risk_management.risk_manager_base import QUALIARiskManagerBase
from ..utils.logger import get_logger

logger = get_logger(__name__)


class MetaStrategyRLAgent:
    """Tabular Q-learning policy for adjusting meta-strategy parameters.

    The agent learns to tune ``alpha`` and ``gamma`` (parameters of the TSVF
    transformation), the rolling window ``window`` and the relative weights of
    sub-strategies ``w_s1``, ``w_s2`` and ``w_s3``. The observation is a
    simplified ``eis`` metric and a reward signal provided by the caller.
    """

    def __init__(
        self,
        alpha: float = 0.3,
        gamma: float = 0.1,
        window: int = 10,
        weights: Dict[str, float] | None = None,
        *,
        replay_capacity: int = 500,
        learning_rate: float = 0.1,
        discount_factor: float = 0.95,
        epsilon: float = 0.1,
        risk_manager: QUALIARiskManagerBase | None = None,
    ) -> None:
        self.alpha = alpha
        self.gamma = gamma
        self.window = window
        self.weights = weights or {"s1": 1 / 3, "s2": 1 / 3, "s3": 1 / 3}

        self._lr = learning_rate
        self._discount = discount_factor
        self._epsilon = epsilon

        # Coleta num_symbols e num_timeframes do contexto se disponível
        # Esta é uma aproximação, idealmente viria da config principal
        context = getattr(risk_manager, "context", {})
        num_symbols = len(context.get("symbols", [1]))
        num_timeframes = len(context.get("timeframes", [1]))

        self._replay = ExperienceReplay(
            replay_capacity,
            risk_manager=risk_manager,
            num_symbols=num_symbols,
            num_timeframes=num_timeframes
        )

        self._state_bins = 10
        self._q_table: Dict[int, np.ndarray] = {}
        self._prev_state: int | None = None
        self._prev_action: int | None = None

        logger.info(
            "MetaStrategyRLAgent initialized: alpha=%s gamma=%s window=%s",
            alpha,
            gamma,
            window,
        )

    # Internal helpers -----------------------------------------------------
    def _discretize_state(self, eis: float) -> int:
        eis_clamped = max(0.0, min(1.0, eis))
        return int(round(eis_clamped * (self._state_bins - 1)))

    def _select_action(self, state: int) -> int:
        if np.random.random() < self._epsilon:
            action = np.random.randint(0, 9)
        else:
            q_values = self._q_table.setdefault(state, np.zeros(9))
            action = int(np.argmax(q_values))
        return action

    def _apply_action(self, action: int) -> None:
        if action == 0:
            self.alpha = min(1.0, self.alpha + 0.05)
        elif action == 1:
            self.alpha = max(0.0, self.alpha - 0.05)
        elif action == 2:
            self.gamma = min(1.0, self.gamma + 0.05)
        elif action == 3:
            self.gamma = max(0.0, self.gamma - 0.05)
        elif action == 4:
            self.window += 1
        elif action == 5:
            self.window = max(1, self.window - 1)
        elif action in (6, 7, 8):
            self._shift_weights(action - 6)

    def _shift_weights(self, index: int) -> None:
        keys = list(self.weights.keys())
        target = keys[index]
        self.weights[target] = min(1.0, self.weights[target] + 0.1)
        remain = 1.0 - self.weights[target]
        other_keys = [k for k in keys if k != target]
        share = remain / len(other_keys)
        for k in other_keys:
            self.weights[k] = share

    # Public API -----------------------------------------------------------
    def update(self, policy_inputs: Dict[str, float]) -> None:
        eis = float(policy_inputs.get("eis", 0.0))
        reward = float(policy_inputs.get("reward", 0.0))

        state = self._discretize_state(eis)

        if self._prev_state is not None and self._prev_action is not None:
            q_values = self._q_table.setdefault(self._prev_state, np.zeros(9))
            next_q = self._q_table.setdefault(state, np.zeros(9))
            td_target = reward + self._discount * np.max(next_q)
            q_values[self._prev_action] += self._lr * (
                td_target - q_values[self._prev_action]
            )
            self._replay.store((self._prev_state, self._prev_action, reward, state))

        action = self._select_action(state)
        self._apply_action(action)

        self._prev_state = state
        self._prev_action = action

    def get_params(self) -> Dict[str, Any]:
        """Return current parameter values."""
        return {
            "alpha": self.alpha,
            "gamma": self.gamma,
            "window": self.window,
            "weights": self.weights.copy(),
        }

    def update_params(self, context: Dict[str, float]) -> Dict[str, Any]:
        """Update policy and return new parameters."""
        self.update(context)
        return self.get_params()
