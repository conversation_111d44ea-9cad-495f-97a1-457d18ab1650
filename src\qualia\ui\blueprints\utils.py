"""Utility routes for the QUALIA Flask application."""

from __future__ import annotations

from datetime import datetime, timezone
from io import BytesIO
from flask import Blueprint, jsonify, render_template, request, send_file
import json
import numpy as np
import matplotlib.pyplot as plt

from ...utils.logger import get_logger
from ...ui.initialize import get_qualia_state, initialize_qualia_system

logger = get_logger(__name__)

utils_bp = Blueprint("utils", __name__)


@utils_bp.route("/")
def index() -> str:
    """Render the main interface page."""
    return render_template("index.html")


@utils_bp.route("/circuit")
def circuit_visualization_page() -> str:
    """Render the quantum circuit visualization page."""
    return render_template("circuit.html")


@utils_bp.route("/api/circuit/visualize")
def visualize_circuit():  # pragma: no cover - mostly visual output
    """Visualize the quantum circuit generated by ``QUALIAQuantumUniverse``."""
    steps = request.args.get("steps", type=int, default=3)
    output_type = request.args.get("output_type", default="mpl")
    logger.info(
        "Visualizando circuito com steps=%s, output_type=%s", steps, output_type
    )

    if output_type == "text":
        mock_circuit = """
            ┌───┐┌─────────┐┌───┐┌─────────┐┌───┐
q_0: ┤ H ├┤ RZ(π/4) ├┤ H ├┤ RZ(π/8) ├┤ H ├
            └───┘└─────────┘└───┘└─────────┘└───┘
            ┌───┐┌─────────┐┌───┐      ┌───┐┌───┐
q_1: ┤ H ├┤ RZ(π/4) ├┤ H ├──────┤ H ├┤ Z ├
            └───┘└─────────┘└───┘      └───┘└───┘
        """
        return jsonify(
            {
                "success": True,
                "circuit_visualization": mock_circuit,
                "format": "text",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )
    if output_type == "latex":
        mock_latex = r"\begin{array}{cc} \text{q_0} & \text{q_1} \\ \end{array}"
        return jsonify(
            {
                "success": True,
                "circuit_visualization": mock_latex,
                "format": "latex",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    fig, ax = plt.subplots(figsize=(10, 5))
    gates = ["H", "RZ", "H", "RZ", "H"]
    positions = [1, 2, 3, 4, 5]
    ax.plot([0, 6], [1, 1], "k-", linewidth=1.5)
    ax.plot([0, 6], [2, 2], "k-", linewidth=1.5)
    for i, gate in enumerate(gates):
        ax.add_patch(
            plt.Rectangle(
                (positions[i] - 0.3, 0.7), 0.6, 0.6, fill=True, color="skyblue"
            )
        )
        ax.text(positions[i], 1, gate, ha="center", va="center", fontsize=10)
    ax.add_patch(plt.Rectangle((1 - 0.3, 1.7), 0.6, 0.6, fill=True, color="lightgreen"))
    ax.text(1, 2, "H", ha="center", va="center", fontsize=10)
    ax.add_patch(plt.Rectangle((5 - 0.3, 1.7), 0.6, 0.6, fill=True, color="lightgreen"))
    ax.text(5, 2, "Z", ha="center", va="center", fontsize=10)
    ax.set_xlim(0, 6)
    ax.set_ylim(0, 3)
    ax.set_yticks([1, 2])
    ax.set_yticklabels(["q_0", "q_1"])
    ax.set_title(f"Mock Circuit Visualization (Steps: {steps})")
    ax.set_xticks([])
    ax.spines["top"].set_visible(False)
    ax.spines["right"].set_visible(False)
    ax.spines["bottom"].set_visible(False)
    img = BytesIO()
    fig.savefig(img, format="png", dpi=300, bbox_inches="tight")
    img.seek(0)
    plt.close(fig)
    return send_file(img, mimetype="image/png")


@utils_bp.route("/api/circuit/export_qasm3", methods=["GET", "POST"])
def export_qasm3_circuit():  # pragma: no cover - heavy IO
    """Export the quantum circuit in QASM 3.0 format."""
    if request.method == "GET":
        steps = request.args.get("steps", type=int, default=3)
        download = request.args.get("download", default="false").lower() == "true"
        filename = request.args.get("filename", default="qualia_circuit.qasm3")
    else:
        data = request.json or {}
        steps = data.get("steps", 3)
        download = data.get("download", False)
        filename = data.get("filename", "qualia_circuit.qasm3")

    logger.info("Exportando circuito QASM com steps=%s, download=%s", steps, download)

    qasm_content = f"""// QASM 3.0 export do circuito QUALIA (mock para teste)
// Circuito gerado para {steps} steps

OPENQASM 3.0;
include \"stdgates.inc\";

qubit[2] q;
bit[2] c;

h q[0];
h q[1];

rz(pi/4) q[0];
rz(pi/4) q[1];

h q[0];
h q[1];

rz(pi/8) q[0];
h q[0];

h q[1];
z q[1];
"""
    if download:
        import tempfile

        with tempfile.NamedTemporaryFile(delete=False, suffix=".qasm3") as tmp_file:
            temp_path = tmp_file.name
            tmp_file.write(qasm_content.encode("utf-8"))
        return send_file(
            temp_path, as_attachment=True, download_name=filename, mimetype="text/plain"
        )

    return jsonify(
        {
            "success": True,
            "qasm_content": qasm_content,
            "hardware_compatible": True,
            "compatibility_issues": {},
            "filename": filename,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    )


@utils_bp.route("/api/circuit/simulate", methods=["POST"])
def simulate_circuit():  # pragma: no cover - depends on qiskit
    """Simulate the quantum circuit using Qiskit Aer."""
    qualia_state = get_qualia_state()
    if qualia_state.universe is None:
        initialize_qualia_system(qualia_state)

    data = request.json or {}
    shots = data.get("shots", 1024)
    steps = data.get("steps")

    try:
        from qiskit_aer import AerSimulator
    except ImportError:
        try:
            from qiskit.providers.aer import AerSimulator
        except ImportError:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Qiskit Aer não está disponível para simulação",
                        "timestamp": datetime.now(timezone.utc).isoformat(),
                    }
                ),
                400,
            )

    build_kwargs = {
        "thermal": getattr(qualia_state.universe, "thermal_active_on_last_run", False),
        "temperature": getattr(qualia_state.universe, "temperature_on_last_run", 0.01),
        "retro_mode": getattr(qualia_state.universe, "retro_mode_on_last_run", "none"),
        "measure_frequency": getattr(
            qualia_state.universe, "measure_frequency_on_last_run", 1
        ),
        "retro_strength": getattr(
            qualia_state.universe,
            "retro_strength_on_last_run",
            getattr(qualia_state.universe, "retro_strength", 0.0),
        ),
    }
    build_kwargs.pop("scr_feedback_factor", None)
    build_kwargs = {k: v for k, v in build_kwargs.items() if v is not None}

    if steps is None:
        steps = getattr(qualia_state.universe, "_last_run_steps", None) or 5

    circuit = qualia_state.universe.build_circuit(steps=steps, **build_kwargs)
    simulator = AerSimulator()
    job = simulator.run(circuit, shots=shots)
    result = job.result()
    counts = result.get_counts()
    total_shots = sum(counts.values())
    probabilities = {k: v / total_shots for k, v in counts.items()}
    top_states = sorted(probabilities.items(), key=lambda x: x[1], reverse=True)[:10]

    return jsonify(
        {
            "success": True,
            "simulation_results": {
                "counts": counts,
                "top_states": top_states,
                "total_shots": total_shots,
                "simulator": str(simulator),
                "circuit_depth": circuit.depth(),
                "circuit_size": circuit.size(),
                "circuit_width": circuit.width(),
            },
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    )
