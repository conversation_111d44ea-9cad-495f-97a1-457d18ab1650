"""
Utilities Module

Provides utility functions and classes for logging, configuration,
and other common operations across the QUALIA system.
"""

from .logger import setup_logging

try:  # pragma: no cover - avoid circular import during configuration
    from .hardware_acceleration import (
        gpu_available,
        fpga_available,
        qpu_available,
        shannon_entropy,
        benchmark_entropy,
    )
except Exception:  # pragma: no cover - fallback when dependencies missing
    gpu_available = fpga_available = qpu_available = lambda *_, **__: False

    def shannon_entropy(*_, **__):
        return 0.0

    def benchmark_entropy(*_, **__):
        return 0.0


from .trading_calculations import parse_reduction_pct
from .metric_sanitizer import sanitize_metrics
from .text_sanitizer import sanitize_text
from .config import load_config
from .feature_flags import feature_toggle, clear_feature_cache
from .validation import validate_positive_float, validate_non_negative_int
from .coherence_collapse import collapse_by_coherence
from .timeframe import (
    timeframe_to_minutes,
    timeframe_to_pandas_freq,
    timeframe_to_milliseconds,
    safe_timeframe_to_minutes,
)

__all__ = [
    "setup_logging",
    "load_config",
    "parse_reduction_pct",
    "sanitize_metrics",
    "sanitize_text",
    "feature_toggle",
    "clear_feature_cache",
    "validate_positive_float",
    "validate_non_negative_int",
    "gpu_available",
    "fpga_available",
    "qpu_available",
    "shannon_entropy",
    "benchmark_entropy",
    "timeframe_to_minutes",
    "timeframe_to_pandas_freq",
    "timeframe_to_milliseconds",
    "safe_timeframe_to_minutes",
    "collapse_by_coherence",
]
