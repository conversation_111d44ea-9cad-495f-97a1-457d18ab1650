from __future__ import annotations

from typing import Any, Dict
import pandas as pd

from ...utils.logger import get_logger
from ..strategy_utils import make_signal_df

logger = get_logger(__name__)


def generate_signals(strategy: Any, analysis_result: Dict[str, Any]) -> pd.DataFrame:
    """Generate trading signals based on analysis results."""
    if not getattr(strategy, "initialized", False):
        raise ValueError("Estratégia não inicializada. Chame initialize() primeiro.")

    market_regime = analysis_result["market_regime"]
    directional_strength = analysis_result["directional_strength"]
    entropy = analysis_result["quantum_entropy"]
    trend_ema = analysis_result["trend_ema"]
    last_price = analysis_result["last_price"]
    timestamp = analysis_result["timestamp"]

    vol_short = analysis_result["vol_short"]
    vol_medium = analysis_result["vol_medium"]

    if not strategy._apply_volatility_filters(analysis_result):
        return pd.DataFrame()

    classic_score = strategy._score_classic_indicators(analysis_result)
    quantum_score = strategy._score_quantum_metrics(analysis_result)

    divergence = abs(classic_score - quantum_score) > 0.5
    if strategy.divergence_filter and divergence:
        logger.debug(
            "Divergência detectada: clássico=%.2f, quântico=%.2f",
            classic_score,
            quantum_score,
        )
        return pd.DataFrame()

    adjusted_quantum_weight = strategy.quantum_weight * (1 + entropy * 0.2)
    adjusted_quantum_weight = min(0.7, max(0.3, adjusted_quantum_weight))

    combined_score = (
        classic_score * (1 - adjusted_quantum_weight)
        + quantum_score * adjusted_quantum_weight
    )

    adjusted_threshold = strategy.signal_threshold
    if directional_strength > 0.3:
        adjusted_threshold *= 0.9
    else:
        adjusted_threshold *= 1.1
    adjusted_threshold = min(0.85, max(0.6, adjusted_threshold))

    signal = None
    confidence = abs(combined_score)

    if confidence > adjusted_threshold:
        if market_regime == "uptrend" and combined_score > 0:
            signal = "buy"
        elif market_regime == "downtrend" and combined_score < 0:
            signal = "sell"
        elif market_regime == "range" and confidence > adjusted_threshold * 1.2:
            signal = "buy" if combined_score > 0 else "sell"

    if signal:
        logger.info(
            "Sinal gerado: %s com confiança %.4f (regime: %s)",
            signal,
            confidence,
            market_regime,
        )
        base_stop_pct = max(0.01, min(0.03, vol_short * 1.5))
        if vol_medium > vol_short:
            base_stop_pct *= 1.2
        if strategy.adaptive_risk:
            if market_regime in {"uptrend", "downtrend"}:
                rr_ratio = strategy._take_profit_r_multiple_value
            else:
                rr_ratio = strategy._take_profit_r_multiple_value
        else:
            rr_ratio = strategy._take_profit_r_multiple_value

        stop_pct = base_stop_pct * strategy._stop_loss_r_multiple_value
        tp_pct = stop_pct * rr_ratio
        stop_loss = (
            last_price * (1 - stop_pct)
            if signal == "buy"
            else last_price * (1 + stop_pct)
        )
        take_profit = (
            last_price * (1 + tp_pct) if signal == "buy" else last_price * (1 - tp_pct)
        )

        signals_df = make_signal_df(
            signal,
            confidence,
            price=last_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            volatility=vol_short,
            classic_score=classic_score,
            quantum_score=quantum_score,
            market_regime=market_regime,
            risk_reward_ratio=rr_ratio,
            directional_strength=directional_strength,
            entropy=entropy,
        )
        signals_df.index = pd.Index([timestamp], name="timestamp")
        strategy.historical_signals.append(
            {
                "timestamp": timestamp,
                "signal": signal,
                "price": last_price,
                "confidence": confidence,
                "market_regime": market_regime,
            }
        )
        if len(strategy.historical_signals) > 100:
            strategy.historical_signals = strategy.historical_signals[-100:]
        return signals_df

    return pd.DataFrame()
