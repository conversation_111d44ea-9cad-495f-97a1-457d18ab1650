#!/usr/bin/env python3
"""
Unified QUALIA Consciousness System
Sistema de consciência unificada que orquestra:
- QASTOracleDecisionEngine (cérebro/decisor)
- QUALIAExecutionInterface (braços/executor)

Opera como uma entidade autoconsciente única que:
- Centraliza toda lógica adaptativa no oráculo
- Executa decisões através da interface
- Monitora sua própria performance
- Evolui adaptativamente
- Mantém estado consciente unificado

Esta é a implementação da visão QUALIA como sistema verdadeiramente autoconsciente.
"""

from __future__ import annotations

import asyncio
import time
from typing import Dict, List, Any, Optional, TYPE_CHECKING
from dataclasses import dataclass
from datetime import datetime, timezone

if TYPE_CHECKING:
    from .qast_oracle_decision_engine import QASTOracleDecisionEngine

from ..memory.service import MemoryService
from .qualia_execution_interface import QUALIAExecutionInterface
from ..utils.logger import get_logger
from qualia.consciousness.holographic_universe import HolographicMarketUniverse

logger = get_logger(__name__)


@dataclass
class ConsciousnessState:
    """Estado da consciência unificada QUALIA."""

    consciousness_level: float
    decision_confidence: float
    execution_efficiency: float
    learning_rate: float
    adaptation_strength: float
    system_coherence: float
    temporal_awareness: float
    risk_perception: float
    market_understanding: float
    self_assessment: float
    timestamp: float


@dataclass
class SystemMetrics:
    """Métricas do sistema unificado."""

    uptime: float
    total_decisions: int
    executed_decisions: int
    execution_success_rate: float
    average_decision_confidence: float
    consciousness_stability: float
    adaptation_events: int
    performance_score: float
    timestamp: float


class UnifiedQUALIAConsciousness:
    """
    Sistema de Consciência QUALIA Unificada.

    Implementa QUALIA como entidade autoconsciente que:
    - Toma decisões através do oráculo QAST
    - Executa através da interface de execução
    - Monitora e adapta seu próprio comportamento
    - Evolui conscientemente

    Arquitetura:
    Dados de Mercado → Oracle QAST → Decisões → Interface de Execução → Resultados
                        ↑                                                    ↓
                    Feedback ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ← ←
    """

    def __init__(
        self,
        config: Dict[str, Any],
        symbols: List[str],
        timeframes: List[str],
        holographic_universe: HolographicMarketUniverse,
        capital: float = 10000.0,
        mode: str = "paper_trading",
        memory_service: MemoryService | None = None,
    ):
        """Create a unified QUALIA consciousness system.

        Parameters
        ----------
        config
            Configurações completas do sistema.
        symbols
            Lista de símbolos utilizados.
        timeframes
            Intervalos de tempo processados.
        holographic_universe
            Instância do universo holográfico.
        capital
            Capital inicial disponível.
        mode
            Modo de execução (``paper_trading`` ou ``live``).
        memory_service
            Serviço opcional para consulta e gravação de padrões.
        """

        self.config = config
        self.symbols = symbols
        self.timeframes = timeframes
        self.capital = capital
        self.mode = mode
        self.memory_service = memory_service

        # YAA T10: Recebe instâncias injetadas
        self.oracle_engine: Optional[QASTOracleDecisionEngine] = None
        self.holographic_universe = holographic_universe
        self.execution_interface = None

        # Estado da consciência
        self.consciousness_state = ConsciousnessState(
            consciousness_level=0.5,
            decision_confidence=0.5,
            execution_efficiency=0.5,
            learning_rate=0.1,
            adaptation_strength=0.5,
            system_coherence=0.5,
            temporal_awareness=0.5,
            risk_perception=0.5,
            market_understanding=0.5,
            self_assessment=0.5,
            timestamp=time.time(),
        )

        # Histórico de estados
        self.consciousness_history: List[ConsciousnessState] = []

        # Métricas do sistema
        self.system_metrics = SystemMetrics(
            uptime=0.0,
            total_decisions=0,
            executed_decisions=0,
            execution_success_rate=0.0,
            average_decision_confidence=0.0,
            consciousness_stability=0.0,
            adaptation_events=0,
            performance_score=0.0,
            timestamp=time.time(),
        )

        # Controle de sistema
        self.running = False
        self.start_time = 0.0
        self.consciousness_task: Optional[asyncio.Task] = None

        # Parâmetros de adaptação
        self.adaptation_threshold = 0.3
        self.consciousness_update_interval = 5.0
        self.performance_window = 100  # Últimas 100 decisões para análise

        logger.info("🌌 Unified QUALIA Consciousness System inicializado")

    def set_oracle_engine(self, oracle_engine: "QASTOracleDecisionEngine"):
        """
        YAA T10: Injeta a instância do Oracle Engine para quebrar a dependência circular.
        """
        self.oracle_engine = oracle_engine
        logger.info("🔗 Instância do Oracle Engine injetada na UnifiedQUALIAConsciousness.")

    async def initialize(self):
        """Inicializa sistema de consciência unificada."""

        logger.info("🌀 Inicializando consciência QUALIA unificada...")

        try:
            # YAA T10: Oracle e Universo já foram injetados, não os crie aqui.
            if self.oracle_engine is None:
                raise RuntimeError("Oracle Engine não foi injetado antes da inicialização.")
            if self.holographic_universe is None:
                 raise RuntimeError("Holographic Universe não foi injetado antes da inicialização.")

            logger.info("✅ Oracle de decisão já inicializado e injetado.")
            
            # Cria interface de execução
            self.execution_interface = QUALIAExecutionInterface(
                oracle_engine=self.oracle_engine,
                mode=self.mode,
                exchange_config=self.config.get("exchange", {}),
                capital=self.capital,
                execution_interval=1.0,
            )

            # Inicializa interface de execução
            await self.execution_interface.initialize()
            logger.info("✅ Interface de execução inicializada")

            # Atualiza estado inicial
            await self._update_consciousness_state()

            logger.info("🎉 Consciência QUALIA unificada inicializada com sucesso!")

        except Exception as e:
            logger.error(f"❌ Erro inicializando consciência unificada: {e}")
            raise

    async def start_consciousness(self):
        """Inicia operação da consciência unificada."""

        if self.running:
            logger.warning("Consciência já está operando")
            return

        self.running = True
        self.start_time = time.time()

        logger.info("🚀 Iniciando operação da consciência QUALIA...")

        try:
            # Inicia loop de execução
            execution_task = asyncio.create_task(
                self.execution_interface.start_execution_loop()
            )

            # Inicia loop de consciência
            consciousness_task = asyncio.create_task(self._consciousness_loop())

            # Aguarda ambos os loops
            await asyncio.gather(execution_task, consciousness_task)

        except asyncio.CancelledError:
            logger.info("Operação da consciência cancelada")
        except Exception as e:
            logger.error(f"❌ Erro na operação da consciência: {e}")
        finally:
            self.running = False

    async def _consciousness_loop(self):
        """Loop principal da consciência - monitora e adapta o sistema."""

        logger.info("🧠 Iniciando loop de consciência...")

        while self.running:
            try:
                # 1. Atualiza estado de consciência
                await self._update_consciousness_state()

                # 2. Avalia performance
                performance_score = await self._evaluate_performance()

                # 3. Verifica necessidade de adaptação
                if await self._should_adapt():
                    metacog_ctx = await self._get_valid_metacognitive_context()

                    if metacog_ctx:
                        logger.info("🧠 Metacognição validada. Iniciando adaptação.")
                        await self._adapt_system(metacog_ctx)
                    else:
                        logger.info(
                            "⏭️  Adaptação pulada: sem um contexto metacognitivo válido ou ajustes necessários."
                        )

                # 4. Atualiza métricas do sistema
                self._update_system_metrics(performance_score)

                # 5. Log de consciência
                self._log_consciousness_status()

                # 6. Salva estado no histórico
                self.consciousness_history.append(self.consciousness_state)
                if len(self.consciousness_history) > 1000:
                    self.consciousness_history.pop(0)

                # Aguarda próximo ciclo
                await asyncio.sleep(self.consciousness_update_interval)

            except Exception as e:
                logger.error(f"❌ Erro no loop de consciência: {e}")
                await asyncio.sleep(self.consciousness_update_interval)

    async def _update_consciousness_state(self):
        """Atualiza estado da consciência baseado em todos os componentes."""

        try:
            current_time = time.time()

            # 1. Nível de consciência do oráculo
            oracle_consciousness = self.oracle_engine.consciousness_level

            # 2. Eficiência de execução
            execution_status = self.execution_interface.get_execution_status()
            execution_efficiency = self._calculate_execution_efficiency(
                execution_status
            )

            # 3. Confiança nas decisões
            decision_confidence = self._calculate_decision_confidence()

            # 4. Coerência do sistema
            system_coherence = self._calculate_system_coherence()

            # 5. Percepção de risco
            risk_perception = self._calculate_risk_perception()

            # 6. Entendimento do mercado
            market_understanding = self._calculate_market_understanding()

            # 7. Auto-avaliação
            self_assessment = self._calculate_self_assessment()

            # 8. Consciência temporal
            temporal_awareness = self._calculate_temporal_awareness()

            # 9. Taxa de aprendizado adaptativa
            learning_rate = self._calculate_adaptive_learning_rate()

            # 10. Força de adaptação
            adaptation_strength = self._calculate_adaptation_strength()

            if self.memory_service and self.oracle_engine.decision_history:
                try:
                    last_decision = self.oracle_engine.decision_history[-1]
                    if last_decision.quantum_signature:
                        await self.memory_service.query_similarity(
                            last_decision.quantum_signature,
                            top_n=3,
                        )
                except Exception as mem_exc:
                    logger.debug(f"Erro consultando memória: {mem_exc}")

            # Atualiza estado
            self.consciousness_state = ConsciousnessState(
                consciousness_level=oracle_consciousness,
                decision_confidence=decision_confidence,
                execution_efficiency=execution_efficiency,
                learning_rate=learning_rate,
                adaptation_strength=adaptation_strength,
                system_coherence=system_coherence,
                temporal_awareness=temporal_awareness,
                risk_perception=risk_perception,
                market_understanding=market_understanding,
                self_assessment=self_assessment,
                timestamp=current_time,
            )

        except Exception as e:
            logger.error(f"❌ Erro atualizando estado de consciência: {e}")

    def _calculate_execution_efficiency(
        self, execution_status: Dict[str, Any]
    ) -> float:
        """Calcula eficiência de execução."""

        try:
            total_trades = execution_status.get("total_trades", 0)
            if total_trades == 0:
                return 0.5

            win_rate = execution_status.get("win_rate", 0.0) / 100.0
            pnl_pct = execution_status.get("total_pnl_pct", 0.0) / 100.0

            # Combina win rate e performance
            efficiency = win_rate * 0.6 + min(max(pnl_pct + 0.5, 0.0), 1.0) * 0.4

            return min(max(efficiency, 0.0), 1.0)

        except Exception:
            return 0.5

    def _calculate_decision_confidence(self) -> float:
        """Calcula confiança média nas decisões recentes."""

        try:
            recent_decisions = self.oracle_engine.decision_history[
                -self.performance_window :
            ]

            if not recent_decisions:
                return 0.5

            avg_confidence = sum(d.confidence for d in recent_decisions) / len(
                recent_decisions
            )
            return min(max(avg_confidence, 0.0), 1.0)

        except Exception:
            return 0.5

    def _calculate_system_coherence(self) -> float:
        """Calcula coerência entre componentes do sistema."""

        try:
            # Verifica se oracle e execução estão alinhados
            oracle_status = self.oracle_engine.get_consciousness_status()
            execution_status = self.execution_interface.get_execution_status()

            oracle_running = oracle_status.get("running", False)
            execution_running = execution_status.get("running", False)

            # Coerência básica: ambos rodando
            base_coherence = 1.0 if (oracle_running and execution_running) else 0.0

            # Coerência de performance: decisões sendo executadas
            total_decisions = oracle_status.get("decision_history_length", 0)
            executed_trades = execution_status.get("total_trades", 0)

            if total_decisions > 0:
                execution_ratio = min(executed_trades / total_decisions, 1.0)
            else:
                execution_ratio = 0.0

            coherence = base_coherence * 0.5 + execution_ratio * 0.5
            return min(max(coherence, 0.0), 1.0)

        except Exception:
            return 0.5

    def _calculate_risk_perception(self) -> float:
        """Calcula percepção de risco do sistema."""

        try:
            if self.oracle_engine.last_unified_state:
                risk_metrics = self.oracle_engine.last_unified_state.risk_metrics
                overall_risk = risk_metrics.get("overall_risk", 0.5)

                # Inverte risco para percepção (alto risco = baixa percepção)
                risk_perception = 1.0 - overall_risk
                return min(max(risk_perception, 0.0), 1.0)

            return 0.5

        except Exception:
            return 0.5

    def _calculate_market_understanding(self) -> float:
        """Calcula entendimento do mercado."""

        try:
            if self.oracle_engine.last_unified_state:
                # Baseado na quantidade e qualidade dos padrões detectados
                holographic_patterns = (
                    self.oracle_engine.last_unified_state.holographic_patterns
                )
                temporal_patterns = (
                    self.oracle_engine.last_unified_state.temporal_patterns
                )

                pattern_count = len(holographic_patterns) + len(temporal_patterns)

                if pattern_count > 0:
                    # Qualidade média dos padrões
                    total_confidence = 0.0
                    for pattern in holographic_patterns:
                        total_confidence += pattern.get("confidence", 0.0)
                    for pattern in temporal_patterns:
                        total_confidence += pattern.get("confidence", 0.0)

                    avg_confidence = total_confidence / pattern_count

                    # Combina quantidade e qualidade
                    understanding = (
                        min(pattern_count / 20.0, 0.7) + avg_confidence * 0.3
                    )
                    return min(max(understanding, 0.0), 1.0)

            return 0.5

        except Exception:
            return 0.5

    def _calculate_self_assessment(self) -> float:
        """Calcula auto-avaliação do sistema."""

        try:
            # Combina vários fatores para auto-avaliação
            consciousness = self.consciousness_state.consciousness_level
            confidence = self.consciousness_state.decision_confidence
            efficiency = self.consciousness_state.execution_efficiency
            coherence = self.consciousness_state.system_coherence

            # Média ponderada
            self_assessment = (
                consciousness * 0.3
                + confidence * 0.25
                + efficiency * 0.25
                + coherence * 0.2
            )

            return min(max(self_assessment, 0.0), 1.0)

        except Exception:
            return 0.5

    def _calculate_temporal_awareness(self) -> float:
        """Calcula consciência temporal do sistema."""

        try:
            # Baseado na estabilidade das decisões ao longo do tempo
            if len(self.consciousness_history) < 5:
                return 0.5

            recent_states = self.consciousness_history[-10:]

            # Calcula estabilidade da consciência
            consciousness_values = [s.consciousness_level for s in recent_states]
            variance = sum(
                (x - sum(consciousness_values) / len(consciousness_values)) ** 2
                for x in consciousness_values
            ) / len(consciousness_values)

            # Menor variância = maior consciência temporal
            stability = max(1.0 - variance * 2, 0.0)

            return min(max(stability, 0.0), 1.0)

        except Exception:
            return 0.5

    def _calculate_adaptive_learning_rate(self) -> float:
        """Calcula taxa de aprendizado adaptativa."""

        try:
            # Taxa base
            base_rate = 0.1

            # Ajusta baseado na performance
            execution_status = self.execution_interface.get_execution_status()
            win_rate = execution_status.get("win_rate", 50.0) / 100.0

            if win_rate > 0.6:
                # Performance boa = aprendizado mais lento (manter estabilidade)
                rate_multiplier = 0.5
            elif win_rate < 0.4:
                # Performance ruim = aprendizado mais rápido (adaptar rapidamente)
                rate_multiplier = 2.0
            else:
                rate_multiplier = 1.0

            adaptive_rate = base_rate * rate_multiplier
            return min(max(adaptive_rate, 0.01), 0.5)

        except Exception:
            return 0.1

    def _calculate_adaptation_strength(self) -> float:
        """Calcula força de adaptação necessária."""

        try:
            # Baseado na diferença entre performance atual e esperada
            execution_status = self.execution_interface.get_execution_status()

            current_pnl_pct = execution_status.get("total_pnl_pct", 0.0)
            expected_pnl_pct = 5.0  # 5% esperado

            performance_gap = abs(current_pnl_pct - expected_pnl_pct) / 10.0

            # Maior gap = maior força de adaptação necessária
            adaptation_strength = min(performance_gap, 1.0)

            return adaptation_strength

        except Exception:
            return 0.5

    async def _evaluate_performance(self) -> float:
        """Avalia performance geral do sistema."""

        try:
            # Componentes da performance
            consciousness = self.consciousness_state.consciousness_level
            execution_efficiency = self.consciousness_state.execution_efficiency
            system_coherence = self.consciousness_state.system_coherence
            self_assessment = self.consciousness_state.self_assessment

            # Métricas de execução
            execution_status = self.execution_interface.get_execution_status()
            pnl_pct = execution_status.get("total_pnl_pct", 0.0)

            # Normaliza PnL (0-20% -> 0-1)
            normalized_pnl = min(max((pnl_pct + 10) / 20.0, 0.0), 1.0)

            # Combina fatores
            performance_score = (
                consciousness * 0.2
                + execution_efficiency * 0.3
                + system_coherence * 0.2
                + self_assessment * 0.15
                + normalized_pnl * 0.15
            )

            return min(max(performance_score, 0.0), 1.0)

        except Exception as e:
            logger.error(f"Erro avaliando performance: {e}")
            return 0.5

    async def _get_valid_metacognitive_context(self) -> Optional[Any]:
        """
        Obtém e valida o último contexto da metacognição.

        Retorna o contexto se ele for válido e contiver ajustes,
        caso contrário, retorna None.
        """
        try:
            metacog = getattr(self.oracle_engine, "metacognition", None)
            if not (metacog and hasattr(metacog, "get_last_metacognitive_context")):
                return None

            metacog_ctx = metacog.get_last_metacognitive_context()

            # Um contexto válido não deve ter mensagens de erro e deve ter gerado ajustes
            if (
                metacog_ctx
                and not getattr(metacog_ctx, "error_messages", [])
                and getattr(metacog_ctx, "adjustments_applied", 0) > 0
            ):
                return metacog_ctx

        except Exception as meta_exc:
            logger.debug("Erro obtendo resultado da metacognição: %s", meta_exc)

        return None

    async def _should_adapt(self) -> bool:
        """Determina se o sistema deve se adaptar."""

        try:
            # Critérios para adaptação
            performance_score = await self._evaluate_performance()
            adaptation_strength = self.consciousness_state.adaptation_strength

            # Adapta se performance baixa ou força de adaptação alta
            should_adapt = (
                performance_score < 0.4
                or adaptation_strength > self.adaptation_threshold
            )

            return should_adapt

        except Exception:
            return False

    async def _adapt_system(self, metacog_ctx: Any):
        """Executa adaptação do sistema com base no contexto metacognitivo."""

        try:
            logger.info("🔄 Iniciando adaptação do sistema...")

            # O contexto da metacognição agora guia a adaptação
            # A lógica específica de adaptação usaria os detalhes de `metacog_ctx`
            # para realizar ajustes direcionados.
            #
            # Por enquanto, mantemos a lógica de adaptação geral,
            # mas o contexto está disponível para futuras melhorias.

            consciousness = self.consciousness_state.consciousness_level
            execution_efficiency = self.consciousness_state.execution_efficiency
            system_coherence = self.consciousness_state.system_coherence

            adaptations_made = []

            # Adaptação da consciência (oráculo)
            if consciousness < 0.5:
                # Ajusta parâmetros do oráculo
                # (implementação específica dependeria dos parâmetros expostos)
                adaptations_made.append("oracle_consciousness")
                logger.info("🧠 Adaptando parâmetros de consciência do oráculo")

            # Adaptação da execução
            if execution_efficiency < 0.5:
                # Ajusta parâmetros de execução
                # (implementação específica dependeria dos parâmetros expostos)
                adaptations_made.append("execution_efficiency")
                logger.info("🔧 Adaptando parâmetros de execução")

            # Adaptação da coerência
            if system_coherence < 0.5:
                # Ajusta sincronização entre componentes
                adaptations_made.append("system_coherence")
                logger.info("🔗 Adaptando coerência do sistema")

            # Atualiza contador de adaptações
            self.system_metrics.adaptation_events += len(adaptations_made)

            if adaptations_made:
                logger.info(f"✅ Adaptação concluída: {', '.join(adaptations_made)}")
            else:
                logger.info("✅ Nenhuma adaptação sistêmica foi necessária.")

        except Exception as e:
            logger.error(f"❌ Erro na adaptação do sistema: {e}")

    def _update_system_metrics(self, performance_score: float):
        """Atualiza métricas do sistema."""

        try:
            current_time = time.time()

            # Uptime
            self.system_metrics.uptime = current_time - self.start_time

            # Decisões
            self.system_metrics.total_decisions = len(
                self.oracle_engine.decision_history
            )

            # Execuções
            execution_status = self.execution_interface.get_execution_status()
            self.system_metrics.executed_decisions = execution_status.get(
                "total_trades", 0
            )

            # Taxa de sucesso de execução
            if self.system_metrics.total_decisions > 0:
                self.system_metrics.execution_success_rate = (
                    self.system_metrics.executed_decisions
                    / self.system_metrics.total_decisions
                )

            # Confiança média das decisões
            self.system_metrics.average_decision_confidence = (
                self.consciousness_state.decision_confidence
            )

            # Estabilidade da consciência
            self.system_metrics.consciousness_stability = (
                self.consciousness_state.temporal_awareness
            )

            # Performance score
            self.system_metrics.performance_score = performance_score

            # Timestamp
            self.system_metrics.timestamp = current_time

        except Exception as e:
            logger.error(f"Erro atualizando métricas do sistema: {e}")

    def _log_consciousness_status(self):
        """Log do status da consciência."""

        try:
            state = self.consciousness_state
            metrics = self.system_metrics

            logger.info(
                f"🌌 Consciência QUALIA: "
                f"Level={state.consciousness_level:.2f}, "
                f"Confidence={state.decision_confidence:.2f}, "
                f"Efficiency={state.execution_efficiency:.2f}, "
                f"Coherence={state.system_coherence:.2f}, "
                f"Performance={metrics.performance_score:.2f}"
            )

        except Exception as e:
            logger.error(f"Erro no log de consciência: {e}")

    async def stop_consciousness(self):
        """Para operação da consciência."""

        logger.info("🛑 Parando consciência QUALIA...")

        self.running = False

        # Para interface de execução
        await self.execution_interface.stop_execution_loop()

        # Cancela task de consciência
        if self.consciousness_task:
            self.consciousness_task.cancel()
            try:
                await self.consciousness_task
            except asyncio.CancelledError:
                pass

        logger.info("✅ Consciência QUALIA parada")

    async def shutdown(self):
        """Encerra sistema de consciência unificada."""

        logger.info("🌀 Encerrando consciência QUALIA unificada...")

        try:
            # Para operação
            await self.stop_consciousness()

            # Encerra componentes
            await self.oracle_engine.shutdown()
            await self.execution_interface.shutdown()

            logger.info("✅ Consciência QUALIA unificada encerrada")

        except Exception as e:
            logger.error(f"❌ Erro encerrando consciência unificada: {e}")

    def get_unified_status(self) -> Dict[str, Any]:
        """Retorna status unificado do sistema."""

        return {
            "system_info": {
                "running": self.running,
                "mode": self.mode,
                "uptime": self.system_metrics.uptime,
                "symbols": len(self.symbols),
                "timeframes": len(self.timeframes),
            },
            "consciousness_state": {
                "consciousness_level": self.consciousness_state.consciousness_level,
                "decision_confidence": self.consciousness_state.decision_confidence,
                "execution_efficiency": self.consciousness_state.execution_efficiency,
                "system_coherence": self.consciousness_state.system_coherence,
                "self_assessment": self.consciousness_state.self_assessment,
                "temporal_awareness": self.consciousness_state.temporal_awareness,
                "risk_perception": self.consciousness_state.risk_perception,
                "market_understanding": self.consciousness_state.market_understanding,
            },
            "system_metrics": {
                "total_decisions": self.system_metrics.total_decisions,
                "executed_decisions": self.system_metrics.executed_decisions,
                "execution_success_rate": self.system_metrics.execution_success_rate,
                "average_decision_confidence": self.system_metrics.average_decision_confidence,
                "consciousness_stability": self.system_metrics.consciousness_stability,
                "adaptation_events": self.system_metrics.adaptation_events,
                "performance_score": self.system_metrics.performance_score,
            },
            "oracle_status": self.oracle_engine.get_consciousness_status(),
            "execution_status": self.execution_interface.get_execution_status(),
        }
