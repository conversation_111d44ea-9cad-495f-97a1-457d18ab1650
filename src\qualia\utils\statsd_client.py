from __future__ import annotations

from typing import Optional

from datadog import DogStatsd

from ..config.settings import market_metrics_enabled
from .logger import get_logger

_logger = get_logger(__name__)
_statsd_client: Optional[DogStatsd] = None


def get_statsd_client() -> Optional[DogStatsd]:
    """Return a shared ``DogStatsd`` client when metrics are enabled."""
    global _statsd_client
    if _statsd_client is None and market_metrics_enabled:
        try:  # pragma: no cover - best effort
            _statsd_client = DogStatsd()
        except Exception as exc:  # pragma: no cover - defensive
            _logger.debug("Failed to initialize DogStatsd client: %s", exc)
            _statsd_client = None
    return _statsd_client


__all__ = ["get_statsd_client"]
