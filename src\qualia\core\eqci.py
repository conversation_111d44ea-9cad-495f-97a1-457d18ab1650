"""Enhanced Quantum-Classical Interface module."""

from __future__ import annotations

import logging
from typing import Dict, List, Optional

import numpy as np
import pandas as pd
from qiskit import QuantumCircuit
from qiskit.circuit.library import StatePreparation

from ..utils.logger import get_logger

logger = get_logger(__name__)


class EnhancedQuantumClassicalInterface:
    """Interface para codificar dados de mercado clássicos."""

    def __init__(
        self,
        critical_feature_window: int = 20,
        secondary_feature_window: int = 20,
        volume_ratio_cap: float = 3.0,
    ) -> None:
        self.critical_feature_window = critical_feature_window
        self.secondary_feature_window = secondary_feature_window
        self.volume_ratio_cap = volume_ratio_cap
        logger.info(
            "EQCI inicializado com: critical_window=%s, secondary_window=%s, vol_cap=%s",
            critical_feature_window,
            secondary_feature_window,
            volume_ratio_cap,
        )

    def _normalize_feature_zscore(
        self, series: pd.Series, window: int, clip_std_devs: float = 2.0
    ) -> float:
        """Normaliza uma feature usando Z-score com clipping."""
        if series.empty or len(series) < window:
            return 0.0

        actual_window = min(window, len(series))
        mean = series.rolling(window=actual_window).mean().iloc[-1]
        std = series.rolling(window=actual_window).std().iloc[-1]

        if pd.isna(mean) or pd.isna(std):
            if len(series) > 1:
                mean = series.mean()
                std = series.std()
            else:
                return 0.0

        if std == 0:
            return 0.0

        z_score = (series.iloc[-1] - mean) / std
        return float(np.clip(z_score, -clip_std_devs, clip_std_devs) / clip_std_devs)

    def _encode_critical_features_state_prep(
        self, norm_price_change: float, norm_volatility: float
    ) -> QuantumCircuit:
        """Codifica momentum e volatilidade normalizadas em um qubit."""
        qc_critical = QuantumCircuit(1, name="Q_CritFeatures_SP")
        critical_features_vector = [norm_price_change, norm_volatility]
        norm_val = np.linalg.norm(critical_features_vector)

        if norm_val == 0:
            state_prep_vector = [1 / np.sqrt(2), 1 / np.sqrt(2)]
        else:
            state_prep_vector = [x / norm_val for x in critical_features_vector]

        try:
            prep = StatePreparation(state_prep_vector, label="crit_prep")
            qc_critical.append(prep, [0])
        except Exception as exc:
            logger.error(
                "EQCI: Erro ao aplicar StatePreparation para %s: %s. Usando RY/RZ fallback.",
                state_prep_vector,
                exc,
                exc_info=True,
            )
            angle_y = (
                np.arccos(np.clip(state_prep_vector[0] * np.sqrt(2), -1, 1)) * 2
                if len(state_prep_vector) > 0
                else np.pi / 2
            )
            angle_z = (
                np.arccos(np.clip(state_prep_vector[1] * np.sqrt(2), -1, 1)) * 2
                if len(state_prep_vector) > 1
                else 0
            )
            qc_critical.ry(angle_y, 0)
            qc_critical.rz(angle_z, 0)
        return qc_critical

    def _encode_volume_ratio_angle(
        self, norm_volume_ratio_angle: float
    ) -> QuantumCircuit:
        """Codifica a razão de volume normalizada em um qubit usando RX."""
        qc_volume = QuantumCircuit(1, name="Q_Volume_Angle")
        theta_volume = norm_volume_ratio_angle * np.pi
        qc_volume.rx(theta_volume, 0)
        return qc_volume

    def encode_market_data(
        self, features: Dict[str, float], max_qubits: Optional[int] = None
    ) -> QuantumCircuit:
        """Codifica features financeiras utilizando amplitude e angle encoding."""
        if not features:
            logger.warning(
                "EQCI: Nenhuma feature para codificar. Retornando circuito padrão."
            )
            qc = QuantumCircuit(2, name="Q_MarketState_Empty")
            qc.h(range(2))
            return qc

        features = dict(features)
        angle_ops: List[float] = []
        obi_val = features.pop("order_book_imbalance", None)
        if obi_val is not None:
            try:
                angle_ops.append(
                    float(np.clip(float(obi_val), -1.0, 1.0) * (np.pi / 2.0))
                )
            except Exception:
                angle_ops.append(0.0)
        fr_val = features.pop("funding_rate", None)
        if fr_val is not None:
            try:
                angle_ops.append(
                    float(np.clip(float(fr_val) * 10.0, -np.pi / 2.0, np.pi / 2.0))
                )
            except Exception:
                angle_ops.append(0.0)
        oi_val = features.pop("open_interest", None)
        if oi_val is not None:
            try:
                angle_ops.append(float(np.clip(float(oi_val), 0.0, 1.0) * np.pi))
            except Exception:
                angle_ops.append(0.0)
        ts_embed = features.pop("ts_embedding", None)
        if isinstance(ts_embed, (list, np.ndarray)):
            for idx, val in enumerate(ts_embed):
                try:
                    features[f"embed_{idx}"] = float(val)
                except Exception:
                    features[f"embed_{idx}"] = 0.0

        if isinstance(features.get("close_prices"), (list, np.ndarray)):
            price_array = np.asarray(features["close_prices"], dtype=float)
            n_qubits = min(10, int(np.ceil(np.log2(len(price_array)))))
            target_len = 2**n_qubits
            if len(price_array) > target_len:
                step = len(price_array) / target_len
                reduced = [
                    np.mean(price_array[int(step * i) : int(step * (i + 1))])
                    for i in range(target_len)
                ]
                vector = np.array(reduced, dtype=float)
            else:
                pad = target_len - len(price_array)
                vector = np.pad(price_array, (0, pad), mode="edge")

            norm_vec = np.linalg.norm(vector)
            final_vector = (
                vector / norm_vec
                if norm_vec > 1e-10
                else np.concatenate(([1.0], np.zeros(target_len - 1)))
            )

            qc = QuantumCircuit(n_qubits, name=f"Q_Prices_{len(price_array)}")
            try:
                qc.append(StatePreparation(final_vector.tolist()), range(n_qubits))
                qc.barrier()
            except Exception as exc:
                logger.error(
                    "EQCI: Erro ao aplicar StatePreparation: %s. Usando fallback direto. final_vector_len=%d, n_qubits=%d, is_power_of_2=%s",
                    exc,
                    len(final_vector),
                    n_qubits,
                    np.log2(len(final_vector)).is_integer(),
                )
                qc = QuantumCircuit(n_qubits, name="Q_MarketState_Fallback_Direct")
                qc.h(range(n_qubits))

            return qc

        logger.debug("EQCI: Codificando features: %s", features)
        feature_names = sorted(features.keys())
        feature_vector = np.array([features[name] for name in feature_names])

        norm = np.linalg.norm(feature_vector)
        if norm < 1e-10:
            logger.warning(
                "EQCI: Vetor de features com norma próxima de zero. Usando distribuição uniforme."
            )
            n_qubits = max(2, int(np.ceil(np.log2(len(feature_names)))))
            qc = QuantumCircuit(n_qubits, name="Q_MarketState_NullInput")
            qc.h(range(n_qubits))
            return qc

        normalized = feature_vector / norm
        n_qubits = max(2, int(np.ceil(np.log2(len(normalized)))))

        qc = QuantumCircuit(n_qubits, name=f"Q_MarketState_AmpEnc_{len(normalized)}F")
        try:
            target_vector_len = 2**n_qubits
            if len(normalized) < target_vector_len:
                padding_size = max(0, target_vector_len - len(normalized))
                padded_vector = np.pad(normalized, (0, padding_size), "constant")
                feature_mapping = {
                    i: name if i < len(feature_names) else f"padding_{i}"
                    for i, name in enumerate(feature_names)
                }
                logger.debug(
                    "EQCI: Padding aplicado. Features mapeadas: %s", feature_mapping
                )
            elif len(normalized) > target_vector_len:
                padded_vector = normalized[:target_vector_len]
                logger.warning(
                    "EQCI: Vetor normalizado (%d) maior que o esperado para %d qubits. Truncando.",
                    len(normalized),
                    n_qubits,
                )
                feature_mapping = {
                    i: name for i, name in enumerate(feature_names[:target_vector_len])
                }
                logger.warning(
                    "EQCI: Features ignoradas: %s", feature_names[target_vector_len:]
                )
            else:
                padded_vector = normalized
                feature_mapping = {i: name for i, name in enumerate(feature_names)}

            if not np.log2(len(padded_vector)).is_integer():
                n_qubits = int(np.ceil(np.log2(len(padded_vector))))
                target_vector_len = 2**n_qubits
                padding_size = max(0, target_vector_len - len(normalized))
                padded_vector = np.pad(normalized, (0, padding_size), "constant")
                logger.warning(
                    "EQCI: Vetor com comprimento não potência de 2 (%d). Ajustando para %d.",
                    len(normalized),
                    target_vector_len,
                )

            norm_padded = np.linalg.norm(padded_vector)
            if norm_padded < 1e-10:
                final_vector = np.zeros(target_vector_len)
                final_vector[0] = 1.0
                logger.warning(
                    "EQCI: Vetor padded com norma zero. Usando estado |0...0⟩."
                )
            else:
                final_vector = padded_vector / norm_padded

            n_qubits = int(np.ceil(np.log2(len(final_vector))))
            target_vector_len = 2**n_qubits
            if len(final_vector) != target_vector_len:
                padding_size = max(0, target_vector_len - len(final_vector))
                final_vector = np.pad(final_vector, (0, padding_size), "constant")
                norm = np.linalg.norm(final_vector)
                if norm > 1e-10:
                    final_vector = final_vector / norm

            logger.debug(
                "EQCI: final_vector_len=%d, n_qubits=%d, is_power_of_2=%s",
                len(final_vector),
                n_qubits,
                np.log2(len(final_vector)).is_integer(),
            )

            final_vector = np.asarray(final_vector)
            _check_imag_lambda = lambda v: (
                np.any(v != 0) if isinstance(v, np.ndarray) else v != 0
            )
            has_complex = np.iscomplexobj(final_vector) or any(
                _check_imag_lambda(getattr(val, "imag", 0)) for val in final_vector
            )
            if has_complex:
                logger.warning(
                    "EQCI: Vetor com valores complexos detectado. Usando apenas parte real."
                )
                final_vector = np.real(final_vector)
                norm = np.linalg.norm(final_vector)
                if norm > 1e-10:
                    final_vector = final_vector / norm
                else:
                    final_vector = np.concatenate(
                        ([1.0], np.zeros(len(final_vector) - 1))
                    )

            final_vector = final_vector.flatten().tolist()

            expected_vector_len = 2**n_qubits
            if len(final_vector) != expected_vector_len:
                logger.warning(
                    "EQCI: Ajustando tamanho do vetor final: %d -> %d",
                    len(final_vector),
                    expected_vector_len,
                )
                if len(final_vector) < expected_vector_len:
                    padding_size = expected_vector_len - len(final_vector)
                    final_vector = final_vector + [0.0] * padding_size
                else:
                    final_vector = final_vector[:expected_vector_len]
                final_vector_np = np.array(final_vector)
                norm = np.linalg.norm(final_vector_np)
                if norm > 1e-10:
                    final_vector = (final_vector_np / norm).tolist()

            prep_op = StatePreparation(final_vector)
            qc.append(prep_op, range(n_qubits))

            feature_values = []
            for name in feature_names[:4]:
                try:
                    value_as_float = float(features[name])
                    feature_values.append(f"{name}: {value_as_float:.4f}")
                except Exception:
                    feature_values.append(f"{name}: {features[name]}")
            feature_values_str = ", ".join(feature_values)
            if len(feature_names) > 4:
                feature_values_str += f" e mais {len(feature_names)-4} features"

            logger.debug(
                "EQCI: Amplitude Encoding com %d features em %d qubits. Features principais: [%s]",
                len(normalized),
                n_qubits,
                feature_values_str,
            )

            qc.barrier()
        except Exception as exc:
            logger.error(
                "EQCI: Erro ao aplicar StatePreparation: %s. Usando fallback.",
                exc,
                exc_info=True,
            )
            qc = QuantumCircuit(n_qubits, name="Q_MarketState_Fallback")
            qc.h(range(n_qubits))
            try:
                for i, val in enumerate(normalized[:n_qubits]):
                    if i >= n_qubits:
                        break
                    if np.iscomplexobj(val):
                        if getattr(val, "imag", 0) != 0:
                            logger.debug(
                                "EQCI: Componente imaginária detectada em valor de fallback. Descartando parte imaginária."
                            )
                        val = float(getattr(val, "real", val))
                    if np.isnan(val) or np.isinf(val):
                        val = 0.0
                    theta = float(val * np.pi)
                    qc.ry(theta, i)
                for i in range(n_qubits - 1):
                    qc.cx(i, i + 1)
            except Exception as ef:
                logger.error(
                    "EQCI: Até o fallback falhou: %s. Mantendo em superposição.",
                    ef,
                )

        if angle_ops:
            if max_qubits is not None:
                available = max(0, max_qubits - qc.num_qubits)
                angle_ops = angle_ops[:available]
            total_qubits = qc.num_qubits + len(angle_ops)
            qc_full = QuantumCircuit(total_qubits, name=f"{qc.name}_EXT")
            qc_full.compose(qc, range(qc.num_qubits), inplace=True)
            for idx, ang in enumerate(angle_ops):
                qc_full.ry(ang, qc.num_qubits + idx)
            qc_full.barrier()
            return qc_full

        return qc
