"""
QUALIA: Quantum Universal Awareness Lattice Interface Architecture
Sistema de Cache Unificado

Este módulo implementa um sistema unificado de cache para otimizar o desempenho
do sistema QUALIA, reduzindo chamadas de API desnecessárias e cálculos repetitivos.

O cache em memória é protegido por um ``threading.RLock``. Todas as operações
de leitura e escrita em ``_memory_cache`` devem adquirir este bloqueio para
garantir segurança em cenários com múltiplas threads.
"""

from __future__ import annotations

import json
from ..utils.logger import get_logger
from .metrics_persistence import persist_metric_to_qpm
import os
import time
from threading import RLock
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, TypeVar
from dataclasses import dataclass

import numpy as np

try:
    from qiskit.quantum_info import Statevector
except Exception:  # pragma: no cover - optional dependency

    class Statevector:  # type: ignore[misc]
        pass


import pandas as pd

from ..utils.persistence import JSONEncoder, convert_to_serializable

# Tipo genérico para o decorator de cache
T = TypeVar("T")

# Cache em memória (dict global)
_memory_cache: Dict[str, Dict[str, Any]] = {
    "data": {},  # Dados armazenados
    "expiry": {},  # Timestamps de expiração
    "metadata": {},  # Metadados do cache (tipo, tamanho, etc)
}

# Bloqueio para acesso concorrente ao cache em memória
_memory_cache_lock = RLock()

logger = get_logger(__name__)

# Singleton instance for the global cache manager
_GLOBAL_CACHE_MANAGER: "CacheManager" | None = None
# Track if singleton warning has been emitted
_WARNING_EMITTED = False


def get_cache_manager(
    cache_dir: Optional[str] = None,
    expiry_seconds: int = 3600,
    cache_type: str = "memory",
) -> "CacheManager":
    """Return a singleton ``CacheManager``.

    Parameters
    ----------
    cache_dir
        Directory for persistent cache files.
    expiry_seconds
        Default expiration period for cached items.
    cache_type
        Type of cache to create on first use ("memory", "disk" or "tiered").

    Returns
    -------
    CacheManager
        The singleton instance.
    """
    global _GLOBAL_CACHE_MANAGER, _WARNING_EMITTED
    if _GLOBAL_CACHE_MANAGER is None:
        if cache_type == "memory":
            _GLOBAL_CACHE_MANAGER = MemoryCache(expiry_seconds=expiry_seconds)
        elif cache_type == "disk":
            _GLOBAL_CACHE_MANAGER = DiskCache(
                cache_dir=cache_dir, expiry_seconds=expiry_seconds
            )
        elif cache_type == "tiered":
            memory_cache = MemoryCache(expiry_seconds=expiry_seconds)
            disk_cache = DiskCache(cache_dir=cache_dir, expiry_seconds=expiry_seconds)
            _GLOBAL_CACHE_MANAGER = TieredCache(
                memory_cache=memory_cache, disk_cache=disk_cache
            )
        else:
            raise ValueError(f"Tipo de cache desconhecido: {cache_type}")
    else:
        if not _WARNING_EMITTED:
            logger.warning("CacheManager already exists; reusing singleton instance")
            _WARNING_EMITTED = True
    return _GLOBAL_CACHE_MANAGER


@dataclass
class CacheStatistics:
    """Statistics about the current memory cache state."""

    total_items: int
    expired_items: int
    live_items: int
    functions: Dict[str, Any]


def numpy_cache(maxsize: int = 128):
    """Simple LRU cache decorator that supports numpy arrays and Statevectors."""

    cache: Dict[Any, Any] = {}
    lru_keys: List[Any] = []

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            key = get_cache_key(*args, **kwargs)
            with _memory_cache_lock:
                if key in cache:
                    if key in lru_keys:
                        lru_keys.remove(key)
                    lru_keys.append(key)
                    result = cache[key]
                    if args:
                        obj = args[0]
                        hook = getattr(obj, "_cache_hit_hook", None)
                        if callable(hook):
                            try:
                                hook(func.__name__, result)
                            except Exception as exc:  # pragma: no cover - defensive
                                logger.debug("Cache hit hook error: %s", exc)
                    return result

                if len(cache) >= maxsize and lru_keys:
                    oldest = lru_keys.pop(0)
                    cache.pop(oldest, None)

            result = func(*args, **kwargs)
            with _memory_cache_lock:
                cache[key] = result
                lru_keys.append(key)
            return result

        return wrapper

    return decorator


class CacheManager:
    """
    Gerenciador centralizado de cache para o sistema QUALIA.

    Esta classe fornece métodos para gerenciar o cache de forma centralizada,
    permitindo operações como armazenamento, recuperação, invalidação e limpeza
    de diferentes tipos de dados em cache.
    """

    def __new__(cls, *args: Any, **kwargs: Any):
        """Create a new instance."""
        return super().__new__(cls)

    def __init__(
        self,
        cache_dir: Optional[str] = None,
        expiry_seconds: int = 3600,
        cache_type: Optional[str] = None,
        memory_cache: Optional["CacheManager"] = None,
        disk_cache: Optional["CacheManager"] = None,
    ):
        """
        Inicializa o gerenciador de cache.

        Args:
            cache_dir: Diretório para armazenar dados persistentes em cache
                      (None para usar apenas cache em memória)
            expiry_seconds: Tempo padrão de expiração em segundos
            cache_type: Tipo de cache ('memory', 'disk', 'tiered') para delegação
            memory_cache: Instância de cache em memória para uso em 'tiered'
            disk_cache: Instância de cache em disco para uso em 'tiered'
        """
        # Delegação para tipos especializados
        if cache_type:
            if cache_type == "memory":
                self._delegate = memory_cache or MemoryCache(
                    expiry_seconds=expiry_seconds
                )
            elif cache_type == "disk":
                self._delegate = disk_cache or DiskCache(
                    cache_dir=cache_dir, expiry_seconds=expiry_seconds
                )
            elif cache_type == "tiered":
                self._delegate = TieredCache(
                    memory_cache=memory_cache, disk_cache=disk_cache
                )
            else:
                raise ValueError(f"Tipo de cache desconhecido: {cache_type}")
            return

        # Importamos config aqui para evitar importação circular
        from ..config import config

        # Inicialização padrão
        self.cache_dir = cache_dir or config.cache_dir
        self.default_expiry = expiry_seconds

        # Cria o diretório de cache se não existir
        if self.cache_dir:
            os.makedirs(self.cache_dir, exist_ok=True)

        # Inicializa o logger quando necessário
        logger.info(f"CacheManager inicializado: dir={self.cache_dir}")

    def _compose_key(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        Gera a chave interna combinando nome e argumentos opcionais.
        """
        if key_args or key_kwargs:
            args_key = get_cache_key(*(key_args or []), **(key_kwargs or {}))
            return f"{key}:{args_key}"
        return key

    def set(
        self,
        key: str,
        value: Any,
        expiry_seconds: Optional[int] = None,
        ttl: Optional[float] = None,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Armazena um valor no cache.

        Args:
            key: Chave para identificar o valor
            value: Valor a ser armazenado
            expiry_seconds: Tempo de expiração em segundos (None para usar o padrão)
            ttl: Tempo de vida útil em segundos (None para usar o padrão)
            key_args: Argumentos adicionais para a chave
            key_kwargs: Argumentos nomeados adicionais para a chave
        """
        # Determina tempo de expiração (prioriza ttl se informado)
        expiry = (
            ttl
            if ttl is not None
            else (expiry_seconds if expiry_seconds is not None else self.default_expiry)
        )
        current_time = time.time()

        # Armazena em memória com composite key
        composite_key = self._compose_key(key, key_args, key_kwargs)
        with _memory_cache_lock:
            _memory_cache["data"][composite_key] = value
            _memory_cache["expiry"][composite_key] = current_time + expiry

        # Se for DataFrame, considera persistência
        if isinstance(value, pd.DataFrame) and self.cache_dir:
            self._save_dataframe_to_disk(composite_key, value, expiry)

    def get(
        self,
        key: str,
        default: Optional[Any] = None,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """
        Recupera um valor do cache.

        Args:
            key: Chave para buscar o valor
            default: Valor a ser retornado se o valor não for encontrado
            key_args: Argumentos adicionais para a chave
            key_kwargs: Argumentos nomeados adicionais para a chave

        Returns:
            Valor armazenado ou None se não encontrado/expirado
        """
        current_time = time.time()

        composite_key = self._compose_key(key, key_args, key_kwargs)
        # Verifica cache em memória
        with _memory_cache_lock:
            if composite_key in _memory_cache["data"]:
                if _memory_cache["expiry"].get(composite_key, 0) > current_time:
                    if getattr(self, "collect_stats", False):
                        self._hits += 1
                        persist_metric_to_qpm("cache_hit", True)
                    return _memory_cache["data"][composite_key]
                else:
                    if getattr(self, "collect_stats", False):
                        self._misses += 1
                        persist_metric_to_qpm("cache_hit", False)
                    # Limpa item expirado
                    self._remove_from_memory(composite_key)

        # Verifica cache em disco para DataFrames
        if self.cache_dir:
            df = self._load_dataframe_from_disk(key)
            if df is not None:
                # Atualiza cache em memória
                with _memory_cache_lock:
                    _memory_cache["data"][composite_key] = df
                    # A expiração já foi verificada no método
                    # _load_dataframe_from_disk
                    _memory_cache["expiry"][composite_key] = self._get_expiry_from_disk(
                        key
                    )
                return df

        if getattr(self, "collect_stats", False):
            self._misses += 1
        return default

    def invalidate(self, key: str) -> bool:
        """
        Invalida um item específico do cache.

        Args:
            key: Chave a ser invalidada

        Returns:
            True se o item foi invalidado, False se não existia
        """
        with _memory_cache_lock:
            exists = key in _memory_cache["data"]

        # Remove da memória
        self._remove_from_memory(key)

        # Remove do disco
        if self.cache_dir:
            self._remove_from_disk(key)

        return exists

    def clear(self, key_prefix: Optional[str] = None) -> int:
        """
        Limpa items do cache.

        Args:
            key_prefix: Prefixo para limpar seletivamente (None para limpar tudo)

        Returns:
            Número de itens removidos
        """
        return clear_cache(key_prefix)

    def _remove_from_memory(self, key: str) -> None:
        """Remove um item do cache em memória."""
        with _memory_cache_lock:
            if key in _memory_cache["data"]:
                del _memory_cache["data"][key]
            if key in _memory_cache["expiry"]:
                del _memory_cache["expiry"][key]

    def _remove_from_disk(self, key: str) -> None:
        """Remove um item do cache em disco."""
        if not self.cache_dir:
            return

        file_key = key.replace(":", "_").replace("/", "_")
        csv_path = os.path.join(self.cache_dir, f"{file_key}.csv")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")

        if os.path.exists(csv_path):
            os.remove(csv_path)
        if os.path.exists(meta_path):
            os.remove(meta_path)

    def _save_dataframe_to_disk(
        self, key: str, df: pd.DataFrame, expiry_seconds: int
    ) -> None:
        """Salva um DataFrame em disco."""
        from ..trading.data_utils import is_data_empty

        if is_data_empty(df):
            return

        file_key = key.replace(":", "_").replace("/", "_")
        csv_path = os.path.join(self.cache_dir, f"{file_key}.csv")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")

        try:
            # Identifica colunas de data/hora
            datetime_columns = [
                col
                for col in df.columns
                if pd.api.types.is_datetime64_any_dtype(df[col])
            ]

            # Salva metadados
            metadata = {
                "created": time.time(),
                "expiry": time.time() + expiry_seconds,
                "shape": df.shape,
                "columns": df.columns.tolist(),
                "datetime_columns": datetime_columns,
            }

            with open(meta_path, "w") as f:
                json.dump(metadata, f, cls=JSONEncoder)

            # Salva DataFrame
            df.to_csv(csv_path, index=False)
            logger.debug(f"DataFrame salvo em disco: {csv_path}")

        except Exception as e:
            logger.warning(f"Erro ao salvar DataFrame em disco: {str(e)}")

    def _load_dataframe_from_disk(self, key: str) -> Optional[pd.DataFrame]:
        """Carrega um DataFrame do disco, verificando expiração."""
        if not self.cache_dir:
            return None

        file_key = key.replace(":", "_").replace("/", "_")
        csv_path = os.path.join(self.cache_dir, f"{file_key}.csv")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")

        if os.path.exists(csv_path) and os.path.exists(meta_path):
            try:
                # Carrega metadados
                with open(meta_path, "r") as f:
                    metadata = json.load(f)

                # Verifica expiração
                if metadata.get("expiry", 0) > time.time():
                    # Carrega o DataFrame
                    df = pd.read_csv(csv_path)

                    # Converte colunas de data/hora
                    for col in metadata.get("datetime_columns", []):
                        if col in df.columns:
                            df[col] = pd.to_datetime(df[col], utc=True)

                    logger.debug(f"DataFrame carregado do disco: {csv_path}")
                    return df
                else:
                    # Remove arquivos expirados
                    logger.debug(f"Cache expirado: {csv_path}")
                    os.remove(csv_path)
                    os.remove(meta_path)

            except Exception as e:
                logger.warning(f"Erro ao carregar DataFrame do disco: {str(e)}")

        return None

    def _get_expiry_from_disk(self, key: str) -> float:
        """Obtém o timestamp de expiração de um arquivo em disco."""
        if not self.cache_dir:
            return 0.0

        file_key = key.replace(":", "_").replace("/", "_")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")

        if os.path.exists(meta_path):
            try:
                with open(meta_path, "r") as f:
                    metadata = json.load(f)
                return metadata.get("expiry", 0.0)
            except Exception:
                return 0.0

        return 0.0

    def has(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Verifica se a chave existe e não expirou no cache."""
        return (
            self.get(key, default=None, key_args=key_args, key_kwargs=key_kwargs)
            is not None
        )

    def remove(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Remove o item do cache (memória e disco)."""
        composite_key = self._compose_key(key, key_args, key_kwargs)
        return self.invalidate(composite_key)

    def reset_stats(self) -> None:
        """Reseta os contadores de estatísticas de cache."""
        self._hits = 0
        self._misses = 0

    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas de cache: hits, misses e hit_rate."""
        total = getattr(self, "_hits", 0) + getattr(self, "_misses", 0)
        hit_rate = (self._hits / total) if total > 0 else 0.0
        return {
            "hits": getattr(self, "_hits", 0),
            "misses": getattr(self, "_misses", 0),
            "hit_rate": hit_rate,
        }


def get_cache_key(*args: Any, **kwargs: Any) -> str:
    """
    Gera uma chave única para os argumentos.

    Args:
        *args: Argumentos posicionais
        **kwargs: Argumentos nomeados

    Returns:
        String representando a chave do cache
    """
    # Concatena todos os args e kwargs em uma string
    key_parts = []

    for arg in args:
        if isinstance(arg, (str, int, float, bool, type(None))):
            key_parts.append(str(arg))
        elif isinstance(arg, pd.DataFrame):
            import hashlib

            df_hash = hashlib.sha256(arg.values.tobytes()).hexdigest()
            key_parts.append(f"df_{df_hash}")
        elif isinstance(arg, np.ndarray):
            import hashlib

            arr_hash = hashlib.sha256(arg.tobytes()).hexdigest()
            key_parts.append(f"nd_{arr_hash}_{arg.shape}")
        elif isinstance(arg, Statevector):
            import hashlib

            sv_hash = hashlib.sha256(arg.data.tobytes()).hexdigest()
            key_parts.append(f"sv_{sv_hash}_{arg.data.shape}")
        elif isinstance(arg, list) or isinstance(arg, tuple):
            key_parts.append(str([str(x) for x in arg]))
        elif isinstance(arg, dict):
            key_parts.append(str({k: str(v) for k, v in sorted(arg.items())}))
        else:
            key_parts.append(f"{type(arg).__name__}_{str(arg)}")

    for k, v in sorted(kwargs.items()):
        if isinstance(v, (str, int, float, bool, type(None))):
            key_parts.append(f"{k}={v}")
        elif isinstance(v, pd.DataFrame):
            import hashlib

            df_hash = hashlib.sha256(v.values.tobytes()).hexdigest()
            key_parts.append(f"{k}=df_{df_hash}")
        elif isinstance(v, np.ndarray):
            import hashlib

            arr_hash = hashlib.sha256(v.tobytes()).hexdigest()
            key_parts.append(f"{k}=nd_{arr_hash}_{v.shape}")
        elif isinstance(v, Statevector):
            import hashlib

            sv_hash = hashlib.sha256(v.data.tobytes()).hexdigest()
            key_parts.append(f"{k}=sv_{sv_hash}_{v.data.shape}")
        elif isinstance(v, list) or isinstance(v, tuple):
            key_parts.append(f"{k}={[str(x) for x in v]}")
        elif isinstance(v, dict):
            key_parts.append(
                f"{k}={str({k2: str(v2) for k2, v2 in sorted(v.items())})}"
            )
        else:
            key_parts.append(f"{k}={type(v).__name__}_{str(v)}")

    # Junta e gera um hash único
    key_str = "_".join(key_parts)

    # Se a chave for muito longa, usa um hash
    if len(key_str) > 256:
        import hashlib

        return hashlib.sha256(key_str.encode("utf-8")).hexdigest()

    return key_str


def cached(
    expiry_seconds: int = 3600, key_prefix: Optional[str] = None
) -> Callable[[Callable[..., T]], Callable[..., T]]:
    """
    Decorator para cachear resultados de funções.

    Args:
        expiry_seconds: Tempo de expiração em segundos
        key_prefix: Prefixo opcional para a chave de cache

    Returns:
        Função decorada que utiliza o sistema de cache
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> T:
            # Identifica a função
            func_id = f"{func.__module__}.{func.__name__}"
            if key_prefix:
                func_id = f"{key_prefix}:{func_id}"

            # Gera chave única para estes argumentos
            args_key = get_cache_key(*args, **kwargs)
            cache_key = f"{func_id}:{args_key}"

            # Verifica se está no cache e não expirou
            current_time = time.time()
            with _memory_cache_lock:
                if cache_key in _memory_cache["data"]:
                    if _memory_cache["expiry"].get(cache_key, 0) > current_time:
                        logger.debug(f"Cache hit: {cache_key}")
                        if func_id not in _memory_cache["metadata"]:
                            _memory_cache["metadata"][func_id] = {
                                "hits": 0,
                                "misses": 0,
                                "items": 0,
                            }
                        _memory_cache["metadata"][func_id]["hits"] += 1
                        persist_metric_to_qpm("cache_hit", True)
                        return _memory_cache["data"][cache_key]
                    else:
                        logger.debug(f"Cache expired: {cache_key}")
                        del _memory_cache["data"][cache_key]
                        if cache_key in _memory_cache["expiry"]:
                            del _memory_cache["expiry"][cache_key]

            # Chama a função original
            logger.debug(f"Cache miss: {cache_key}")
            result = func(*args, **kwargs)

            with _memory_cache_lock:
                _memory_cache["data"][cache_key] = result
                _memory_cache["expiry"][cache_key] = current_time + expiry_seconds

                # Registra metadados
                if func_id not in _memory_cache["metadata"]:
                    _memory_cache["metadata"][func_id] = {
                        "hits": 0,
                        "misses": 0,
                        "items": 0,
                    }

                _memory_cache["metadata"][func_id]["misses"] += 1
                _memory_cache["metadata"][func_id]["items"] = sum(
                    1 for k in _memory_cache["data"] if k.startswith(func_id)
                )
                persist_metric_to_qpm("cache_hit", False)

            return result

        return wrapper

    return decorator


def cached_dataframe(
    expiry_seconds: int = 3600, cache_file_path: Optional[str] = None
) -> Callable[[Callable[..., pd.DataFrame]], Callable[..., pd.DataFrame]]:
    """
    Decorator especializado para cachear DataFrames, com suporte a persistência.

    Args:
        expiry_seconds: Tempo de expiração em segundos
        cache_file_path: Caminho para arquivo de cache persistente

    Returns:
        Função decorada que utiliza o sistema de cache
    """

    def decorator(func: Callable[..., pd.DataFrame]) -> Callable[..., pd.DataFrame]:
        @wraps(func)
        def wrapper(*args: Any, **kwargs: Any) -> pd.DataFrame:
            # Identifica a função
            func_id = f"{func.__module__}.{func.__name__}"

            # Gera chave única para estes argumentos
            args_key = get_cache_key(*args, **kwargs)
            cache_key = f"{func_id}:{args_key}"
            file_key = cache_key.replace(":", "_").replace("/", "_")

            # Verifica se está no cache de memória e não expirou
            current_time = time.time()
            with _memory_cache_lock:
                if cache_key in _memory_cache["data"]:
                    if _memory_cache["expiry"].get(cache_key, 0) > current_time:
                        logger.debug(f"Memory cache hit: {cache_key}")
                        if func_id not in _memory_cache["metadata"]:
                            _memory_cache["metadata"][func_id] = {
                                "hits": 0,
                                "misses": 0,
                                "items": 0,
                            }
                        _memory_cache["metadata"][func_id]["hits"] += 1
                        persist_metric_to_qpm("cache_hit", True)
                        return _memory_cache["data"][cache_key]
                    else:
                        logger.debug(f"Memory cache expired: {cache_key}")
                        del _memory_cache["data"][cache_key]
                        if cache_key in _memory_cache["expiry"]:
                            del _memory_cache["expiry"][cache_key]

            # Se tiver caminho de arquivo, verifica o cache de disco
            if cache_file_path:
                full_path = os.path.join(cache_file_path, f"{file_key}.csv")
                meta_path = os.path.join(cache_file_path, f"{file_key}.meta.json")

                if os.path.exists(full_path) and os.path.exists(meta_path):
                    try:
                        # Carrega metadados
                        with open(meta_path, "r") as f:
                            metadata = json.load(f)

                        # Verifica expiração
                        if metadata.get("expiry", 0) > current_time:
                            logger.debug(f"Disk cache hit: {full_path}")
                            df = pd.read_csv(full_path)

                            # Converte colunas de data/hora se necessário
                            for col in metadata.get("datetime_columns", []):
                                if col in df.columns:
                                    df[col] = pd.to_datetime(df[col], utc=True)

                            # Armazena também em memória
                            with _memory_cache_lock:
                                _memory_cache["data"][cache_key] = df
                                _memory_cache["expiry"][cache_key] = metadata["expiry"]
                                if func_id not in _memory_cache["metadata"]:
                                    _memory_cache["metadata"][func_id] = {
                                        "hits": 0,
                                        "misses": 0,
                                        "items": 0,
                                    }
                                _memory_cache["metadata"][func_id]["hits"] += 1
                                persist_metric_to_qpm("cache_hit", True)

                            return df
                    except Exception as e:
                        logger.warning(f"Error loading from disk cache: {str(e)}")

            # Se chegou aqui, não está em nenhum cache
            logger.debug(f"Cache miss: {cache_key}")
            df = func(*args, **kwargs)

            # Se não é um DataFrame, retorna sem cachear
            if not isinstance(df, pd.DataFrame):
                logger.warning(f"Function {func_id} did not return a DataFrame")
                return df

            from ..trading.data_utils import is_data_empty

            # Armazena no cache de memória
            with _memory_cache_lock:
                _memory_cache["data"][cache_key] = df
                _memory_cache["expiry"][cache_key] = current_time + expiry_seconds

            # Se tiver caminho de arquivo, salva no disco
            if cache_file_path and isinstance(df, pd.DataFrame):
                from ..trading.data_utils import is_data_empty

                if not is_data_empty(df):
                    try:
                        os.makedirs(cache_file_path, exist_ok=True)
                        full_path = os.path.join(cache_file_path, f"{file_key}.csv")
                        meta_path = os.path.join(cache_file_path, f"{file_key}.meta.json")

                        # Identifica colunas de data/hora
                        datetime_columns = [
                            col
                            for col in df.columns
                            if pd.api.types.is_datetime64_any_dtype(df[col])
                        ]

                        # Salva metadados
                        metadata = {
                            "created": time.time(),
                            "expiry": current_time + expiry_seconds,
                            "shape": df.shape,
                            "columns": df.columns.tolist(),
                            "datetime_columns": datetime_columns,
                        }

                        with open(meta_path, "w") as f:
                            json.dump(metadata, f, cls=JSONEncoder)

                        # Salva DataFrame
                        df.to_csv(full_path, index=False)
                        logger.debug(f"Saved to disk cache: {full_path}")

                    except Exception as e:
                        logger.warning(f"Error saving to disk cache: {str(e)}")

            # Registra metadados
            with _memory_cache_lock:
                if func_id not in _memory_cache["metadata"]:
                    _memory_cache["metadata"][func_id] = {
                        "hits": 0,
                        "misses": 0,
                        "items": 0,
                    }

                _memory_cache["metadata"][func_id]["misses"] += 1
                _memory_cache["metadata"][func_id]["items"] = sum(
                    1 for k in _memory_cache["data"] if k.startswith(func_id)
                )
                persist_metric_to_qpm("cache_hit", False)

            return df

        return wrapper

    return decorator


def clear_cache(prefix: Optional[str] = None) -> int:
    """
    Limpa o cache em memória.

    Args:
        prefix: Prefixo opcional para limpar seletivamente

    Returns:
        Número de itens removidos
    """
    count = 0
    keys_to_remove = []

    with _memory_cache_lock:
        # Identifica chaves para remover
        for key in list(_memory_cache["data"].keys()):
            if prefix is None or key.startswith(prefix):
                keys_to_remove.append(key)

        # Remove as chaves
        for key in keys_to_remove:
            del _memory_cache["data"][key]
            if key in _memory_cache["expiry"]:
                del _memory_cache["expiry"][key]
            count += 1

    logger.info(f"Cleared {count} items from cache")
    return count


def get_cache_stats() -> CacheStatistics:
    """
    Obtém estatísticas do cache.

    Returns:
        Dicionário com estatísticas do cache
    """
    with _memory_cache_lock:
        total_items = len(_memory_cache["data"])
        expired_items = sum(
            1 for key, expiry in _memory_cache["expiry"].items() if expiry < time.time()
        )

        return CacheStatistics(
            total_items=total_items,
            expired_items=expired_items,
            live_items=total_items - expired_items,
            functions=_memory_cache["metadata"],
        )


def cleanup_expired_cache() -> int:
    """
    Remove itens expirados do cache.

    Returns:
        Número de itens removidos
    """
    current_time = time.time()
    count = 0
    keys_to_remove = []

    with _memory_cache_lock:
        # Identifica chaves expiradas
        for key, expiry in list(_memory_cache["expiry"].items()):
            if expiry < current_time and key in _memory_cache["data"]:
                keys_to_remove.append(key)

        # Remove as chaves
        for key in keys_to_remove:
            if key in _memory_cache["data"]:
                del _memory_cache["data"][key]
            if key in _memory_cache["expiry"]:
                del _memory_cache["expiry"][key]
            count += 1

    logger.debug(f"Cleaned up {count} expired items from cache")
    return count


# Classes de compatibilidade com diferentes tipos de cache


class MemoryCache(CacheManager):
    """Cache somente em memória."""

    def __init__(
        self,
        expiry_seconds: int = 3600,
        max_size: Optional[int] = None,
        cache_type: Optional[str] = None,
        **kwargs,
    ) -> None:
        # Passa adiante parâmetros adicionais irrelevantes para este cache, garantindo compatibilidade
        super().__init__(
            cache_dir=None,
            expiry_seconds=expiry_seconds,
            cache_type=cache_type,
            **kwargs,
        )
        self.max_size = max_size
        self._order: List[str] = []

    def set(
        self,
        key: str,
        value: Any,
        expiry_seconds: Optional[int] = None,
        ttl: Optional[float] = None,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> None:
        super().set(
            key,
            value,
            expiry_seconds=expiry_seconds,
            ttl=ttl,
            key_args=key_args,
            key_kwargs=key_kwargs,
        )
        composite_key = self._compose_key(key, key_args, key_kwargs)
        self._order.append(composite_key)
        # Enforce LRU eviction
        if self.max_size is not None and len(self._order) > self.max_size:
            oldest = self._order.pop(0)
            self.invalidate(oldest)

    def remove(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> bool:
        composite_key = self._compose_key(key, key_args, key_kwargs)
        if composite_key in self._order:
            self._order.remove(composite_key)
        return super().remove(key, key_args=key_args, key_kwargs=key_kwargs)

    def clear(self, key_prefix: Optional[str] = None) -> int:
        count = super().clear(key_prefix)
        if key_prefix is None:
            self._order.clear()
        else:
            self._order = [k for k in self._order if not k.startswith(key_prefix)]
        return count


class DiskCache(CacheManager):
    """Cache somente em disco."""

    def __init__(
        self, cache_dir: Optional[str] = None, expiry_seconds: int = 3600, **kwargs
    ):
        # Importação local para evitar dependência circular durante a inicialização
        from ..config import config

        # Garante que o diretório de cache padrão seja obtido do ``config``
        super().__init__(
            cache_dir=cache_dir or config.cache_dir, expiry_seconds=expiry_seconds
        )

    def set(
        self,
        key: str,
        value: Any,
        expiry_seconds: Optional[int] = None,
        ttl: Optional[float] = None,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> None:
        # Armazena em memória e em disco (JSON) para qualquer tipo de valor
        expiry = (
            ttl
            if ttl is not None
            else (expiry_seconds if expiry_seconds is not None else self.default_expiry)
        )
        current_time = time.time()
        super().set(
            key,
            value,
            expiry_seconds=expiry_seconds,
            ttl=ttl,
            key_args=key_args,
            key_kwargs=key_kwargs,
        )
        composite_key = self._compose_key(key, key_args, key_kwargs)
        file_key = composite_key.replace(":", "_").replace("/", "_")
        json_path = os.path.join(self.cache_dir, f"{file_key}.json")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")
        try:
            serializable = convert_to_serializable(value)
            with open(json_path, "w", encoding="utf-8") as f:
                json.dump(serializable, f, cls=JSONEncoder)
            meta = {"expiry": current_time + expiry}
            with open(meta_path, "w", encoding="utf-8") as f:
                json.dump(meta, f)
        except Exception as e:
            logger.warning(f"Error saving to disk cache (json): {e}")

    def get(
        self,
        key: str,
        default: Optional[Any] = None,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> Any:
        """Recupera um valor do cache em disco, considerando expiração por meta."""
        composite_key = self._compose_key(key, key_args, key_kwargs)
        file_key = composite_key.replace(":", "_").replace("/", "_")
        json_path = os.path.join(self.cache_dir, f"{file_key}.json")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")
        current_time = time.time()
        if os.path.exists(json_path) and os.path.exists(meta_path):
            try:
                with open(meta_path, "r") as f:
                    meta = json.load(f)
                if meta.get("expiry", 0) > current_time:
                    with open(json_path, "r", encoding="utf-8") as f:
                        data = json.load(f)
                    # Recarrega em memória
                    with _memory_cache_lock:
                        _memory_cache["data"][composite_key] = data
                        _memory_cache["expiry"][composite_key] = (
                            current_time + self.default_expiry
                        )
                    return data
                else:
                    # Expirado: remove arquivos
                    os.remove(json_path)
                    os.remove(meta_path)
            except Exception as e:
                logger.warning(f"Error loading from disk cache (json): {e}")
        # Fallback para valores em memória ou DataFrame
        return super().get(
            key, default=default, key_args=key_args, key_kwargs=key_kwargs
        )

    def remove(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Remove o item do cache (memória, disco e arquivos persistidos)."""
        composite_key = self._compose_key(key, key_args, key_kwargs)
        # Remove memória e DataFrame files
        existed = super().remove(key, key_args=key_args, key_kwargs=key_kwargs)
        # Remove json e meta
        file_key = composite_key.replace(":", "_").replace("/", "_")
        json_path = os.path.join(self.cache_dir, f"{file_key}.json")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")
        try:
            if os.path.exists(json_path):
                os.remove(json_path)
            if os.path.exists(meta_path):
                os.remove(meta_path)
        except Exception as exc:
            logger.warning(
                "Failed to remove cached json files for key %s: %s", key, exc
            )
        return existed

    def has(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Verifica se o item existe e não expirou no cache em disco."""
        composite_key = self._compose_key(key, key_args, key_kwargs)
        file_key = composite_key.replace(":", "_").replace("/", "_")
        json_path = os.path.join(self.cache_dir, f"{file_key}.json")
        meta_path = os.path.join(self.cache_dir, f"{file_key}.meta.json")
        current_time = time.time()
        # Prioriza arquivo json com meta
        if os.path.exists(json_path) and os.path.exists(meta_path):
            try:
                with open(meta_path, "r") as f:
                    meta = json.load(f)
                if meta.get("expiry", 0) > current_time:
                    return True
                else:
                    # Expirado: remove arquivos e memória
                    os.remove(json_path)
                    os.remove(meta_path)
                    with _memory_cache_lock:
                        if composite_key in _memory_cache["data"]:
                            del _memory_cache["data"][composite_key]
                        if composite_key in _memory_cache["expiry"]:
                            del _memory_cache["expiry"][composite_key]
                    return False
            except Exception as e:
                logger.warning(f"Error checking disk cache TTL: {e}")
                return False
        # Fallback para memória ou DataFrame
        return super().has(key, key_args=key_args, key_kwargs=key_kwargs)

    def clear(self, key_prefix: Optional[str] = None) -> int:
        """Limpa cache em memória e arquivos persistidos no disco."""
        # Limpa memória
        count = super().clear(key_prefix)
        # Limpa arquivos persistentes
        try:
            for fname in os.listdir(self.cache_dir or ""):
                if (
                    key_prefix is None
                    or fname.startswith(key_prefix + "_")
                    or fname.startswith(key_prefix or "")
                ):
                    path = os.path.join(self.cache_dir, fname)
                    if os.path.isfile(path):
                        os.remove(path)
        except Exception as e:
            logger.warning(f"Error clearing disk cache files: {e}")
        return count


class TieredCache(CacheManager):
    """Cache em memória e disco combinado."""

    def __init__(
        self,
        memory_cache: CacheManager,
        disk_cache: CacheManager,
        memory_first: bool = True,
        **kwargs,
    ):
        # Utiliza instâncias existentes de cache de memória e disco
        self.memory_cache = memory_cache
        self.disk_cache = disk_cache
        self.memory_first = memory_first

    def set(
        self,
        key: str,
        value: Any,
        expiry_seconds: Optional[int] = None,
        ttl: Optional[float] = None,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> None:
        self.memory_cache.set(
            key,
            value,
            expiry_seconds=expiry_seconds,
            ttl=ttl,
            key_args=key_args,
            key_kwargs=key_kwargs,
        )
        try:
            self.disk_cache.set(
                key,
                value,
                expiry_seconds=expiry_seconds,
                ttl=ttl,
                key_args=key_args,
                key_kwargs=key_kwargs,
            )
        except Exception:
            logger.warning(f"Disk cache set failed for key: {key}")

    def get(
        self,
        key: str,
        default: Optional[Any] = None,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> Any:
        # Prioriza conforme configuração
        # Estatísticas de cache
        record = getattr(self, "collect_stats", False)
        if self.memory_first:
            val = self.memory_cache.get(
                key, default=None, key_args=key_args, key_kwargs=key_kwargs
            )
            if val is not None:
                if record:
                    self._hits += 1
                    persist_metric_to_qpm("cache_hit", True)
                return val
            val = self.disk_cache.get(
                key, default=None, key_args=key_args, key_kwargs=key_kwargs
            )
            if val is not None:
                if record:
                    self._hits += 1
                    persist_metric_to_qpm("cache_hit", True)
                self.memory_cache.set(
                    key, val, key_args=key_args, key_kwargs=key_kwargs
                )
                return val
        else:
            val = self.disk_cache.get(
                key, default=None, key_args=key_args, key_kwargs=key_kwargs
            )
            if val is not None:
                if record:
                    self._hits += 1
                    persist_metric_to_qpm("cache_hit", True)
                try:
                    self.memory_cache.set(
                        key, val, key_args=key_args, key_kwargs=key_kwargs
                    )
                except Exception as exc:
                    logger.warning(
                        "Failed to set memory cache for key %s: %s", key, exc
                    )
                return val
            val = self.memory_cache.get(
                key, default=None, key_args=key_args, key_kwargs=key_kwargs
            )
            if val is not None:
                if record:
                    self._hits += 1
                    persist_metric_to_qpm("cache_hit", True)
                return val
        # Miss
        if record:
            self._misses += 1
            persist_metric_to_qpm("cache_hit", False)
        return default

    def has(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> bool:
        return self.memory_cache.has(
            key, key_args=key_args, key_kwargs=key_kwargs
        ) or self.disk_cache.has(key, key_args=key_args, key_kwargs=key_kwargs)

    def remove(
        self,
        key: str,
        key_args: Optional[List[Any]] = None,
        key_kwargs: Optional[Dict[str, Any]] = None,
    ) -> bool:
        mem_removed = self.memory_cache.remove(
            key, key_args=key_args, key_kwargs=key_kwargs
        )
        disk_removed = self.disk_cache.remove(
            key, key_args=key_args, key_kwargs=key_kwargs
        )
        return mem_removed or disk_removed

    def clear(self, key_prefix: Optional[str] = None) -> int:
        count_mem = self.memory_cache.clear(key_prefix)
        count_disk = self.disk_cache.clear(key_prefix)
        return count_mem + count_disk
