from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Dict, Optional, Sequence

from .event_bus import Simple<PERSON>ventBus
from .quantum_pattern_memory import QuantumPatternMemory
from ..common_types import QuantumSignaturePacket
from ..utils.logger import get_logger
from typing import TYPE_CHECKING

if TYPE_CHECKING:  # pragma: no cover - for type hints
    from qualia.market.event_bus import MarketPatternDetected

MARKET_PATTERN_EVENT = "market.pattern_detected"

logger = get_logger(__name__)

STORE_EVENT = "qpm.store_pattern"


@dataclass
class StorePatternEvent:
    """Payload for :data:`STORE_EVENT`."""

    vector: Sequence[float]
    metrics: Optional[Dict[str, Any]] = None
    market_snapshot: Optional[Dict[str, Any]] = None
    outcome: Optional[Dict[str, Any]] = None
    decision_context: Optional[Dict[str, Any]] = None


def publish_store_pattern(
    event_bus: SimpleEventBus,
    vector: Sequence[float],
    *,
    metrics: Optional[Dict[str, Any]] = None,
    market_snapshot: Optional[Dict[str, Any]] = None,
    outcome: Optional[Dict[str, Any]] = None,
    decision_context: Optional[Dict[str, Any]] = None,
) -> None:
    """Publish a pattern to be stored in :class:`QuantumPatternMemory`.

    Parameters
    ----------
    event_bus : SimpleEventBus
        Bus instance used to publish the event.
    vector : Sequence[float]
        Vector representing the pattern.
    metrics : dict, optional
        Additional metrics for :class:`QuantumSignaturePacket`.
    market_snapshot : dict, optional
        Market snapshot accompanying the pattern.
    outcome : dict, optional
        Outcome metadata describing the result of the decision.
    decision_context : dict, optional
        Additional context for the decision.
    """

    payload = StorePatternEvent(
        vector=list(vector),
        metrics=metrics or {},
        market_snapshot=market_snapshot or {},
        outcome=outcome or {},
        decision_context=decision_context,
    )
    event_bus.publish(STORE_EVENT, payload)


def register_store_handler(
    qpm: QuantumPatternMemory,
    event_bus: SimpleEventBus,
    event_name: str = STORE_EVENT,
) -> None:
    """Subscribe ``qpm`` to store events published on ``event_bus``."""

    def _handler(payload: Any) -> None:
        if isinstance(payload, StorePatternEvent):
            vector = payload.vector
            metrics = payload.metrics or {}
            market_snapshot = payload.market_snapshot or {}
            outcome = payload.outcome or {}
            decision_context = payload.decision_context
        elif isinstance(payload, dict):
            vector = payload.get("vector")
            metrics = payload.get("metrics", {})
            market_snapshot = payload.get("market_snapshot", {})
            outcome = payload.get("outcome", {})
            decision_context = payload.get("decision_context")
        else:
            logger.warning("QPMInterface: payload inválido %r", payload)
            return

        if vector is None:
            logger.warning("QPMInterface: evento sem vetor recebido")
            return
        packet = QuantumSignaturePacket(vector=list(vector), metrics=metrics)
        try:
            qpm.store_pattern(
                packet,
                market_snapshot or {},
                outcome or {},
                decision_context,
            )
        except Exception as exc:  # pragma: no cover - defensive
            logger.exception("QPMInterface: falha ao armazenar padrão", exc_info=exc)

    event_bus.subscribe(event_name, _handler)


def register_market_pattern_handler(
    qpm: QuantumPatternMemory,
    event_bus: SimpleEventBus,
    event_name: str = MARKET_PATTERN_EVENT,
) -> None:
    """Subscribe ``qpm`` to ``market.pattern_detected`` events."""

    def _handler(payload: Any) -> None:
        from qualia.market.event_bus import MarketPatternDetected

        if isinstance(payload, MarketPatternDetected):
            vector = payload.vector
            metadata = payload.metadata or {}
        elif isinstance(payload, dict):
            vector = payload.get("vector")
            metadata = payload.get("metadata", {})
        else:
            vector = getattr(payload, "vector", None)
            metadata = getattr(payload, "metadata", {}) or {}
        if vector is None:
            logger.warning("QPMInterface: evento de mercado sem vetor")
            return
        packet = QuantumSignaturePacket(vector=list(vector), metrics={})
        try:
            qpm.store_pattern(
                packet,
                metadata.get("market_snapshot", {}),
                metadata.get("outcome", {}),
                metadata.get("decision_context"),
                extra_metadata=metadata.get("extra_metadata"),
            )
        except Exception as exc:  # pragma: no cover - defensive
            logger.exception(
                "QPMInterface: falha ao armazenar padrão de mercado",
                exc_info=exc,
            )

    event_bus.subscribe(event_name, _handler)


def register_market_pattern_handler_async(
    qpm: QuantumPatternMemory,
    event_bus: AsyncEventBus,
    event_name: str = MARKET_PATTERN_EVENT,
) -> None:
    """Subscribe ``qpm`` to ``market.pattern_detected`` on an async bus."""

    async def _handler(payload: Any) -> None:
        from qualia.market.event_bus import MarketPatternDetected

        if isinstance(payload, MarketPatternDetected):
            vector = payload.vector
            metadata = payload.metadata or {}
        elif isinstance(payload, dict):
            vector = payload.get("vector")
            metadata = payload.get("metadata", {})
        else:
            vector = getattr(payload, "vector", None)
            metadata = getattr(payload, "metadata", {}) or {}
        if vector is None:
            logger.warning("QPMInterface: evento de mercado sem vetor")
            return
        packet = QuantumSignaturePacket(vector=list(vector), metrics={})
        try:
            qpm.store_pattern(
                packet,
                metadata.get("market_snapshot", {}),
                metadata.get("outcome", {}),
                metadata.get("decision_context"),
                extra_metadata=metadata.get("extra_metadata"),
            )
        except Exception as exc:  # pragma: no cover - defensive
            logger.exception(
                "QPMInterface: falha ao armazenar padrão de mercado",
                exc_info=exc,
            )

    event_bus.subscribe(event_name, _handler)


__all__ = [
    "STORE_EVENT",
    "StorePatternEvent",
    "publish_store_pattern",
    "register_store_handler",
    "register_market_pattern_handler",
    "register_market_pattern_handler_async",
]
