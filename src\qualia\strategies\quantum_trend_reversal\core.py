"""
Estratégia de Reversão de Tendência Quântica

Implementa uma estratégia que identifica possíveis reversões
de tendência usando circuitos quânticos para analisar padrões
de entanglement e equilíbrio de fases.

Se o pacote ``qiskit`` não estiver disponível, a estratégia opera
de forma degradada, retornando métricas neutras e registrando um
aviso no logger.
"""

from __future__ import annotations

from ...utils.logger import get_logger
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, TYPE_CHECKING
from ...config.config_manager import ConfigManager

import numpy as np
import pandas as pd
from ..strategy_utils import make_signal_df
from numpy.typing import ArrayLike

from ...strategies.strategy_interface import (
    TradingStrategy,
    register_strategy,
)

try:  # noqa: WPS501 - Import dentro do bloco de try para fallback
    from qiskit.quantum_info import Statevector
    from qiskit import QuantumCircuit
    from qiskit.quantum_info import Operator, DensityMatrix

    HAS_QISKIT = True
except ImportError:  # pragma: no cover - Ambiente sem Qiskit
    HAS_QISKIT = False

if TYPE_CHECKING:
    from ...market.quantum_metrics_calculator import QuantumMetricsCalculator
    from ...strategies.strategy_interface import TradingContext

from .indicators import (
    calculate_adx,
    calculate_sma,
    find_support_resistance,
    identify_candle_patterns,
)
from .risk import (
    position_sizing as risk_position_sizing,
    risk_management as apply_risk_management,
)

logger = get_logger(__name__)


@register_strategy(
    # Register the strategy by its full class name as well as the alias
    name="QuantumTrendReversalStrategy",
    category="quantum_enhanced",
    description=(
        "Estratégia de reversão de tendência baseada em análise "
        "quântica de desequilíbrio de fases"
    ),
    version="1.0.0",
    tags=["q_reversal", "qrev"],
    legacy=True,
)
class QuantumTrendReversalStrategy(TradingStrategy):
    """
    Estratégia que identifica possíveis pontos de reversão de tendência.

    Esta estratégia utiliza circuitos quânticos para detectar padrões que indicam
    esgotamento de momentum e possíveis reversões através de:

    1. Análise de entanglement entre preços e volume
    2. Detecção de desequilíbrio de fases entre diferentes períodos
    3. Cálculo de entropia quântica para estimar incerteza de mercado

    Combina-se análise de tendência clássica com interferência quântica para
    identificar pontos ótimos para operações contrárias à tendência.
    """

    # Alias para registro alternativo
    strategy_alias = "q_reversal"

    # Anotação de tipo para o atributo de instância
    quantum_calculator: "QuantumMetricsCalculator"
    initialized: bool  # Também anotar initialized que já existe

    # Parâmetros com valores padrão e tipos (já parecem bem anotados na
    # definição do __init__)
    lookback_period: int
    reversal_threshold: float
    trend_strength_min: float
    qubits: int
    circuit_depth: int
    quantum_weight: float
    use_volume: bool
    consolidation_bars: int

    def __init__(
        self,
        lookback_period: int = 20,
        reversal_threshold: float = 0.7,
        trend_strength_min: float = 0.4,
        qubits: int = 8,
        circuit_depth: int = 3,
        quantum_weight: float = 0.6,
        use_volume: bool = True,
        consolidation_bars: int = 3,
        raise_on_ctc_failure: bool = False,
        debug_ctc: bool = False,
        symbol: Optional[str] = None,
        timeframe: Optional[str] = None,
        config_manager: Optional[ConfigManager] = None,
    ):
        """
        Inicializa a estratégia com os parâmetros especificados.

        Args:
            lookback_period: Período para análise de tendência
            reversal_threshold: Limiar para sinais de reversão (0.0-1.0)
            trend_strength_min: Força mínima da tendência para considerar reversão
            qubits: Número de qubits para cálculos quânticos
            circuit_depth: Profundidade do circuito quântico
            quantum_weight: Peso das métricas quânticas vs. clássicas
            use_volume: Usar volume nas análises quânticas
            consolidation_bars: Número mínimo de barras de consolidação
            raise_on_ctc_failure: Re-lançar exceções de CTC ao invés de ignorar
            debug_ctc: Exibir traceback completo em falhas de CTC
        """
        super().__init__(
            context={"symbol": symbol, "timeframe": timeframe},
            config_manager=config_manager,
        )
        self.symbol = symbol
        self.timeframe = timeframe

        self.lookback_period = lookback_period
        self.reversal_threshold = reversal_threshold
        self.trend_strength_min = trend_strength_min
        self.qubits = qubits
        self.circuit_depth = circuit_depth
        self.quantum_weight = quantum_weight
        self.use_volume = use_volume
        self.consolidation_bars = consolidation_bars
        self.raise_on_ctc_failure = raise_on_ctc_failure
        self.debug_ctc = debug_ctc
        self.ctc_failed = False

        # Nome utilizado em logs e identificação
        self.name = "QuantumTrendReversalStrategy"

        # Componentes que serão inicializados posteriormente
        self.quantum_calculator = None
        self.initialized = False

        if not HAS_QISKIT:
            logger.warning(
                "Qiskit não está instalado. Funções quânticas serão desabilitadas."
            )

    def get_parameters(self) -> Dict[str, Any]:
        """
        Retorna os parâmetros atuais da estratégia Quantum Trend Reversal.

        Returns:
            Dicionário contendo os parâmetros configuráveis da estratégia.
        """
        return {
            "lookback_period": self.lookback_period,
            "reversal_threshold": self.reversal_threshold,
            "trend_strength_min": self.trend_strength_min,
            "qubits": self.qubits,
            "circuit_depth": self.circuit_depth,
            "quantum_weight": self.quantum_weight,
            "use_volume": self.use_volume,
            "consolidation_bars": self.consolidation_bars,
            "raise_on_ctc_failure": self.raise_on_ctc_failure,
            "debug_ctc": self.debug_ctc,
        }

    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        Define novos parâmetros para a estratégia Quantum Trend Reversal.

        Args:
            parameters: Dicionário contendo os novos parâmetros a serem aplicados.
        """
        if "lookback_period" in parameters:
            self.lookback_period = parameters["lookback_period"]
        if "reversal_threshold" in parameters:
            self.reversal_threshold = parameters["reversal_threshold"]
        if "trend_strength_min" in parameters:
            self.trend_strength_min = parameters["trend_strength_min"]
        if "qubits" in parameters:
            self.qubits = parameters["qubits"]
        if "circuit_depth" in parameters:
            self.circuit_depth = parameters["circuit_depth"]
        if "quantum_weight" in parameters:
            self.quantum_weight = parameters["quantum_weight"]
        if "use_volume" in parameters:
            self.use_volume = parameters["use_volume"]
        if "consolidation_bars" in parameters:
            self.consolidation_bars = parameters["consolidation_bars"]
        if "raise_on_ctc_failure" in parameters:
            self.raise_on_ctc_failure = parameters["raise_on_ctc_failure"]
        if "debug_ctc" in parameters:
            self.debug_ctc = parameters["debug_ctc"]

        # Atualizar configuração do QuantumMetricsCalculator existente
        if (
            "qubits" in parameters
            and self.initialized
            and self.quantum_calculator is not None
            and hasattr(self.quantum_calculator, "qualia_universe")
        ):
            logger.info(
                "Atualizando número de qubits do QuantumMetricsCalculator para %s",
                self.qubits,
            )
            try:
                self.quantum_calculator.qualia_universe.n_qubits = self.qubits
            except Exception as exc:  # pragma: no cover - falha inesperada
                logger.warning(
                    "Falha ao atualizar n_qubits do QuantumMetricsCalculator: %s",
                    exc,
                )

    def get_template_config(self) -> dict:
        """Retorna a configuração template da estratégia para o QAST."""

        return {
            "class_name": self.__class__.__name__,
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "params": self.get_parameters(),
        }

    def get_base_strategy(self) -> dict:
        """Retorna o template usado como base para evolução."""

        return self.get_template_config()

    def get_params(self) -> dict:
        """Alias compatível para ``get_parameters``."""

        return self.get_parameters()

    def set_params(self, params: dict) -> None:
        """Alias compatível para ``set_parameters``."""

        self.set_parameters(params)

    def initialize(self, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Inicializa a estratégia com parâmetros e recursos necessários.

        Args:
            context: Dicionário com parâmetros e recursos para inicialização
        """
        logger.info(
            f"Inicializando estratégia Quantum Trend Reversal com {self.qubits} qubits"
        )

        if context:
            self.set_parameters(context)
            if "quantum_metrics_calculator" in context:
                self.quantum_calculator = context["quantum_metrics_calculator"]
            elif "qualia_universe" in context and self.quantum_calculator is None:
                from ...market.quantum_metrics_calculator import (
                    QuantumMetricsCalculator,
                )

                self.quantum_calculator = QuantumMetricsCalculator(
                    qualia_universe=context["qualia_universe"]
                )

        if not HAS_QISKIT:
            self.initialized = True
            logger.warning(
                "Inicialização degradada: Qiskit ausente, recursos quânticos desativados."
            )
            return

        if context and "use_cache" in context and self.quantum_calculator is not None:
            self.quantum_calculator.use_cache = context["use_cache"]

        self.initialized = True
        logger.info("Estratégia Quantum Trend Reversal inicializada com sucesso")

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        trading_context: "TradingContext",
        quantum_metrics: Optional[Dict[str, Any]] = None,
        similar_past_patterns: Optional[List[Dict[str, Any]]] = None,
        common_indicators: Optional[Dict[str, Any]] = None,
        *,
        context: "TradingContext" | None = None,
    ) -> Dict[str, Any]:
        """Analisa os dados de mercado em busca de padrões de reversão.

        Parameters
        ----------
        market_data : pandas.DataFrame
            Dados OHLCV utilizados na análise.
        trading_context : TradingContext
            Contexto atual de trading com informações adicionais do mercado.
        context : TradingContext, optional
            Alias legado para ``trading_context``.
        quantum_metrics : dict, optional
            Métricas quânticas pré-calculadas que, se fornecidas, evitam novo
            cálculo.
        similar_past_patterns : list of dict, optional
            Padrões históricos similares recuperados da QPM.
        common_indicators : dict, optional
            Indicadores clássicos já calculados e compartilhados entre
            estratégias.

        Returns
        -------
        dict
            Dicionário com resultados da análise.
        """
        trading_context = trading_context or context
        if not self.initialized:
            raise ValueError(
                "Estratégia não inicializada. Chame initialize() primeiro."
            )

        if len(market_data) < self.lookback_period + 10:
            raise ValueError(
                f"Dados insuficientes. Mínimo: {self.lookback_period + 10} barras"
            )

        # Análise clássica de tendência
        closes = market_data["close"].to_numpy()
        highs = market_data["high"].to_numpy()
        lows = market_data["low"].to_numpy()
        volumes = (
            market_data["volume"].to_numpy()
            if "volume" in market_data.columns and self.use_volume
            else None
        )

        # 1. Indicadores clássicos de tendência
        # -- ADX (Average Directional Index)
        adx = self._calculate_adx(highs, lows, closes, period=14)

        # -- Média móvel simples de diferentes períodos
        sma20 = self._calculate_sma(closes, 20)
        sma50 = self._calculate_sma(closes, 50)

        # -- Suporte e resistência dinâmicos
        support, resistance = self._find_support_resistance(
            highs, lows, closes, n=self.lookback_period
        )

        # -- Identificar padrões de velas
        candle_patterns = self._identify_candle_patterns(
            opens=market_data["open"].to_numpy(), highs=highs, lows=lows, closes=closes
        )

        # 2. Análise quântica
        quantum_results: Dict[str, Any] = {}

        if HAS_QISKIT and self.quantum_calculator is not None:
            # -- Métricas quânticas padrão
            if quantum_metrics is None:
                try:
                    # Preparar dados de mercado para métricas quânticas: usar timeframe 'lookback'
                    market_data_for_quantum = {
                        "strategy": {
                            "lookback": {"close": closes[-self.lookback_period :]}
                        }
                    }
                    if self.use_volume and volumes is not None:
                        market_data_for_quantum["strategy"]["lookback"][
                            "volume"
                        ] = volumes[-self.lookback_period :]
                    quantum_metrics = self.quantum_calculator.calculate_metrics(
                        market_data_for_quantum
                    )
                except Exception as e:
                    logger.warning(f"Erro ao calcular métricas quânticas padrão: {e}")
                    quantum_metrics = {}
            quantum_results["standard_metrics"] = quantum_metrics or {}

            # -- Análise de fase e interferência quântica (usando grover_circuit da factory)
            try:
                phase_analysis = self._analyze_quantum_phases(
                    closes, n=self.lookback_period
                )
                quantum_results["phase_analysis"] = phase_analysis
            except Exception as e:
                logger.warning(f"Erro na análise de fases quânticas: {e}")
                quantum_results["phase_analysis"] = {
                    "error": str(e),
                    "phase_imbalance": 0.5,
                }
        else:
            logger.warning(
                "Qiskit ausente ou calculador não inicializado. Usando métricas quânticas neutras."
            )
            quantum_results["standard_metrics"] = quantum_metrics or {}
            quantum_results["phase_analysis"] = {
                "phase_imbalance": 0.0,
                "reversal_direction": 0,
                "temporal_imbalance": 0.0,
            }

        # -- Detecção de padrões quânticos (utilizando algoritmo QAST do QUALIA)
        try:
            qast_result = self._detect_qast_patterns(closes, volumes)
            quantum_results["qast_patterns"] = qast_result
        except Exception as e:
            logger.warning(f"Erro na detecção de padrões QAST: {e}")
            quantum_results["qast_patterns"] = {
                "error": str(e),
                "pattern_strength": 0.0,
            }

        # 3. Avaliação de tendência atual
        trend_direction, trend_strength = self._evaluate_trend(
            closes, sma20, sma50, adx
        )

        # 4. Detecção de níveis-chave
        distance_to_support = (
            (closes[-1] - support) / closes[-1] if support > 0 else np.inf
        )
        distance_to_resistance = (
            (resistance - closes[-1]) / closes[-1] if resistance > 0 else np.inf
        )

        # Criar séries temporais para retorno
        timestamps = market_data.index[-self.lookback_period :]

        # Construir resultado
        return {
            "trend_direction": trend_direction,  # 1 = alta, -1 = baixa, 0 = lateral
            "trend_strength": trend_strength,  # 0.0 a 1.0
            "adx": adx[-10:],  # últimos 10 valores
            "candle_patterns": candle_patterns,
            "support": support,
            "resistance": resistance,
            "distance_to_support": distance_to_support,
            "distance_to_resistance": distance_to_resistance,
            "quantum_results": quantum_results,
            "last_price": closes[-1],
            "close_series": pd.Series(
                closes[-self.lookback_period :], index=timestamps
            ),
            "timestamps": timestamps,
        }

    def generate_signals(self, analysis_result: Dict[str, Any]) -> pd.DataFrame:
        """
        Gera sinais de trading com base nos resultados da análise.

        Args:
            analysis_result: Resultados da análise de mercado

        Returns:
            DataFrame com sinais de trading
        """
        if not self.initialized:
            raise ValueError(
                "Estratégia não inicializada. Chame initialize() primeiro."
            )

        # Extrair resultados da análise
        trend_direction = analysis_result["trend_direction"]
        trend_strength = analysis_result["trend_strength"]
        support = analysis_result["support"]
        resistance = analysis_result["resistance"]
        distance_to_support = analysis_result["distance_to_support"]
        distance_to_resistance = analysis_result["distance_to_resistance"]
        candle_patterns = analysis_result["candle_patterns"]
        last_price = analysis_result["last_price"]
        timestamps = analysis_result["timestamps"]

        # Extrair métricas quânticas
        quantum_results = analysis_result["quantum_results"]
        quantum_metrics = quantum_results.get("standard_metrics", {})
        phase_analysis = quantum_results.get("phase_analysis", {})
        qast_patterns = quantum_results.get("qast_patterns", {})

        # Verificar força mínima da tendência
        if trend_strength < self.trend_strength_min:
            logger.debug(
                f"Força da tendência {trend_strength:.4f} abaixo do mínimo {self.trend_strength_min}"
            )
            return pd.DataFrame()  # Tendência fraca demais para reversão

        # Calcular pontuação de reversão clássica
        classic_reversal_score = self._calculate_classic_reversal_score(
            trend_direction,
            candle_patterns,
            distance_to_support,
            distance_to_resistance,
        )

        # Calcular pontuação de reversão quântica
        quantum_reversal_score = self._calculate_quantum_reversal_score(
            quantum_metrics, phase_analysis, qast_patterns
        )

        # Combinar pontuações com peso
        combined_score = (
            classic_reversal_score * (1 - self.quantum_weight)
            + quantum_reversal_score * self.quantum_weight
        )

        # Gerar sinal baseado na pontuação combinada
        signal = None
        confidence = combined_score

        if confidence > self.reversal_threshold:
            # Reversão contra a tendência atual
            if trend_direction > 0:  # Tendência de alta -> Sinal de venda
                signal = "sell"
            elif trend_direction < 0:  # Tendência de baixa -> Sinal de compra
                signal = "buy"

        # Criar DataFrame de sinal
        if signal:
            logger.info(f"Sinal de reversão: {signal} com confiança {confidence:.4f}")

            # Calcular stop loss e take profit baseados em suporte/resistência
            if signal == "buy":
                stop_loss = min(
                    support * 0.99, last_price * 0.97
                )  # Abaixo do suporte ou 3%
                take_profit = last_price * (
                    1 + 2 * (last_price - stop_loss) / last_price
                )  # 1:2 ratio
            else:  # sell
                stop_loss = max(
                    resistance * 1.01, last_price * 1.03
                )  # Acima da resistência ou 3%
                take_profit = last_price * (
                    1 - 2 * (stop_loss - last_price) / last_price
                )  # 1:2 ratio

            signals_df = make_signal_df(
                signal,
                confidence,
                price=last_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                trend_direction=trend_direction,
                trend_strength=trend_strength,
                classic_score=classic_reversal_score,
                quantum_score=quantum_reversal_score,
            )
            signals_df.index = pd.Index([timestamps[-1]], name="timestamp")

            return signals_df

        return pd.DataFrame()  # Retornar DataFrame vazio se não houver sinal

    def position_sizing(
        self, signals: pd.DataFrame, capital: float, risk_per_trade: float
    ) -> pd.DataFrame:
        """Delegate to :func:`risk_position_sizing`."""
        return risk_position_sizing(self, signals, capital, risk_per_trade)

    def risk_management(
        self, signals: pd.DataFrame, market_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Delegate to :func:`apply_risk_management`."""
        return apply_risk_management(self, signals, market_data)

    def _calculate_sma(self, data: np.ndarray, period: int) -> np.ndarray:
        """Wrapper around :func:`calculate_sma`."""
        return calculate_sma(data, period)

    def _calculate_adx(
        self, high: ArrayLike, low: ArrayLike, close: ArrayLike, period: int = 14
    ) -> np.ndarray:
        """Wrapper around :func:`calculate_adx`."""
        return calculate_adx(high, low, close, period)

    def _find_support_resistance(
        self, high: np.ndarray, low: np.ndarray, close: np.ndarray, n: int = 20
    ) -> Tuple[float, float]:
        """Wrapper around :func:`find_support_resistance`."""
        return find_support_resistance(high, low, close, n)

    def _identify_candle_patterns(
        self, opens: np.ndarray, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray
    ) -> Dict[str, Any]:
        """Wrapper around :func:`identify_candle_patterns`."""
        return identify_candle_patterns(opens, highs, lows, closes)

    def _analyze_quantum_phases(
        self, prices: np.ndarray, n: int = 20
    ) -> Dict[str, Any]:
        """
        Analisa o desequilíbrio de fases quânticas e temporal (CTC).
        """
        self.ctc_failed = False
        if not HAS_QISKIT:
            logger.warning(
                "Qiskit não disponível. _analyze_quantum_phases retornará valores neutros."
            )
            return {
                "phase_imbalance": 0.0,
                "reversal_direction": 0,
                "temporal_imbalance": 0.0,
                "error": "Qiskit missing",
            }

        if not self.initialized or self.quantum_calculator is None:
            return {
                "phase_imbalance": 0.0,
                "reversal_direction": 0,
                "temporal_imbalance": 0.0,
                "error": "Not initialized",
            }

        try:
            recent_prices = prices[-n:]
            if len(recent_prices) < 2:
                return {
                    "phase_imbalance": 0.0,
                    "reversal_direction": 0,
                    "temporal_imbalance": 0.0,
                    "error": "Insufficient recent prices",
                }

            # Normalização robusta de preços
            min_val = np.min(recent_prices)
            max_val = np.max(recent_prices)

            if np.isclose(min_val, max_val):  # Preços constantes ou quase constantes
                # Para preços constantes, podemos definir um estado neutro ou pular a análise.
                # Usaremos um estado que leva a um psi_cr como [1/sqrt(2),
                # 1/sqrt(2)] após processamento.
                normalized_prices = np.array([0.5] * len(recent_prices))
            else:
                normalized_prices = (recent_prices - min_val) / (max_val - min_val)

            # Garantir que normalized_prices são finitos e não negativos para psi_cr
            # Pegar os últimos dois ou usar default se não houver suficientes
            # após normalização
            if len(normalized_prices) >= 2:
                psi_input_values = normalized_prices[-2:]
            else:
                psi_input_values = np.array([0.5, 0.5])  # Default

            # Processar inputs para Statevector: garantir não-negatividade e finitude
            # e que não sejam ambos zero.
            amplitudes_sqrt_inputs = []
            for x in psi_input_values:
                if not np.isfinite(x) or x < 0:
                    # Substituir inválidos por 0 para sqrt
                    amplitudes_sqrt_inputs.append(0.0)
                else:
                    amplitudes_sqrt_inputs.append(x)

            # Se a soma dos inputs para sqrt for próxima de zero (ex: ambos são 0), usar default
            # para evitar Statevector([0,0])
            if np.sum(amplitudes_sqrt_inputs) < 1e-9:
                final_amplitudes_for_sv = np.array([1 / np.sqrt(2), 1 / np.sqrt(2)])
            else:
                # A lógica original era np.sqrt(p), assumindo que p é proxy de probabilidade.
                # Qiskit Statevector normaliza o input. Ex: Statevector([a,b]) -> [a/N, b/N] onde N=sqrt(|a|^2+|b|^2)
                # Se passarmos [sqrt(p1), sqrt(p2)], e p1+p2=1, já está normalizado.
                # Se passarmos [v1,v2] onde v1,v2 são valores, ele normaliza.
                # Para manter a intenção de sqrt(prob_proxy), aplicamos sqrt e
                # deixamos o Qiskit normalizar.
                final_amplitudes_for_sv = np.sqrt(np.array(amplitudes_sqrt_inputs))
                # Verificar se após sqrt ainda temos [0,0]
                if np.sum(np.abs(final_amplitudes_for_sv)) < 1e-9:
                    final_amplitudes_for_sv = np.array([1 / np.sqrt(2), 1 / np.sqrt(2)])

            psi_cr = Statevector(final_amplitudes_for_sv)

            # --------------------------------------------------------------
            # Construção real de um circuito de QFT para análise de fases
            # --------------------------------------------------------------
            try:
                from qiskit.circuit.library import QFT
                from qiskit_aer import Aer
                from qiskit import transpile

                qft_circuit = QuantumCircuit(self.qubits)
                for idx, value in enumerate(normalized_prices[-self.qubits :]):
                    qft_circuit.ry(float(value) * np.pi, idx)

                qft_circuit.compose(QFT(self.qubits), inplace=True)

                simulator = Aer.get_backend("statevector_simulator")
                transpiled = transpile(qft_circuit, simulator)
                qft_state = simulator.run(transpiled).result().get_statevector()
                phases = np.angle(qft_state)
                qft_variance = np.var(phases)
                qft_depth = qft_circuit.depth()
            except Exception as qft_err:  # pragma: no cover - fallback path
                logger.warning("Erro ao construir circuito QFT: %s", qft_err)
                phases = np.array([0.0])
                qft_variance = 0.0
                qft_depth = 0

            # Recalcular com normalized_prices robusto
            long_trend = np.mean(normalized_prices)
            recent_momentum = (
                np.mean(normalized_prices[-5:])
                if len(normalized_prices) >= 5
                else long_trend
            )
            trend_delta = abs(recent_momentum - long_trend)
            trend_component = min(1.0, trend_delta * 10)
            qft_component = min(1.0, qft_variance / (np.pi**2))
            phase_imbalance = float(
                np.clip((trend_component + qft_component) / 2, 0.0, 1.0)
            )
            reversal_direction = -1 if recent_momentum > long_trend else 1

            # 5. Integrar análise temporal com Deutsch CTC
            temporal_imbalance = 0.0
            try:
                from ...core.deutsch_ctc import solve_deutsch_ctc

                # from qiskit.quantum_info import Statevector # Já importado e
                # psi_cr criado
                from qiskit.quantum_info import Operator

                # qc_interaction e U_test como antes
                # CR qubit (0), CTC qubit (1)
                qc_interaction = QuantumCircuit(2)
                qc_interaction.cx(0, 1)
                U_test = Operator(qc_interaction).data

                rho_ctc_solution = solve_deutsch_ctc(
                    # MODIFICADO: Convertido psi_cr para DensityMatrix
                    DensityMatrix(psi_cr),
                    U_test,
                    num_cr_qubits=1,
                    num_ctc_qubits=1,
                    verbose=False,
                )
                if rho_ctc_solution is not None:
                    data = getattr(rho_ctc_solution, "data", rho_ctc_solution)
                    # Adicionar verificação para shape de data antes de
                    # np.trace
                    if isinstance(data, np.ndarray) and data.shape == (2**1, 2**1):
                        temporal_imbalance = float(abs(np.trace(data) - 1.0))
                    else:
                        logger.warning(
                            f"Simulação CTC retornou dados inesperados: {type(data)}, shape {getattr(data, 'shape', 'N/A')}. Imbalanço temporal não calculado."
                        )
                        temporal_imbalance = 0.0  # ou algum valor de erro/default
                else:
                    logger.info("Simulação CTC não convergiu ou retornou None.")

            except Exception as err_ctc:
                self.ctc_failed = True
                if self.debug_ctc:
                    logger.exception("Erro na simulação CTC", exc_info=True)
                else:
                    logger.warning(f"Ignorando erro na simulação CTC: {err_ctc}")
                if self.raise_on_ctc_failure:
                    raise

            circuit_depth = qft_depth
            return {
                "phase_imbalance": phase_imbalance,
                "reversal_direction": reversal_direction,
                "temporal_imbalance": temporal_imbalance,
                "circuit_depth": circuit_depth,
            }

        except Exception as e:
            logger.error(f"Erro na análise de fases quânticas: {e}", exc_info=True)
            if self.raise_on_ctc_failure and self.ctc_failed:
                raise
            return {
                "phase_imbalance": 0.0,
                "reversal_direction": 0,
                "temporal_imbalance": 0.0,
                "error": str(e),
            }

    def _detect_qast_patterns(
        self, prices: np.ndarray, volumes: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """
        Detecta padrões usando o algoritmo QAST (Quantum Algorithmic Symbolic Transformation).

        Args:
            prices: Array de preços
            volumes: Array opcional de volumes

        Returns:
            Dicionário com padrões detectados e suas pontuações
        """
        # Implementação simplificada - em produção usaria a função qast_symbolic_oracle
        # e outros algoritmos quânticos do sistema QUALIA
        result = {}

        try:
            # Preparar dados
            n = min(len(prices), 30)  # Limitar a 30 pontos para análise
            recent_prices = prices[-n:]

            # Normalizar
            norm_prices = (recent_prices - np.min(recent_prices)) / (
                np.max(recent_prices) - np.min(recent_prices)
            )

            # Calcular métricas de tendência/padrão
            diffs = np.diff(norm_prices)

            # Métricas básicas
            volatility = np.std(diffs)
            direction = np.sign(np.sum(diffs))

            # Detectar padrões de aceleração/desaceleração (mudanças na
            # derivada)
            acceleration = np.diff(diffs)
            avg_accel = np.mean(acceleration)

            # Simular detecção de padrões específicos
            # Padrão de esgotamento (diminuição de momentum perto de extremos)
            exhaustion_pattern = 0.0
            if len(norm_prices) >= 10:
                # Verificar se preços estão em extremos
                is_extreme = (norm_prices[-1] > 0.8) or (norm_prices[-1] < 0.2)

                # Verificar se aceleração está diminuindo
                is_slowing = abs(np.mean(acceleration[-3:])) < abs(
                    np.mean(acceleration[-10:-3])
                )

                if is_extreme and is_slowing:
                    exhaustion_pattern = min(1.0, 2.0 * abs(np.mean(acceleration[-3:])))

            # Pattern strength (baseado em quão forte são os padrões)
            pattern_strength = min(
                1.0, exhaustion_pattern + 0.3 * abs(avg_accel) / volatility
            )

            result = {
                "pattern_strength": pattern_strength,
                "exhaustion_pattern": exhaustion_pattern,
                "direction": direction,
                "volatility": volatility,
            }

        except Exception as e:
            logger.error(f"Erro na detecção de padrões QAST: {e}")
            result = {"pattern_strength": 0.0, "error": str(e)}

        return result

    def _evaluate_trend(
        self, prices: np.ndarray, sma20: np.ndarray, sma50: np.ndarray, adx: np.ndarray
    ) -> Tuple[int, float]:
        """
        Avalia a direção e força da tendência atual.

        Args:
            prices: Array de preços
            sma20: Média móvel de 20 períodos
            sma50: Média móvel de 50 períodos
            adx: Valores do ADX

        Returns:
            Tupla com (direção da tendência, força da tendência)
        """
        # Verificar se temos dados suficientes
        if (
            np.isnan(sma20[-1])
            or np.isnan(sma50[-1])
            or np.isnan(adx[-1])
            or len(prices) < 20
        ):
            return 0, 0.0

        # 1. Direção da tendência baseada em médias móveis
        if sma20[-1] > sma50[-1]:
            direction = 1  # Tendência de alta
        elif sma20[-1] < sma50[-1]:
            direction = -1  # Tendência de baixa
        else:
            direction = 0  # Lateral

        # 2. Verificar inclinação das médias
        sma20_slope = (
            (sma20[-1] - sma20[-5]) / sma20[-5]
            if len(sma20) >= 5 and sma20[-5] > 0
            else 0
        )
        sma50_slope = (
            (sma50[-1] - sma50[-5]) / sma50[-5]
            if len(sma50) >= 5 and sma50[-5] > 0
            else 0
        )

        # Ajustar direção se inclinação contradiz cruzamento
        if direction == 1 and sma20_slope < 0 and sma50_slope < 0:
            direction = 0  # Médias de alta mas inclinando para baixo
        elif direction == -1 and sma20_slope > 0 and sma50_slope > 0:
            direction = 0  # Médias de baixa mas inclinando para cima

        # 3. Avaliar força da tendência
        # - ADX: maior que 25 = tendência forte
        adx_strength = min(1.0, adx[-1] / 50.0)  # Normalizar para 0-1

        # - Consistência: verificar quantos dos últimos N candles seguem a direção
        n_candles = min(10, len(prices) - 1)
        diffs = np.diff(prices[-n_candles - 1 :])
        consistent_candles = np.sum(np.sign(diffs) == direction) / n_candles

        # - Distância das médias
        distance = abs(sma20[-1] - sma50[-1]) / ((sma20[-1] + sma50[-1]) / 2)
        # Normalizar, máximo em 5% de separação
        distance_factor = min(1.0, distance * 20)

        # Combinar fatores para força da tendência
        trend_strength = (
            adx_strength * 0.5 + consistent_candles * 0.3 + distance_factor * 0.2
        )

        return direction, trend_strength

    def _calculate_classic_reversal_score(
        self,
        trend_direction: int,
        candle_patterns: Dict[str, float],
        distance_to_support: float,
        distance_to_resistance: float,
    ) -> float:
        """
        Calcula pontuação de reversão baseada em indicadores clássicos.

        Args:
            trend_direction: Direção da tendência (1=alta, -1=baixa, 0=lateral)
            candle_patterns: Dicionário com padrões de velas identificados
            distance_to_support: Distância percentual ao suporte
            distance_to_resistance: Distância percentual à resistência

        Returns:
            Pontuação entre 0 e 1
        """
        score = 0.0

        # 1. Pontuação baseada em padrões de velas
        pattern_score = 0.0

        if (
            trend_direction > 0
        ):  # Tendência de alta - procurar padrões de reversão de alta
            bearish_patterns = [
                "shooting_star",
                "bearish_engulfing",
                "evening_star",
                "doji",
            ]
            for pattern in bearish_patterns:
                if pattern in candle_patterns:
                    pattern_score = max(pattern_score, candle_patterns[pattern])

        elif (
            trend_direction < 0
        ):  # Tendência de baixa - procurar padrões de reversão de baixa
            bullish_patterns = ["hammer", "bullish_engulfing", "morning_star", "doji"]
            for pattern in bullish_patterns:
                if pattern in candle_patterns:
                    pattern_score = max(pattern_score, candle_patterns[pattern])

        # 2. Pontuação baseada na proximidade de suporte/resistência
        level_score = 0.0

        if trend_direction > 0 and distance_to_resistance < 0.02:
            # Perto da resistência em tendência de alta
            level_score = 1.0 - distance_to_resistance / 0.02

        elif trend_direction < 0 and distance_to_support < 0.02:
            # Perto do suporte em tendência de baixa
            level_score = 1.0 - distance_to_support / 0.02

        # Combinar pontuações
        score = pattern_score * 0.7 + level_score * 0.3

        return min(1.0, score)

    def _calculate_quantum_reversal_score(
        self,
        quantum_metrics: Dict[str, Any],
        phase_analysis: Dict[str, Any],
        qast_patterns: Dict[str, Any],
    ) -> float:
        """
        Calcula pontuação de reversão baseada em métricas quânticas.

        Args:
            quantum_metrics: Métricas quânticas padrão
            phase_analysis: Resultados da análise de fases
            qast_patterns: Padrões detectados por QAST

        Returns:
            Pontuação entre 0 e 1
        """
        score = 0.0

        # 1. Pontuação baseada em desequilíbrio de fases
        phase_score = phase_analysis.get("phase_imbalance", 0.0)

        # 2. Pontuação baseada em entropia quântica (maior entropia = maior
        # potencial de reversão)
        entropy = (
            quantum_metrics.get("entropy", [0.5])[0]
            if "entropy" in quantum_metrics
            else 0.5
        )
        # Normalizar, > 0.67 dá pontuação máxima
        entropy_score = min(1.0, entropy * 1.5)

        # 3. Pontuação baseada em padrões de mercado via QAST
        pattern_score = qast_patterns.get("pattern_strength", 0.0)
        exhaustion_score = qast_patterns.get("exhaustion_pattern", 0.0)

        # 4. Pontuação baseada em desequilíbrio temporal (Deutsch CTC)
        temporal_score = phase_analysis.get("temporal_imbalance", 0.0)

        # Combinar pontuações
        score = (
            phase_score * 0.35
            + entropy_score * 0.25
            + pattern_score * 0.20
            + exhaustion_score * 0.10
            + temporal_score * 0.10
        )

        return min(1.0, score)

    def _calculate_avg_range(self, data: pd.DataFrame, lookback: int = 20) -> float:
        """
        Calcula o range médio de preços para um período.

        Args:
            data: DataFrame com dados OHLCV
            lookback: Número de barras para análise

        Returns:
            Range médio como porcentagem
        """
        if len(data) < lookback:
            return 0.01  # Valor padrão se não temos dados suficientes

        recent_data = data.iloc[-lookback:]
        ranges = (recent_data["high"] - recent_data["low"]) / recent_data["close"]
        avg_range = ranges.mean()

        return avg_range

    def backtest(
        self,
        market_data: pd.DataFrame,
        initial_capital: float = 10000.0,
        risk_per_trade_pct: float = 0.01,
    ) -> Dict[str, Any]:
        """Simple backtest loop using :meth:`analyze_market` and generated signals.

        This backtester is intentionally lightweight and deterministic so it can
        be used by the QAST evolutionary engine. No leverage or short selling is
        considered and any open position is liquidated at the end of the
        simulation.

        Parameters
        ----------
        market_data : pandas.DataFrame
            Historical OHLCV data ordered by timestamp.
        initial_capital : float, optional
            Starting capital for the simulation. Defaults to ``10000.0``.
        risk_per_trade_pct : float, optional
            Percentage of available capital risked on each trade. Defaults to
            ``0.01`` (1%).

        Returns
        -------
        dict
            Summary of the backtest including final capital, return percentage
            and trade history.
        """

        if market_data.empty or "close" not in market_data.columns:
            logger.warning("Market data vazio ou formato inválido para backtest.")
            return {
                "final_capital": initial_capital,
                "total_return_pct": 0.0,
                "num_trades": 0,
                "trade_history": [],
            }

        capital = initial_capital
        position: Optional[Dict[str, Any]] = None
        trades: List[Dict[str, Any]] = []

        start_idx = max(self.lookback_period + 10, 1)

        for i in range(start_idx, len(market_data)):
            current_slice = market_data.iloc[: i + 1]
            tc = TradingContext(
                symbol=self.symbol or "N/A",
                timeframe=self.timeframe or "N/A",
                current_price=float(current_slice["close"].iloc[-1]),
                ohlcv=current_slice,
                timestamp=current_slice.index[-1],
                wallet_state={},
            )

            analysis = self.analyze_market(current_slice, tc)
            signals = self.generate_signals(analysis)
            signals = self.risk_management(signals, current_slice)
            signals = self.position_sizing(signals, capital, risk_per_trade_pct)

            if signals.empty:
                continue

            sig = signals.iloc[-1]
            price = float(sig["price"])
            size = float(sig.get("position_size", 0.0))

            if sig["signal"] == "buy" and position is None and size > 0.0:
                capital -= size * price
                position = {
                    "entry_price": price,
                    "size": size,
                    "timestamp": sig.name,
                }
                trades.append({"type": "entry", **position})
            elif sig["signal"] == "sell" and position is not None:
                pnl = (price - position["entry_price"]) * position["size"]
                capital += position["size"] * price
                trades.append(
                    {
                        "type": "exit",
                        "timestamp": sig.name,
                        "price": price,
                        "size": position["size"],
                        "pnl_abs": pnl,
                    }
                )
                position = None

        if position is not None:
            final_price = float(market_data["close"].iloc[-1])
            pnl = (final_price - position["entry_price"]) * position["size"]
            capital += position["size"] * final_price
            trades.append(
                {
                    "type": "exit",
                    "timestamp": market_data.index[-1],
                    "price": final_price,
                    "size": position["size"],
                    "pnl_abs": pnl,
                }
            )

        total_return_pct = (
            (capital - initial_capital) / initial_capital * 100
            if initial_capital > 0
            else 0.0
        )

        return {
            "final_capital": capital,
            "total_return_pct": total_return_pct,
            "num_trades": len(trades) // 2,
            "trade_history": trades,
        }
