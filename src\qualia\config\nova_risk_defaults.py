from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("nova_risk_defaults.yaml")


def load_nova_risk_defaults() -> Dict[str, Any]:
    """Load risk defaults for nova_estrategia_qualia from YAML."""
    return load_yaml_config("QUALIA_NOVA_RISK_DEFAULTS", _DEFAULT_PATH, logger=logger)


__all__ = ["load_nova_risk_defaults"]
