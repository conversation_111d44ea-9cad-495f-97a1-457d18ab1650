"""
Módulo de Calibração QUALIA - Melhorias Estruturais

Este módulo contém as melhorias estruturais implementadas para o sistema de calibração:

- cache_manager: Sistema de cache inteligente para dados históricos e resultados
- parallel_processor: Processamento paralelo de múltiplos ativos
- validation_system: Validação cruzada e métricas de qualidade

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

from .cache_manager import IntelligentCacheManager, CacheEntry, CacheStats
from .parallel_processor import ParallelCalibrationProcessor, WorkerTask, TaskResult, WorkerStats
from .validation_system import CalibrationValidationSystem, ValidationMetrics, CrossValidationResult, QualityAssessment

__all__ = [
    'IntelligentCacheManager',
    'CacheEntry', 
    'CacheStats',
    'ParallelCalibrationProcessor',
    'WorkerTask',
    'TaskResult', 
    'WorkerStats',
    'CalibrationValidationSystem',
    'ValidationMetrics',
    'CrossValidationResult',
    'QualityAssessment'
]
