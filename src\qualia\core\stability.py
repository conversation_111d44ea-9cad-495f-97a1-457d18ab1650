from typing import Sequence, Tu<PERSON>, Dict, List
import numpy as np


def compute_alpha(H: float, alpha_params: Dict[str, float]) -> float:
    """
    Calcula α(H) = α0 + α1·H + α2·H².

    Args:
      H: valor corrente de entropia simbólica (H_k).
      alpha_params: dicionário com chaves:
        - "alpha0": float
        - "alpha1": float
        - "alpha2": float

    Returns:
      valor de α(H).
    """
    a0 = alpha_params.get("alpha0", 0.0)
    a1 = alpha_params.get("alpha1", 0.0)
    a2 = alpha_params.get("alpha2", 0.0)

    return a0 + a1 * H + a2 * (H**2)


def simulate_mass_dynamics(
    H_series: Sequence[float],
    alpha_params: Dict[str, float],
    M0: float = 1.0,
    M_min: float = 0.0,
) -> Tuple[List[float], List[float]]:
    """
    Simula M_k ao longo de uma sequência de H_k, retornando
    série<PERSON> de M_k e V_k = M_k - M_min.

    Args:
      H_series: lista/array de valores de entropia simbólica H_k.
      alpha_params: parâmetros de α (ver compute_alpha).
      M0: valor inicial de massa informacional M₀.
      M_min: limiar inferior para M_k.

    Returns:
      (M_series, V_series), onde ambos têm comprimento len(H_series)+1:
        - M_series[k] é M_k,
        - V_series[k] = M_k - M_min.
    """
    num_steps = len(H_series)
    M_series: List[float] = [0.0] * (num_steps + 1)
    V_series: List[float] = [0.0] * (num_steps + 1)

    M_series[0] = M0
    V_series[0] = M0 - M_min

    Mk = M0
    for k in range(num_steps):
        Hk = H_series[k]
        alpha_Hk = compute_alpha(Hk, alpha_params)

        # M_{k+1} = M_k * (1 - α(H_k))
        # Garantir que a massa não aumente se alpha_Hk for negativo
        # e que o fator de decaimento (1 - alpha_Hk) não seja negativo.
        decay_factor = 1.0 - np.clip(
            alpha_Hk, 0.0, 1.0
        )  # Garante que alpha_Hk em [0,1] para decaimento
        Mk_plus_1 = Mk * decay_factor

        # Massa não deve ir abaixo de M_min durante a simulação (implícito na função V)
        # mas a dinâmica de M pode ir abaixo de M_min se não for clipada aqui.
        # Para V_k = M_k - M_min, se M_k < M_min, V_k < 0.
        # A definição de estabilidade geralmente espera V_k >= 0.
        # Se M_k pode ir abaixo de M_min e isso é desejado, então V_k pode ser negativo.
        # Se M_k deve ser >= M_min, então Mk_plus_1 = max(Mk_plus_1, M_min)
        # Por ora, não farei o clip de Mk_plus_1 em M_min, para que V_k reflita M_k - M_min sem restrições.

        M_series[k + 1] = Mk_plus_1
        V_series[k + 1] = Mk_plus_1 - M_min
        Mk = Mk_plus_1

    return M_series, V_series


def compute_lyapunov(M_series: Sequence[float], M_min: float = 0.0) -> List[float]:
    """
    Aplica V_k = M_k - M_min a uma série de M_k.

    Args:
      M_series: sequência de valores de M_k.
      M_min: limiar inferior usado na definição de V_k.

    Returns:
      V_series de mesmo comprimento que M_series.
    """
    V_series: List[float] = [(Mk - M_min) for Mk in M_series]
    return V_series


def compute_delta_V(V_series: Sequence[float]) -> List[float]:
    """
    Calcula ΔV_k = V_{k+1} - V_k para k = 0..len(V_series)-2.

    Args:
      V_series: sequência de valores V_k.

    Returns:
      delta_V de comprimento len(V_series)-1.
    """
    if len(V_series) < 2:
        return []

    delta_V: List[float] = [
        V_series[k + 1] - V_series[k] for k in range(len(V_series) - 1)
    ]
    return delta_V
