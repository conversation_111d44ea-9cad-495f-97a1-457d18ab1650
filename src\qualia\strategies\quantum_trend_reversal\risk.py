"""Risk management helpers for :mod:`quantum_trend_reversal`."""

from __future__ import annotations

from typing import Any, TYPE_CHECKING
import numpy as np
import pandas as pd

from ...utils.logger import get_logger

if TYPE_CHECKING:
    from .core import QuantumTrendReversalStrategy

logger = get_logger(__name__)


def adjust_risk_with_uncertainty(
    uncertainty: float,
    max_position_size: float,
    *,
    threshold: float = 0.8,
    position_factor: float = 0.5,
) -> float:
    """Return adjusted max position size based on metacognitive uncertainty."""
    if uncertainty >= threshold:
        logger.debug(
            "Incerteza %.2f acima do limiar %.2f – reduzindo max_position_size",
            uncertainty,
            threshold,
        )
        return max_position_size * position_factor
    return max_position_size


def position_sizing(
    strategy: Any,
    signals: pd.DataFrame,
    capital: float,
    risk_per_trade: float,
    *,
    uncertainty: float = 0.0,
    uncertainty_threshold: float = 0.8,
    uncertainty_position_factor: float = 0.5,
    uncertainty_stop_loss_factor: float = 1.2,
) -> pd.DataFrame:
    """Calculate position sizes for generated signals."""
    # Verificar tipo antes de usar .empty
    if isinstance(signals, np.ndarray):
        if signals.size == 0:
            return pd.DataFrame()
        signals = pd.DataFrame({"signal": signals.flatten()})

    if not isinstance(signals, pd.DataFrame):
        logger.error(f"position_sizing: Tipo inesperado: {type(signals)}")
        return pd.DataFrame()

    if signals.empty:
        return signals
    result = signals.copy()
    base_max_pos = getattr(strategy, "max_position_size", 0.25)
    max_pos_size = adjust_risk_with_uncertainty(
        uncertainty,
        base_max_pos,
        threshold=uncertainty_threshold,
        position_factor=uncertainty_position_factor,
    )
    for idx in result.index:
        signal = result.loc[idx]
        price = signal["price"]
        stop_loss = signal["stop_loss"]
        confidence = signal["confidence"]
        adjusted_risk = risk_per_trade * min(
            1.0, confidence / strategy.reversal_threshold
        )
        risk_per_unit = abs(price - stop_loss)
        if uncertainty >= uncertainty_threshold:
            risk_per_unit *= uncertainty_stop_loss_factor
            if stop_loss < price:
                stop_loss = price - risk_per_unit
            else:
                stop_loss = price + risk_per_unit
        risk_amount = capital * adjusted_risk
        if risk_per_unit < 1e-9:
            strategy.logger.warning(
                "Risco por unidade é zero ou muito pequeno em %s; tamanho de posição zerado",
                idx,
            )
            position_size = 0.0
        else:
            position_size = risk_amount / risk_per_unit
        position_value = position_size * price
        max_position_value = capital * max_pos_size
        if position_value > max_position_value:
            position_size = max_position_value / price
        result.loc[idx, "position_size"] = position_size
        result.loc[idx, "position_value"] = position_size * price
        result.loc[idx, "risk_amount"] = risk_amount
        result.loc[idx, "risk_per_unit"] = risk_per_unit
        result.loc[idx, "stop_loss"] = stop_loss
    return result


def risk_management(
    strategy: Any, signals: pd.DataFrame, market_data: pd.DataFrame
) -> pd.DataFrame:
    """Apply risk filters to generated signals."""
    # Verificar tipo antes de usar .empty
    if isinstance(signals, np.ndarray):
        if signals.size == 0:
            return pd.DataFrame()
        signals = pd.DataFrame({"signal": signals.flatten()})

    if not isinstance(signals, pd.DataFrame):
        logger.error(f"risk_management: Tipo inesperado: {type(signals)}")
        return pd.DataFrame()

    if signals.empty:
        return signals

    result = signals.copy()

    market_indices = market_data.index.get_indexer(result.index, method="nearest")
    if (market_indices < 0).any() or (market_indices >= len(market_data)).any():
        raise IndexError("Signal index out of bounds for market_data")

    window = strategy.consolidation_bars + 1
    high_roll = market_data["high"].rolling(window=window).max()
    low_roll = market_data["low"].rolling(window=window).min()
    close_roll = market_data["close"].rolling(window=window).mean()
    price_range_series = (high_roll - low_roll) / close_roll
    price_range = pd.Series(
        price_range_series.iloc[market_indices].values, index=result.index
    )

    avg_range = strategy._calculate_avg_range(market_data, lookback=20)
    consolidation_ok = (price_range < avg_range * 0.8).fillna(False)

    if "volume" in market_data.columns:
        prev_volume_mean = (
            market_data["volume"]
            .shift(1)
            .rolling(window=strategy.consolidation_bars)
            .mean()
        )
        volume_ok = pd.Series(
            (
                market_data["volume"].iloc[market_indices].values
                > prev_volume_mean.iloc[market_indices].values * 1.2
            ),
            index=result.index,
        ).fillna(False)
    else:
        volume_ok = pd.Series(True, index=result.index)

    recent_low = market_data["low"].rolling(window=11, min_periods=1).min()
    recent_high = market_data["high"].rolling(window=11, min_periods=1).max()

    price_ok = pd.Series(index=result.index, dtype=bool)
    buy_mask = result["signal"] == "buy"
    sell_mask = ~buy_mask
    price_ok.loc[buy_mask] = (
        result.loc[buy_mask, "price"].values
        < recent_low.iloc[market_indices[buy_mask]].values * 1.03
    )
    price_ok.loc[sell_mask] = (
        result.loc[sell_mask, "price"].values
        > recent_high.iloc[market_indices[sell_mask]].values * 0.97
    )
    price_ok = price_ok.fillna(False)

    consolidation_mask = market_indices >= strategy.consolidation_bars
    criteria_count = (
        consolidation_ok.astype(int) + volume_ok.astype(int) + price_ok.astype(int)
    )
    valid_mask = consolidation_mask & (criteria_count >= 2)

    for idx in result.index[~valid_mask]:
        strategy.logger.debug(
            f"Sinal filtrado por gestão de risco: {result.loc[idx, 'signal']} em {idx}"
        )

    if valid_mask.any():
        return result.loc[valid_mask]
    return pd.DataFrame()


def process_signals(
    strategy: "QuantumTrendReversalStrategy",
    signals: pd.DataFrame,
    market_data: pd.DataFrame,
    capital: float,
    risk_per_trade: float,
) -> pd.DataFrame:
    """Processa sinais com gestão de risco integrada."""
    # Verificar se signals é um numpy array e converter se necessário
    if isinstance(signals, np.ndarray):
        logger.warning(
            f"{strategy.__class__.__name__} - process_signals: Recebido numpy array em vez de DataFrame. Convertendo..."
        )
        if signals.size == 0:
            return pd.DataFrame()
        signals = pd.DataFrame({"signal": signals.flatten()})

    # Verificar se é DataFrame antes de usar .empty
    if not isinstance(signals, pd.DataFrame):
        logger.error(
            f"{strategy.__class__.__name__} - process_signals: Tipo inesperado: {type(signals)}"
        )
        return pd.DataFrame()

    if signals.empty:
        return signals

    # ... existing code ...
    risk_manager = getattr(strategy, "risk_manager", None)
    if risk_manager is None:
        logger.warning("Risk manager não encontrado na estratégia")
        return signals
