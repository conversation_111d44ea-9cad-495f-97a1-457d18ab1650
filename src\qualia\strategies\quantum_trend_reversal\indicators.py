"""Indicator utilities for :mod:`quantum_trend_reversal`."""

from __future__ import annotations

from typing import Any, Di<PERSON>, <PERSON><PERSON>

import numpy as np
import pandas as pd
from numpy.lib.stride_tricks import sliding_window_view
from numpy.typing import Array<PERSON>ike


def calculate_sma(data: np.ndarray, period: int) -> np.ndarray:
    """Calculate Simple Moving Average using vectorized operations."""

    if period <= 0:
        raise ValueError("period must be positive")

    values = np.asarray(data, dtype=float)
    if len(values) < period:
        return np.full_like(values, np.nan, dtype=float)

    window_sums = np.convolve(values, np.ones(period), mode="valid")
    sma = window_sums / period
    return np.concatenate([np.full(period - 1, np.nan), sma])


def calculate_adx(
    high: ArrayLike, low: ArrayLike, close: ArrayLike, period: int = 14
) -> np.ndarray:
    """Calculate Average Directional Index (ADX) using vectorized operations."""

    high_arr = np.asarray(high, dtype=float)
    low_arr = np.asarray(low, dtype=float)
    close_arr = np.asarray(close, dtype=float)
    n = len(close_arr)

    adx = np.full(n, np.nan, dtype=float)
    if n <= period:
        return adx

    up_moves = np.maximum(high_arr[1:] - high_arr[:-1], 0.0)
    down_moves = np.maximum(low_arr[:-1] - low_arr[1:], 0.0)

    true_range = np.maximum.reduce(
        [
            high_arr[1:] - low_arr[1:],
            np.abs(high_arr[1:] - close_arr[:-1]),
            np.abs(low_arr[1:] - close_arr[:-1]),
        ]
    )

    plus_dm_sum = np.convolve(up_moves, np.ones(period), mode="valid")
    minus_dm_sum = np.convolve(down_moves, np.ones(period), mode="valid")
    tr_sum = np.convolve(true_range, np.ones(period), mode="valid")

    plus_di = np.where(tr_sum > 0, 100 * plus_dm_sum / tr_sum, 0.0)
    minus_di = np.where(tr_sum > 0, 100 * minus_dm_sum / tr_sum, 0.0)
    dx = np.where(
        (plus_di + minus_di) > 0,
        100 * np.abs(plus_di - minus_di) / (plus_di + minus_di),
        0.0,
    )

    alpha = 1 / period
    adx_values = (
        pd.Series(dx, dtype=float).ewm(alpha=alpha, adjust=False).mean().to_numpy()
    )

    adx[period:] = adx_values
    return adx


def find_support_resistance(
    high: np.ndarray, low: np.ndarray, close: np.ndarray, n: int = 20
) -> Tuple[float, float]:
    """Identify support and resistance levels."""
    if len(close) < n:
        return 0, 0
    recent_high = high[-n:]
    recent_low = low[-n:]
    price_range = np.max(recent_high) - np.min(recent_low)
    min_price = np.min(recent_low)
    max_price = np.max(recent_high)
    bins = 10
    bin_size = price_range / bins
    hist_low, _ = np.histogram(recent_low, bins=bins, range=(min_price, max_price))
    hist_high, bin_edges = np.histogram(
        recent_high, bins=bins, range=(min_price, max_price)
    )
    support_bin = np.argmax(hist_low)
    resistance_bin = np.argmax(hist_high)
    support = bin_edges[support_bin] + bin_size / 2
    resistance = bin_edges[resistance_bin] + bin_size / 2
    last_price = close[-1]
    if support > last_price:
        support = np.min(recent_low[-5:])
    if resistance < last_price:
        resistance = np.max(recent_high[-5:])
    return support, resistance


def identify_candle_patterns(
    opens: np.ndarray, highs: np.ndarray, lows: np.ndarray, closes: np.ndarray
) -> Dict[str, Any]:
    """Identify candle patterns on recent data."""
    patterns: Dict[str, Any] = {}
    n = len(closes)
    if n < 3:
        return patterns
    last_open = opens[-1]
    last_high = highs[-1]
    last_low = lows[-1]
    last_close = closes[-1]
    body_size = abs(last_close - last_open)
    upper_shadow = last_high - max(last_open, last_close)
    lower_shadow = min(last_open, last_close) - last_low
    candle_range = last_high - last_low
    if (
        last_close > last_open
        and lower_shadow > body_size * 2
        and upper_shadow < body_size * 0.5
    ):
        patterns["hammer"] = 0.8
    if body_size < candle_range * 0.1:
        patterns["doji"] = 0.6
    if (
        n >= 2
        and last_close > last_open
        and closes[-2] < opens[-2]
        and last_close > opens[-2]
        and last_open < closes[-2]
    ):
        patterns["bullish_engulfing"] = 0.9
    return patterns
