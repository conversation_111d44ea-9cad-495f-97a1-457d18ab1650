"""Risk management helpers for :mod:`enhanced_quantum_momentum`."""

from __future__ import annotations

from typing import TYPE_CHECKING, Any, Tuple
import pandas as pd
import numpy as np

from ...utils.logger import get_logger

if TYPE_CHECKING:
    from .core import EnhancedQuantumMomentumStrategy

logger = get_logger(__name__)


def adjust_risk_with_uncertainty(
    uncertainty: float,
    max_position_size: float,
    stop_loss_pct: float,
    *,
    threshold: float = 0.8,
    position_factor: float = 0.5,
    stop_loss_factor: float = 1.2,
) -> Tuple[float, float]:
    """Adjust risk parameters based on metacognitive uncertainty.

    Parameters
    ----------
    uncertainty
        Current uncertainty level reported by metacognition.
    max_position_size
        Baseline maximum position size as fraction of capital.
    stop_loss_pct
        Baseline stop loss percentage.
    threshold
        Uncertainty threshold above which adjustments apply.
    position_factor
        Factor to multiply ``max_position_size`` when uncertainty is high.
    stop_loss_factor
        Factor to multiply ``stop_loss_pct`` when uncertainty is high.

    Returns
    -------
    <PERSON><PERSON>[float, float]
        Adjusted ``max_position_size`` and ``stop_loss_pct``.
    """
    if uncertainty >= threshold:
        logger.debug(
            "Nível de incerteza %.2f acima do limiar %.2f – reduzindo posição e "
            "aumentando stop loss",
            uncertainty,
            threshold,
        )
        return (
            max_position_size * position_factor,
            stop_loss_pct * stop_loss_factor,
        )
    return max_position_size, stop_loss_pct


def risk_management(
    strategy: "EnhancedQuantumMomentumStrategy",
    signals: pd.DataFrame,
    market_data: pd.DataFrame,
) -> pd.DataFrame:
    """Apply advanced risk filters."""
    # Verificar tipo antes de usar .empty
    if isinstance(signals, np.ndarray):
        logger.warning(
            f"{strategy.__class__.__name__} - risk_management: Recebido numpy array em vez de DataFrame. Convertendo..."
        )
        if signals.size == 0:
            return pd.DataFrame()
        signals = pd.DataFrame({"signal": signals.flatten()})
    
    if not isinstance(signals, pd.DataFrame):
        logger.error(
            f"{strategy.__class__.__name__} - risk_management: Tipo inesperado: {type(signals)}"
        )
        return pd.DataFrame()
    
    if signals.empty:
        return signals

    risk_manager = getattr(strategy, "risk_manager", None)
    if risk_manager is None:
        logger.warning("Risk manager não encontrado na estratégia")
        return signals

    result = signals.copy()
    valid_signals = []
    for idx in result.index:
        signal = result.loc[idx]
        signal_type = signal["signal"]
        market_regime = signal["market_regime"]
        directional_strength = signal["directional_strength"]
        confidence = signal["confidence"]
        rr_ratio = signal["risk_reward_ratio"]
        regime_aligned = (
            (market_regime == "uptrend" and signal_type == "buy")
            or (market_regime == "downtrend" and signal_type == "sell")
            or (market_regime == "range" and directional_strength < 0.25)
        )
        confidence_ok = confidence > strategy.signal_threshold * (
            1.2 if market_regime == "range" else 1.0
        )
        rr_ok = rr_ratio >= 2.5
        recent_signals = [
            s for s in strategy.historical_signals[-5:] if s["signal"] == signal_type
        ]
        no_recent = len(recent_signals) <= 2
        if regime_aligned and confidence_ok and rr_ok and no_recent:
            valid_signals.append(idx)
        else:
            reasons = []
            if not regime_aligned:
                reasons.append(f"regime não alinhado ({market_regime})")
            if not confidence_ok:
                reasons.append(f"confiança insuficiente ({confidence:.2f})")
            if not rr_ok:
                reasons.append(f"r/r baixo ({rr_ratio:.1f})")
            if not no_recent:
                reasons.append("excesso de sinais recentes")
            strategy.logger.debug(
                f"Sinal filtrado ({', '.join(reasons)}): {signal_type} em {idx}"
            )
    if valid_signals:
        return result.loc[valid_signals]
    return pd.DataFrame()
