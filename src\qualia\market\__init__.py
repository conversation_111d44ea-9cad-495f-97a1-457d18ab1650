"""Market integration helpers for QUALIA.

These modules provide data feeds and order execution utilities. They reference
"quantum trading" concepts but remain standard software components and do not
imply any actual consciousness.

Example
-------
>>> from ..market.quantum_metrics_calculator import QuantumMetricsCalculator
>>> calc = QuantumMetricsCalculator()
"""

from .symbol_utils import normalize_symbol
from .event_bus import MARKET_PATTERN_EVENT, MarketPatternDetected

try:  # pragma: no cover - optional dependency during tests
    from .ticker_worker import TickerWorker
except Exception:  # pragma: no cover - avoid hard failure if deps missing
    TickerWorker = None

__all__ = [
    "normalize_symbol",
    "TickerWorker",
    "MARKET_PATTERN_EVENT",
    "MarketPatternDetected",
]
