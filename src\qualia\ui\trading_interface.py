"""
QUALIA Trading System: Trading Interface Blueprint
--------------------------------------------------
<PERSON><PERSON><PERSON><PERSON> que define as rotas da API Flask para a interface de trading,
integrando a lógica de trading com a interface do usuário.
"""

from flask import Blueprint, render_template, jsonify, request, session
from pathlib import Path
try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()
from datetime import datetime, timedelta, timezone
import numpy as np
import ccxt
from ..utils.logger import get_logger
import requests
from typing import Any, List, TypedDict, Optional, Dict
from ..config.settings import get_env
from flask_wtf.csrf import validate_csrf
from wtforms.validators import ValidationError
from flask import abort
from ..market.data_simulators import (
    generate_mock_market_data,
    simulate_positions,
    simulate_trade_history,
    simulate_performance_metrics,
    simulate_risk_metrics,
)

logger = get_logger(__name__)


class PerformanceMetrics(TypedDict):
    pnl: float
    pnl_percentage: float
    win_rate: float
    drawdown: float
    sharpe: float
    trades: int
    regime_change: int


class QuantumMetrics(TypedDict):
    entropy: float
    coherence: float
    active_states: int
    page_entropy: float
    otoc: float
    quantum_coherence: float
    loschmidt_echo: float
    informational_mass: float
    fidelity_to_initial: float
    pattern_recognition_rate: float
    self_reflection_depth: float
    thermal_coefficient: float
    retrocausal_coefficient: float


class TradingState(TypedDict, total=False):
    status: str
    last_update: Optional[str]
    error_message: Optional[str]
    api_configured: bool
    live_mode: bool
    api_key: Optional[str]
    api_secret: Optional[str]
    exchange: Optional[str]
    symbols: List[str]
    risk_profile: str
    initial_capital: float
    market_data: Dict[str, Any]
    positions: List[Dict[str, Any]]
    trade_history: List[Dict[str, Any]]
    performance_metrics: PerformanceMetrics
    quantum_metrics: QuantumMetrics
    metric_history: List[Dict[str, Any]]
    max_drawdown_threshold: float
    enable_retrocausality: bool


# Criar blueprint Flask
trading_bp = Blueprint(
    "trading",
    __name__,
    url_prefix="/trading",
    template_folder="../../templates/trading",
)


def create_trading_blueprint(
    state: Optional[TradingState] = None, engine: Any | None = None
) -> Blueprint:
    """Return ``trading_bp`` using provided state and engine."""

    global TRADING_STATE, TRADING_ENGINE
    if state is not None:
        TRADING_STATE = state
    if engine is not None:
        TRADING_ENGINE = engine
    return trading_bp


@trading_bp.before_request
def enforce_csrf() -> None:
    """Validate CSRF token for modifying requests."""
    if request.method in {"POST", "PUT", "DELETE", "PATCH"}:
        token = request.headers.get("X-CSRFToken")
        try:
            validate_csrf(token)
        except ValidationError:
            logger.exception("Falha na validação do token CSRF")
            abort(400, "CSRF token inválido")


# Estado global do sistema de trading
TRADING_STATE: TradingState = {
    "status": "stopped",  # 'stopped', 'initialized', 'running', 'stopping', 'error'
    "last_update": None,
    "error_message": None,
    "api_configured": False,
    "live_mode": False,
    "api_key": None,
    "api_secret": None,
    "exchange": None,
    "symbols": [],
    "risk_profile": "balanced",
    "initial_capital": 10000,
    "market_data": {},
    "positions": [],
    "trade_history": [],
    "performance_metrics": {
        "pnl": 0,
        "pnl_percentage": 0,
        "win_rate": 0,
        "drawdown": 0,
        "sharpe": 0,
        "trades": 0,
        "regime_change": 0,
    },
    "quantum_metrics": {
        "entropy": 0,
        "coherence": 0,
        "active_states": 0,
        "page_entropy": 0,
        "otoc": 0,
        "quantum_coherence": 0,
        "loschmidt_echo": 0,
        "pattern_recognition_rate": 0,
        "self_reflection_depth": 0,
        "thermal_coefficient": 0.1,
        "retrocausal_coefficient": 0.0,
        "regime_change": 0,
    },
    "metric_history": [],  # Histórico de métricas quânticas para visualização
    "max_drawdown_threshold": 0.2,
    "enable_retrocausality": True,
}

# Importações do sistema QUALIA de trading
try:
    from ..market.kraken_integration import KrakenIntegration
    from ..strategies.scalping import ScalpingStrategy
    from ..risk.manager import RiskManager
    from ..market.trading_engine import TradingEngine

    MODULES_LOADED = True
except ImportError as e:
    logger.warning(f"Aviso de importação: {e}")
    MODULES_LOADED = False

# Motor de trading global (instância única)
TRADING_ENGINE = None

# Símbolos criptográficos padrão para trading
DEFAULT_SYMBOLS = ["BTC/USD", "ETH/USD", "XRP/USD", "ADA/USD", "SOL/USD"]

# Timeframes padrão para análise (ex: 1m, 5m, 15m, 1h, 4h)
DEFAULT_TIMEFRAMES = ["5m", "15m", "1h", "4h"]
# ---------------------------------------------------------------------------
# Dependency injection helpers
# ---------------------------------------------------------------------------


def set_trading_state(state: TradingState) -> None:
    """Inject custom ``TradingState`` instance."""

    global TRADING_STATE
    TRADING_STATE = state


def set_trading_engine(engine: Any | None) -> None:
    """Inject custom trading engine."""

    global TRADING_ENGINE
    TRADING_ENGINE = engine


# Rotas da API de Trading


@trading_bp.route("/")
def trading_dashboard():
    """Renderiza a página principal do dashboard de trading."""
    return render_template("trading/index.html")


@trading_bp.route("/api/status")
def get_trading_status():
    """Retorna o status atual do sistema de trading."""
    if not TRADING_STATE["last_update"]:
        TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

    # Atualizar métricas quânticas
    if TRADING_STATE["status"] == "running":
        # Se temos motor real de trading e está em execução, usar métricas
        # reais
        if MODULES_LOADED and TRADING_ENGINE is not None:
            try:
                # Atualizar métricas quânticas com dados reais
                quantum_metrics = TRADING_ENGINE._calculate_quantum_risk_metrics()
                if quantum_metrics:
                    TRADING_STATE.update_quantum_metrics(quantum_metrics)
                    logger.info(
                        "Métricas quânticas reais atualizadas com sucesso via motor de trading"
                    )
                else:
                    # Se não conseguiu calcular métricas via motor, tentar via
                    # acesso direto
                    real_metrics = get_direct_quantum_metrics()
                    if real_metrics:
                        TRADING_STATE.update_quantum_metrics(real_metrics)
                        logger.info(
                            "Métricas quânticas reais obtidas diretamente via QUALIA Universe"
                        )
                    else:
                        logger.warning(
                            "Não foi possível obter métricas quânticas reais"
                        )
            except (RuntimeError, ValueError) as e:
                logger.exception(
                    "Erro ao calcular métricas quânticas via motor: %s. Tentando acesso direto.",
                    e,
                )
                # Tentar obter métricas diretamente do QUALIA Universe
                real_metrics = get_direct_quantum_metrics()
                if real_metrics:
                    TRADING_STATE.update_quantum_metrics(real_metrics)
                    logger.info(
                        "Métricas quânticas reais obtidas diretamente via QUALIA Universe"
                    )
        else:
            # Se não temos motor real, tentar acesso direto ao QUALIA Universe
            real_metrics = get_direct_quantum_metrics()
            if real_metrics:
                TRADING_STATE.update_quantum_metrics(real_metrics)
                logger.info(
                    "Métricas quânticas reais obtidas diretamente via QUALIA Universe"
                )
            else:
                logger.warning(
                    "Não foi possível obter métricas quânticas reais. Interface poderá não exibir dados completos."
                )

    # Garantir que as métricas de performance existam
    if (
        "performance_metrics" not in TRADING_STATE
        or not TRADING_STATE["performance_metrics"]
    ):
        TRADING_STATE["performance_metrics"] = {
            "pnl": 0,
            "pnl_percentage": 0,
            "win_rate": 0,
            "drawdown": 0,
            "sharpe": 0,
            "trades": 0,
        }

    # Garantir que as métricas quânticas existam
    if "quantum_metrics" not in TRADING_STATE or not TRADING_STATE["quantum_metrics"]:
        TRADING_STATE["quantum_metrics"] = {
            "entropy": 0,
            "coherence": 0,
            "active_states": 0,
        }

    return jsonify(
        {
            "status": TRADING_STATE["status"],
            "last_update": TRADING_STATE["last_update"],
            "error_message": TRADING_STATE["error_message"],
            "api_configured": TRADING_STATE["api_configured"],
            "live_mode": TRADING_STATE["live_mode"],
            "performance_metrics": TRADING_STATE["performance_metrics"],
            "quantum_metrics": TRADING_STATE["quantum_metrics"],
        }
    )


@trading_bp.route("/api/pairs")
def get_trading_pairs():
    """Retorna os pares de trading disponíveis."""
    # Lista padrão de pares populares
    pairs = [
        "BTC/USD",
        "ETH/USD",
        "SOL/USD",
        "XRP/USD",
        "ADA/USD",
        "LINK/USD",
        "DOT/USD",
        "DOGE/USD",
        "AVAX/USD",
        "MATIC/USD",
        "UNI/USD",
        "ATOM/USD",
        "LTC/USD",
        "BCH/USD",
        "XLM/USD",
    ]

    # Se o exchange estiver configurado, tentar obter a lista real
    if TRADING_STATE["exchange"] is not None:
        try:
            markets = TRADING_STATE["exchange"].load_markets()
            pairs = sorted(markets.keys())
        except (ccxt.BaseError, requests.RequestException) as e:
            logger.exception("Erro ao obter pares de trading: %s", e)

    return jsonify({"pairs": pairs})


@trading_bp.route("/api/risk-metrics")
def get_risk_metrics():
    """Retorna as métricas de risco do portfólio atual usando o calculador de métricas quânticas."""
    global TRADING_ENGINE

    try:
        risk_metrics = {}

        # Se estiver usando motor real de trading
        if (
            MODULES_LOADED
            and TRADING_ENGINE is not None
            and TRADING_STATE["status"] == "running"
        ):
            # Obter dados de mercado para análise de risco
            market_data = TRADING_ENGINE.get_market_data()
            positions = TRADING_ENGINE.get_positions()

            # Verificar se temos dados suficientes
            if market_data and len(market_data) > 0:
                # Tentar usar o novo endpoint de análise de risco quântico
                try:
                    # Preparar dados para o endpoint
                    payload = {"market_data": market_data, "positions": positions}

                    # Chamar o endpoint de análise de risco quântico

                    response = requests.post(
                        "http://localhost:5000/api/quantum/risk_analysis",
                        json=payload,
                        timeout=10,
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("success") and "risk_analysis" in result:
                            # Usar análise de risco do novo endpoint
                            risk_metrics = result["risk_analysis"]
                            logger.info(
                                "Análise de risco quântico obtida com sucesso via API"
                            )
                        else:
                            logger.warning(
                                f"API de análise de risco retornou erro: {result.get('message', 'Desconhecido')}"
                            )
                            # Usar método do motor em caso de falha
                            risk_metrics = (
                                TRADING_ENGINE._calculate_quantum_risk_metrics()
                            )
                    else:
                        logger.warning(
                            f"Falha ao chamar API de análise de risco: {response.status_code}"
                        )
                        # Usar método do motor em caso de falha
                        risk_metrics = TRADING_ENGINE._calculate_quantum_risk_metrics()

                except (requests.RequestException, ValueError) as e:
                    logger.exception(
                        "Erro ao usar API de análise de risco quântico: %s", e
                    )
                    # Usar método do motor em caso de falha na API
                    risk_metrics = TRADING_ENGINE._calculate_quantum_risk_metrics()
            else:
                # Usar método do motor quando não há dados suficientes
                risk_metrics = TRADING_ENGINE._calculate_quantum_risk_metrics()

            # Atualizar estado do trading
            if "quantum_metrics" not in TRADING_STATE:
                TRADING_STATE["quantum_metrics"] = {}
            TRADING_STATE.set_quantum_metric("risk", risk_metrics)
        else:
            # Modo simulação
            if (
                "quantum_metrics" in TRADING_STATE
                and "risk" in TRADING_STATE["quantum_metrics"]
            ):
                risk_metrics = TRADING_STATE["quantum_metrics"]["risk"]
            else:
                # Tentar usar API de análise de risco mesmo em modo simulação
                try:
                    # Preparar dados para o endpoint
                    payload = {
                        "market_data": TRADING_STATE["market_data"],
                        "positions": TRADING_STATE["positions"],
                    }

                    # Chamar o endpoint de análise de risco quântico

                    response = requests.post(
                        "http://localhost:5000/api/quantum/risk_analysis",
                        json=payload,
                        timeout=10,
                    )

                    if response.status_code == 200:
                        result = response.json()
                        if result.get("success") and "risk_analysis" in result:
                            # Usar análise de risco do novo endpoint
                            risk_metrics = result["risk_analysis"]
                            logger.info(
                                "Análise de risco quântico simulada obtida com sucesso via API"
                            )
                        else:
                            # Simular métricas de risco básicas
                            risk_metrics = simulate_risk_metrics(
                                TRADING_STATE["positions"]
                            )
                    else:
                        # Simular métricas de risco básicas
                        risk_metrics = simulate_risk_metrics(TRADING_STATE["positions"])

                except (requests.RequestException, ValueError) as e:
                    logger.exception(
                        "Erro ao usar API de análise de risco quântico em modo simulação: %s",
                        e,
                    )
                    # Simular métricas de risco básicas em caso de falha
                    risk_metrics = simulate_risk_metrics(TRADING_STATE["positions"])

                # Atualizar estado
                if "quantum_metrics" not in TRADING_STATE:
                    TRADING_STATE["quantum_metrics"] = {}
                TRADING_STATE.set_quantum_metric("risk", risk_metrics)

        return jsonify(
            {
                "success": True,
                "risk_metrics": risk_metrics,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    except (RuntimeError, ValueError, requests.RequestException) as e:
        logger.exception("Erro ao obter métricas de risco: %s", e)
        return jsonify(
            {"success": False, "message": f"Erro ao obter métricas de risco: {str(e)}"}
        )


@trading_bp.route("/api/market-data")
def get_market_data():
    """Retorna os dados de mercado para os símbolos selecionados."""
    global TRADING_ENGINE

    if not TRADING_STATE["symbols"]:
        return jsonify({"market_data": {}})

    # Verificar se temos um motor de trading inicializado e funcionando
    if MODULES_LOADED and TRADING_ENGINE is not None:
        try:
            # Obter dados reais do motor de trading
            real_market_data = TRADING_ENGINE.get_market_data()
            if real_market_data and len(real_market_data) > 0:
                # Atualizar o estado global com os dados reais
                TRADING_STATE["market_data"] = real_market_data
                logger.info(
                    f"Dados de mercado atualizados com sucesso via API para {len(real_market_data)} símbolos"
                )
            else:
                logger.warning(
                    "Não foi possível obter dados de mercado reais do motor de trading"
                )
        except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
            logger.exception(
                "Erro ao obter dados de mercado do motor de trading: %s", e
            )

    # Se ainda não temos dados (apenas em ambiente de desenvolvimento), gerar
    # simulados
    if not TRADING_STATE["market_data"] or len(TRADING_STATE["market_data"]) == 0:
        logger.warning("Gerando dados de mercado simulados para desenvolvimento")
        TRADING_STATE["market_data"] = generate_mock_market_data(
            TRADING_STATE["symbols"]
        )

    return jsonify({"market_data": TRADING_STATE["market_data"]})


@trading_bp.route("/api/positions")
def get_positions():
    """Retorna as posições abertas atuais."""
    global TRADING_ENGINE

    # Verificar se temos um motor de trading inicializado e funcionando
    if MODULES_LOADED and TRADING_ENGINE is not None:
        try:
            # Obter posições reais do motor de trading
            real_positions = TRADING_ENGINE.get_positions()
            if real_positions is not None:
                # Atualizar o estado global com as posições reais
                TRADING_STATE["positions"] = real_positions
                logger.info("Posições atualizadas com sucesso via API")
            else:
                logger.warning(
                    "Não foi possível obter posições reais do motor de trading"
                )
        except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
            logger.exception("Erro ao obter posições do motor de trading: %s", e)

    # Se ainda não temos posições (apenas em ambiente de desenvolvimento),
    # gerar simuladas
    if not TRADING_STATE["positions"] or len(TRADING_STATE["positions"]) == 0:
        logger.warning("Gerando posições simuladas para desenvolvimento")
        TRADING_STATE["positions"] = simulate_positions(
            TRADING_STATE["symbols"], TRADING_STATE["initial_capital"]
        )

    return jsonify({"positions": TRADING_STATE["positions"]})


@trading_bp.route("/api/trade-history")
def get_trade_history():
    """Retorna o histórico de trades."""
    global TRADING_ENGINE

    # Verificar se temos um motor de trading inicializado e funcionando
    if MODULES_LOADED and TRADING_ENGINE is not None:
        try:
            # Obter histórico real do motor de trading
            real_history = TRADING_ENGINE.get_trade_history()
            if real_history is not None and len(real_history) > 0:
                # Atualizar o estado global com o histórico real
                TRADING_STATE["trade_history"] = real_history
                logger.info(
                    f"Histórico de trades atualizado com sucesso via API: {len(real_history)} trades"
                )
            else:
                logger.warning(
                    "Não foi possível obter histórico real do motor de trading"
                )
        except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
            logger.exception(
                "Erro ao obter histórico de trades do motor de trading: %s", e
            )

    # Se ainda não temos histórico de trades (apenas em ambiente de
    # desenvolvimento), gerar simulado
    if not TRADING_STATE["trade_history"] or len(TRADING_STATE["trade_history"]) == 0:
        logger.warning("Gerando histórico de trades simulado para desenvolvimento")
        TRADING_STATE["trade_history"] = simulate_trade_history(
            TRADING_STATE["symbols"], TRADING_STATE["initial_capital"]
        )

        # Atualizar métricas de desempenho
        TRADING_STATE["performance_metrics"] = simulate_performance_metrics(
            TRADING_STATE["trade_history"], TRADING_STATE["initial_capital"]
        )

    return jsonify({"trade_history": TRADING_STATE["trade_history"]})


@trading_bp.route("/api/store-credentials", methods=["POST"])
def store_credentials():
    """Armazena as credenciais de API na sessão do usuário."""
    data = request.json or {}
    api_key = data.get("api_key")
    api_secret = data.get("api_secret")

    if not api_key or not api_secret:
        return jsonify(
            {"success": False, "message": "API Key e Secret são obrigatórios."}
        )

    if not request.is_secure and "localhost" not in request.host:
        return (
            jsonify(
                {
                    "success": False,
                    "message": "Credenciais devem ser enviadas via HTTPS.",
                }
            ),
            400,
        )

    session["api_key"] = api_key
    session["api_secret"] = api_secret

    TRADING_STATE["api_key"] = api_key
    TRADING_STATE["api_secret"] = api_secret
    TRADING_STATE["api_configured"] = True

    return jsonify({"success": True})


@trading_bp.route("/api/test-connection", methods=["POST"])
def test_api_connection():
    """Testa a conexão com a API do exchange."""
    data = request.json or {}
    api_key = data.get("api_key") or session.get("api_key")
    api_secret = data.get("api_secret") or session.get("api_secret")

    if not api_key or not api_secret:
        return jsonify(
            {"success": False, "message": "API Key e Secret são obrigatórios."}
        )

    try:
        # Teste de conexão com a API da Kraken
        exchange = ccxt.kraken(
            {"apiKey": api_key, "secret": api_secret, "enableRateLimit": True}
        )

        # Testar balance para verificar as credenciais
        if MODULES_LOADED:
            # Usar o módulo real de integração
            kraken = KrakenIntegration(api_key, api_secret)
            _ = kraken.get_balance()
        else:
            # Usar CCXT diretamente
            _ = exchange.fetch_balance()

        # Credenciais válidas
        TRADING_STATE["api_key"] = api_key
        TRADING_STATE["api_secret"] = api_secret
        TRADING_STATE["api_configured"] = True
        TRADING_STATE["exchange"] = exchange

        logger.info("Conexão com API da Kraken estabelecida com sucesso")

        return jsonify(
            {
                "success": True,
                "message": "Conexão com a Kraken estabelecida com sucesso.",
            }
        )

    except (ccxt.BaseError, requests.RequestException) as e:
        logger.exception("Erro na conexão com API: %s", e)
        return jsonify(
            {"success": False, "message": f"Erro ao conectar à Kraken: {str(e)}"}
        )


@trading_bp.route("/api/initialize", methods=["POST"])
def initialize_trading():
    """Inicializa o sistema de trading com os parâmetros fornecidos."""
    global TRADING_ENGINE
    data = request.json or {}

    api_key = data.get("api_key") or session.get("api_key")
    api_secret = data.get("api_secret") or session.get("api_secret")

    # Validar dados obrigatórios
    if not api_key or not api_secret:
        return jsonify(
            {"success": False, "message": "API Key e Secret são obrigatórios."}
        )

    if not data.get("symbols") or len(data.get("symbols", [])) == 0:
        return jsonify(
            {"success": False, "message": "Selecione pelo menos um par de trading."}
        )

    try:
        # Atualizar o estado global
        TRADING_STATE["api_key"] = api_key
        TRADING_STATE["api_secret"] = api_secret
        TRADING_STATE["symbols"] = data["symbols"]
        TRADING_STATE["risk_profile"] = data.get("risk_profile", "balanced")
        TRADING_STATE["initial_capital"] = data.get("initial_capital", 10000)
        TRADING_STATE["live_mode"] = data.get("live_mode", False)
        TRADING_STATE["api_configured"] = True

        # Inicializar o motor de trading
        if MODULES_LOADED:
            # Parar motor existente se houver
            if TRADING_ENGINE is not None:
                try:
                    TRADING_ENGINE.stop()
                    logger.info("Motor de trading existente parado")
                except RuntimeError as e:
                    logger.exception("Erro ao parar motor existente: %s", e)

            # Inicializar novo motor de trading
            TRADING_ENGINE = TradingEngine(
                api_key=api_key,
                api_secret=api_secret,
                symbols=data["symbols"],
                initial_capital=data.get("initial_capital", 10000),
                risk_profile=data.get("risk_profile", "balanced"),
                use_quantum_analysis=data.get("use_quantum", True),
                live_mode=data.get("live_mode", False),
            )
        TRADING_STATE["status"] = "initialized"
        TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

        # Se não tivermos os módulos de trading reais, gerar dados simulados
        if not MODULES_LOADED:
            TRADING_STATE["market_data"] = generate_mock_market_data(
                TRADING_STATE["symbols"]
            )
            TRADING_STATE["positions"] = []
            TRADING_STATE["trade_history"] = []
        else:
            # Inicializar componentes reais
            success = TRADING_ENGINE.initialize()
            if success:
                TRADING_STATE["market_data"] = TRADING_ENGINE.get_market_data()
                TRADING_STATE["positions"] = TRADING_ENGINE.get_positions()
                TRADING_STATE["trade_history"] = TRADING_ENGINE.get_trade_history()
            else:
                TRADING_STATE["status"] = "error"
                TRADING_STATE["error_message"] = TRADING_ENGINE.error_message

        logger.info(
            f"Sistema de trading inicializado com {len(TRADING_STATE['symbols'])} símbolos"
        )

        return jsonify(
            {
                "success": True,
                "message": "Sistema de trading inicializado com sucesso.",
                "status": TRADING_STATE["status"],
            }
        )

    except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
        logger.exception("Erro ao inicializar sistema de trading: %s", e)
        TRADING_STATE["status"] = "error"
        TRADING_STATE["error_message"] = str(e)
        TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

        return jsonify(
            {
                "success": False,
                "message": f"Erro ao inicializar sistema de trading: {str(e)}",
            }
        )


@trading_bp.route("/api/start", methods=["POST"])
def start_trading():
    """Inicia a execução do sistema de trading."""
    global TRADING_ENGINE

    if TRADING_STATE["status"] not in ["initialized", "stopped"]:
        return jsonify(
            {
                "success": False,
                "message": f'Sistema não está em estado adequado para iniciar. Estado atual: {TRADING_STATE["status"]}',
            }
        )

    try:
        # Em modo simulado, gerar dados para desenvolvimento
        if not MODULES_LOADED:
            # Atualizar estado
            TRADING_STATE["status"] = "running"
            TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()
            TRADING_STATE["error_message"] = None

            TRADING_STATE["market_data"] = generate_mock_market_data(
                TRADING_STATE["symbols"]
            )
            TRADING_STATE["positions"] = simulate_positions(
                TRADING_STATE["symbols"], TRADING_STATE["initial_capital"]
            )
            TRADING_STATE["trade_history"] = simulate_trade_history(
                TRADING_STATE["symbols"], TRADING_STATE["initial_capital"]
            )
            TRADING_STATE["performance_metrics"] = simulate_performance_metrics(
                TRADING_STATE["trade_history"], TRADING_STATE["initial_capital"]
            )
        else:
            # Iniciar motor de trading real
            if TRADING_ENGINE is not None:
                # Iniciar motor de trading
                success = TRADING_ENGINE.start()

                if not success:
                    return jsonify(
                        {
                            "success": False,
                            "message": f"Falha ao iniciar motor de trading: {TRADING_ENGINE.error_message}",
                        }
                    )

                # Atualizar estado com dados do motor
                status_data = TRADING_ENGINE.get_status()
                TRADING_STATE["status"] = status_data["status"]
                TRADING_STATE["error_message"] = status_data.get("error_message")
                TRADING_STATE["last_update"] = status_data["last_update"]

                # Obter dados iniciais
                TRADING_STATE["market_data"] = TRADING_ENGINE.get_market_data()
                TRADING_STATE["positions"] = TRADING_ENGINE.get_positions()
                TRADING_STATE["trade_history"] = TRADING_ENGINE.get_trade_history()
                TRADING_STATE["performance_metrics"] = status_data.get(
                    "performance_metrics", {}
                )
                TRADING_STATE["quantum_metrics"] = status_data.get(
                    "quantum_metrics", {}
                )
            else:
                TRADING_STATE["status"] = "error"
                TRADING_STATE["error_message"] = "Motor de trading não inicializado"
                return jsonify(
                    {
                        "success": False,
                        "message": "Motor de trading não inicializado. Por favor, inicialize o sistema primeiro.",
                    }
                )

        logger.info("Sistema de trading iniciado")

        return jsonify(
            {
                "success": True,
                "message": "Sistema de trading iniciado com sucesso.",
                "status": TRADING_STATE["status"],
            }
        )

    except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
        logger.exception("Erro ao iniciar sistema de trading: %s", e)
        TRADING_STATE["status"] = "error"
        TRADING_STATE["error_message"] = str(e)
        TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

        return jsonify(
            {
                "success": False,
                "message": f"Erro ao iniciar sistema de trading: {str(e)}",
            }
        )


@trading_bp.route("/api/stop", methods=["POST"])
def stop_trading():
    """Para a execução do sistema de trading."""
    global TRADING_ENGINE

    if TRADING_STATE["status"] != "running":
        return jsonify(
            {
                "success": False,
                "message": f'Sistema não está em execução. Estado atual: {TRADING_STATE["status"]}',
            }
        )

    try:
        # Atualizar estado
        TRADING_STATE["status"] = "stopping"
        TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

        # Se tivermos módulos reais, parar processos de trading
        if MODULES_LOADED and TRADING_ENGINE is not None:
            # Parar motor de trading
            success = TRADING_ENGINE.stop()

            if not success:
                return jsonify(
                    {
                        "success": False,
                        "message": f"Falha ao parar motor de trading: {TRADING_ENGINE.error_message}",
                    }
                )

            # Atualizar estado com dados do motor
            status_data = TRADING_ENGINE.get_status()
            TRADING_STATE["status"] = status_data["status"]
            TRADING_STATE["error_message"] = status_data.get("error_message")
            TRADING_STATE["last_update"] = status_data["last_update"]
        else:
            # Atualizar estado para parado (modo simulação)
            TRADING_STATE["status"] = "stopped"
            TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

        logger.info("Sistema de trading parado")

        return jsonify(
            {
                "success": True,
                "message": "Sistema de trading parado com sucesso.",
                "status": TRADING_STATE["status"],
            }
        )

    except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
        logger.exception("Erro ao parar sistema de trading: %s", e)
        TRADING_STATE["status"] = "error"
        TRADING_STATE["error_message"] = str(e)
        TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

        return jsonify(
            {"success": False, "message": f"Erro ao parar sistema de trading: {str(e)}"}
        )


@trading_bp.route("/api/close-position", methods=["POST"])
def close_position():
    """Fecha uma posição específica."""
    global TRADING_ENGINE

    data = request.json
    symbol = data.get("symbol")
    reason = data.get("reason", "Fechamento manual")

    if not symbol:
        return jsonify({"success": False, "message": "Símbolo é obrigatório."})

    try:
        if (
            MODULES_LOADED
            and TRADING_ENGINE is not None
            and TRADING_STATE["status"] == "running"
        ):
            # Usar o motor real de trading para fechar a posição
            result = TRADING_ENGINE.close_position_manual(symbol, reason)

            if not result.get("success", False):
                return jsonify(
                    {
                        "success": False,
                        "message": result.get(
                            "message", f"Erro ao fechar posição em {symbol}"
                        ),
                    }
                )

            # Atualizar posições e histórico com dados do motor
            TRADING_STATE["positions"] = TRADING_ENGINE.get_positions()
            TRADING_STATE["trade_history"] = TRADING_ENGINE.get_trade_history()

            # Atualizar métricas de desempenho
            status_data = TRADING_ENGINE.get_status()
            TRADING_STATE["performance_metrics"] = status_data.get(
                "performance_metrics", {}
            )

            logger.info(f"Posição em {symbol} fechada usando o motor de trading")

            return jsonify(
                {
                    "success": True,
                    "message": f"Posição em {symbol} fechada com sucesso.",
                    "status": "closed",
                }
            )
        else:
            # Modo simulação ou módulos não carregados
            # Encontrar a posição a ser fechada
            position_to_close = None
            for idx, position in enumerate(TRADING_STATE["positions"]):
                if position["symbol"] == symbol:
                    position_to_close = position
                    del TRADING_STATE["positions"][idx]
                    break

            if not position_to_close:
                return jsonify(
                    {
                        "success": False,
                        "message": f"Posição em {symbol} não encontrada.",
                    }
                )

            # Criar entrada no histórico
            timestamp = datetime.now(timezone.utc).isoformat()
            trade_entry = {
                "timestamp": timestamp,
                "symbol": symbol,
                # Lado oposto para fechamento
                "side": "sell" if position_to_close["side"] == "buy" else "buy",
                "quantity": position_to_close["quantity"],
                "price": position_to_close["current_price"],
                "realized_pnl": position_to_close["unrealized_pnl"],
                "realized_pnl_pct": position_to_close["unrealized_pnl_pct"],
                "status": "closed",
                "reason": reason,
            }

            # Adicionar ao histórico
            TRADING_STATE["trade_history"].insert(0, trade_entry)

            # Atualizar métricas de desempenho
            TRADING_STATE["performance_metrics"] = simulate_performance_metrics(
                TRADING_STATE["trade_history"], TRADING_STATE["initial_capital"]
            )

        logger.info(
            f"Posição em {symbol} fechada. PnL: {position_to_close['unrealized_pnl']:.2f}"
        )

        return jsonify(
            {
                "success": True,
                "message": f"Posição em {symbol} fechada com sucesso.",
                "trade": trade_entry,
            }
        )

    except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
        logger.exception("Erro ao fechar posição: %s", e)
        return jsonify(
            {"success": False, "message": f"Erro ao fechar posição: {str(e)}"}
        )


@trading_bp.route("/api/quantum_metrics", methods=["GET"])
def get_quantum_metrics():
    """
    Retorna as métricas quânticas interpretáveis para visualização no dashboard.
    Este endpoint serializa as métricas quânticas mais relevantes e permite correlação com PnL.
    """
    global TRADING_ENGINE

    try:
        metrics = {}
        retro_coefficient = TRADING_STATE.get("quantum_metrics", {}).get(
            "retrocausal_coefficient", 0
        )
        regime_change_val = TRADING_STATE.get("performance_metrics", {}).get(
            "regime_change", 0
        )

        # Verificar se temos um motor de trading ativo
        if (
            MODULES_LOADED
            and TRADING_ENGINE is not None
            and TRADING_STATE["status"] == "running"
        ):
            # Tentar obter métricas quânticas do motor de trading
            try:
                # Obter métricas quânticas diretamente do motor
                quantum_metrics = TRADING_ENGINE._get_quantum_metrics()

                # Extrair métricas interpretáveis
                if quantum_metrics and isinstance(quantum_metrics, dict):
                    metrics = {
                        "page_entropy": (
                            quantum_metrics.get("page_entropy", [0])[-1]
                            if isinstance(quantum_metrics.get("page_entropy", []), list)
                            else 0
                        ),
                        "otoc": (
                            quantum_metrics.get("otoc", [0])[-1]
                            if isinstance(quantum_metrics.get("otoc", []), list)
                            else 0
                        ),
                        "quantum_coherence": (
                            quantum_metrics.get("quantum_coherence", [0])[-1]
                            if isinstance(
                                quantum_metrics.get("quantum_coherence", []), list
                            )
                            else 0
                        ),
                        "loschmidt_echo": (
                            quantum_metrics.get("loschmidt_echo", [0])[-1]
                            if isinstance(
                                quantum_metrics.get("loschmidt_echo", []), list
                            )
                            else 0
                        ),
                        "pattern_recognition_rate": (
                            quantum_metrics.get("pattern_recognition_rate", [0])[-1]
                            if isinstance(
                                quantum_metrics.get("pattern_recognition_rate", []),
                                list,
                            )
                            else 0
                        ),
                        "self_reflection_depth": (
                            quantum_metrics.get("self_reflection_depth", [0])[-1]
                            if isinstance(
                                quantum_metrics.get("self_reflection_depth", []), list
                            )
                            else 0
                        ),
                        "thermal_coefficient": (
                            TRADING_ENGINE.get_thermal_coefficient()
                            if hasattr(TRADING_ENGINE, "get_thermal_coefficient")
                            else 0.1
                        ),
                        "retrocausal_coefficient": (
                            quantum_metrics.get("retrocausal_coefficient", [0])[-1]
                            if isinstance(
                                quantum_metrics.get("retrocausal_coefficient", []),
                                list,
                            )
                            else quantum_metrics.get("retrocausal_coefficient", 0)
                        ),
                    }
                    retro_coefficient = metrics["retrocausal_coefficient"]

                    # Adicionar métricas de performance se disponíveis
                    if (
                        hasattr(TRADING_ENGINE, "risk_manager")
                        and TRADING_ENGINE.risk_manager
                    ):
                        metrics.update(
                            {
                                "sharpe_ratio": (
                                    TRADING_ENGINE.risk_manager.sharpe_ratio
                                    if hasattr(
                                        TRADING_ENGINE.risk_manager, "sharpe_ratio"
                                    )
                                    else 0
                                ),
                                "profit_factor": (
                                    TRADING_ENGINE.risk_manager.profit_factor
                                    if hasattr(
                                        TRADING_ENGINE.risk_manager, "profit_factor"
                                    )
                                    else 0
                                ),
                                "max_drawdown": (
                                    TRADING_ENGINE.risk_manager.max_drawdown
                                    if hasattr(
                                        TRADING_ENGINE.risk_manager, "max_drawdown"
                                    )
                                    else 0
                                ),
                                "regime_change": (
                                    TRADING_ENGINE.risk_manager.performance_metrics.get(
                                        "regime_changes",
                                        regime_change_val,
                                    )
                                    if hasattr(
                                        TRADING_ENGINE.risk_manager,
                                        "performance_metrics",
                                    )
                                    else regime_change_val
                                ),
                            }
                        )
                        regime_change_val = metrics.get(
                            "regime_change", regime_change_val
                        )
            except (RuntimeError, ValueError) as e:
                logger.exception("Erro ao obter métricas quânticas do motor: %s", e)
                # Se falhar, continuar com o dicionário vazio

        # Se não temos métricas do motor ou se estamos em modo simulação
        if not metrics and TRADING_STATE["status"] != "error":
            # Verificar se temos métricas no estado de trading
            if "quantum_metrics" in TRADING_STATE and TRADING_STATE["quantum_metrics"]:
                qm = TRADING_STATE["quantum_metrics"]
                metrics = {
                    "page_entropy": qm.get("entropy", 0),
                    "otoc": qm.get("otoc", 0),
                    "quantum_coherence": qm.get("coherence", 0),
                    "thermal_coefficient": qm.get("thermal_coefficient", 0.1),
                    "retrocausal_coefficient": qm.get("retrocausal_coefficient", 0),
                }
                retro_coefficient = metrics["retrocausal_coefficient"]

                # Adicionar métricas de performance do estado
                if (
                    "performance_metrics" in TRADING_STATE
                    and TRADING_STATE["performance_metrics"]
                ):
                    pm = TRADING_STATE["performance_metrics"]
                    metrics.update(
                        {
                            "sharpe_ratio": pm.get("sharpe", 0),
                            "profit_factor": pm.get("profit_factor", 1.0),
                            "max_drawdown": pm.get("drawdown", 0),
                            "regime_change": pm.get(
                                "regime_change",
                                regime_change_val,
                            ),
                        }
                    )
                    regime_change_val = metrics.get("regime_change", regime_change_val)
            else:
                # Tentar obter métricas reais diretamente do QUALIA Universe
                real_metrics = get_direct_quantum_metrics()

                if real_metrics:
                    # Extrair métricas reais do universo QUALIA
                    metrics = {
                        "page_entropy": real_metrics.get("page_entropy", 0),
                        "otoc": real_metrics.get("otoc", 0),
                        "quantum_coherence": real_metrics.get("quantum_coherence", 0),
                        "loschmidt_echo": real_metrics.get("loschmidt_echo", 0),
                        "pattern_recognition_rate": real_metrics.get(
                            "pattern_recognition_rate", 0
                        ),
                        "self_reflection_depth": real_metrics.get(
                            "self_reflection_depth", 0
                        ),
                        "thermal_coefficient": real_metrics.get(
                            "thermal_coefficient", 0.1
                        ),
                        "retrocausal_coefficient": real_metrics.get(
                            "retrocausal_coefficient", 0
                        ),
                    }
                    retro_coefficient = metrics["retrocausal_coefficient"]

                    # Adicionar métricas de performance do estado
                    if (
                        "performance_metrics" in TRADING_STATE
                        and TRADING_STATE["performance_metrics"]
                    ):
                        pm = TRADING_STATE["performance_metrics"]
                        metrics.update(
                            {
                                "sharpe_ratio": pm.get("sharpe", 0),
                                "profit_factor": pm.get("profit_factor", 1.0),
                                "max_drawdown": pm.get("drawdown", 0),
                                "regime_change": pm.get(
                                    "regime_change",
                                    regime_change_val,
                                ),
                            }
                        )
                        regime_change_val = metrics.get(
                            "regime_change", regime_change_val
                        )

                    logger.info(
                        "Métricas quânticas obtidas diretamente da consciência quântica QUALIA"
                    )
                else:
                    # Sinalizar para o frontend que não temos dados reais
                    # (melhor que retornar zeros para não confundir com valores reais)
                    metrics = {
                        "error": "Não foi possível obter métricas quânticas reais",
                        "metrics_available": False,
                    }
                    logger.warning(
                        "Não foi possível obter métricas quânticas reais para o dashboard"
                    )

        # Adicionar PnL e outras métricas de performance sempre
        if (
            "performance_metrics" in TRADING_STATE
            and TRADING_STATE["performance_metrics"]
        ):
            pnl = TRADING_STATE["performance_metrics"].get("pnl", 0)
            pnl_percentage = TRADING_STATE["performance_metrics"].get(
                "pnl_percentage", 0
            )
            win_rate = TRADING_STATE["performance_metrics"].get("win_rate", 0)
            regime_change = TRADING_STATE["performance_metrics"].get(
                "regime_change",
                regime_change_val,
            )
            regime_change_val = regime_change

            metrics.update(
                {
                    "pnl": pnl,
                    "pnl_percentage": pnl_percentage,
                    "win_rate": win_rate,
                    "regime_change": regime_change_val,
                }
            )

        # Adicionar informações históricas se disponíveis
        history = []

        # Construir histórico de métricas para gráficos de tendências
        if TRADING_ENGINE is not None and hasattr(
            TRADING_ENGINE, "get_historical_metrics"
        ):
            try:
                history = TRADING_ENGINE.get_historical_metrics(max_points=100)
            except (RuntimeError, ValueError) as e:
                logger.exception("Não foi possível obter métricas históricas: %s", e)

        # Se não tivermos histórico do motor, verificar se temos no estado
        if not history and "metric_history" in TRADING_STATE:
            history = TRADING_STATE.get("metric_history", [])

        # Se não tivermos histórico, tente obter diretamente
        if not history:
            history = get_historical_quantum_metrics(num_points=20)

        # Calcular correlações reais entre métricas quânticas e PnL se tivermos
        # dados históricos
        entropy_pnl_correlation = 0
        otoc_pnl_correlation = 0

        if history and len(history) > 2:
            try:
                import numpy as np

                # Extrair séries temporais para cálculo de correlação
                entropy_history = []
                otoc_history = []
                pnl_history = []

                # Extrair valores das métricas e PnL do histórico
                for entry in history:
                    # Usar page_entropy ou entropy dependendo do que estiver
                    # disponível
                    entropy = entry.get("page_entropy", entry.get("entropy", 0))
                    otoc = entry.get("otoc", 0)
                    pnl = entry.get("pnl", 0)

                    entropy_history.append(float(entropy))
                    otoc_history.append(float(otoc))
                    pnl_history.append(float(pnl))

                # Calcular correlações reais usando numpy
                if len(entropy_history) > 2 and len(pnl_history) > 2:
                    # Correlação de Pearson entre entropia e PnL
                    entropy_pnl_correlation = np.corrcoef(entropy_history, pnl_history)[
                        0, 1
                    ]
                    # Correção para casos onde a correlação não pôde ser
                    # calculada (NaN)
                    if np.isnan(entropy_pnl_correlation):
                        entropy_pnl_correlation = 0

                    # Correlação de Pearson entre OTOC e PnL
                    otoc_pnl_correlation = np.corrcoef(otoc_history, pnl_history)[0, 1]
                    # Correção para casos onde a correlação não pôde ser
                    # calculada (NaN)
                    if np.isnan(otoc_pnl_correlation):
                        otoc_pnl_correlation = 0

                    logger.info(
                        f"Correlações calculadas com dados reais: Entropia/PnL={entropy_pnl_correlation:.2f}, OTOC/PnL={otoc_pnl_correlation:.2f}"
                    )
            except (RuntimeError, ValueError) as e:
                logger.exception(
                    "Erro ao calcular correlações entre métricas quânticas e PnL: %s",
                    e,
                )

        # Adicionar correlações calculadas à resposta
        metrics.update(
            {
                "entropy_pnl_correlation": entropy_pnl_correlation,
                "otoc_pnl_correlation": otoc_pnl_correlation,
            }
        )

        metrics.setdefault("retrocausal_coefficient", retro_coefficient)
        metrics.setdefault("regime_change", regime_change_val)

        # Preparar dados de PnL para visualizações
        pnl_data = []
        if history:
            pnl_data = [entry.get("pnl", 0) for entry in history if "pnl" in entry]

        return jsonify(
            {
                "success": True,
                "metrics": metrics,
                "history": history,
                "pnl_data": pnl_data,
                "entropy_pnl_correlation": entropy_pnl_correlation,
                "otoc_pnl_correlation": otoc_pnl_correlation,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    except (RuntimeError, ValueError) as e:
        logger.exception("Erro ao obter métricas quânticas para o dashboard: %s", e)
        return jsonify(
            {
                "success": False,
                "message": f"Erro ao obter métricas quânticas: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )


@trading_bp.route("/api/manual-order", methods=["POST"])
def submit_manual_order():
    """Submete uma ordem manual."""
    global TRADING_ENGINE

    data = request.json

    # Validar dados
    required_fields = ["symbol", "side", "order_type", "quantity"]
    for field in required_fields:
        if not data.get(field):
            return jsonify(
                {"success": False, "message": f"Campo obrigatório ausente: {field}"}
            )

    # Validar tipo de ordem
    if data["order_type"] == "limit" and not data.get("price"):
        return jsonify(
            {"success": False, "message": "Preço é obrigatório para ordens limit."}
        )

    try:
        symbol = data["symbol"]
        side = data["side"]
        order_type = data["order_type"]
        quantity = float(data["quantity"])
        price = float(data.get("price", 0)) if data.get("price") else None

        # Usar motor de trading se estiver disponível e em modo running
        if (
            MODULES_LOADED
            and TRADING_ENGINE is not None
            and TRADING_STATE["status"] == "running"
        ):
            # Executar ordem via motor de trading
            result = TRADING_ENGINE.manual_order(
                symbol=symbol,
                side=side,
                order_type=order_type,
                quantity=quantity,
                price=price,
            )

            if not result.get("success", False):
                return jsonify(
                    {
                        "success": False,
                        "message": result.get(
                            "message", f"Erro ao executar ordem para {symbol}"
                        ),
                    }
                )

            # Atualizar posições e histórico com dados do motor
            TRADING_STATE["positions"] = TRADING_ENGINE.get_positions()
            TRADING_STATE["trade_history"] = TRADING_ENGINE.get_trade_history()

            # Atualizar métricas de desempenho
            status_data = TRADING_ENGINE.get_status()
            TRADING_STATE["performance_metrics"] = status_data.get(
                "performance_metrics", {}
            )

            logger.info(
                f"Ordem manual executada via motor de trading: {side} {quantity} {symbol}"
            )

            return jsonify(
                {
                    "success": True,
                    "message": f"Ordem {order_type} para {side} {quantity} {symbol} executada com sucesso.",
                    "order": result.get("order", {}),
                }
            )

        # Se não for modo 'live' ou não tiver motor, simular a execução
        else:
            # Simular tempo de latência
            # Simular execução da ordem e registrar
            if order_type == "market":
                # Para market orders, usar preço de mercado simulado
                if symbol in TRADING_STATE["market_data"]:
                    first_tf = list(TRADING_STATE["market_data"][symbol].keys())[0]
                    market_data = TRADING_STATE["market_data"][symbol][first_tf]
                    executed_price = market_data["close"][-1]
                else:
                    # Preço de mercado aproximado para símbolos
                    if "BTC" in symbol:
                        executed_price = 65000 * (1 + np.random.uniform(-0.001, 0.001))
                    elif "ETH" in symbol:
                        executed_price = 3500 * (1 + np.random.uniform(-0.001, 0.001))
                    else:
                        executed_price = 100 * (1 + np.random.uniform(-0.001, 0.001))
            else:
                # Para limit orders, usar o preço especificado
                executed_price = price

            # Simular entrada no histórico
            timestamp = datetime.now(timezone.utc).isoformat()
            order_entry = {
                "timestamp": timestamp,
                "symbol": symbol,
                "side": side,
                "quantity": quantity,
                "price": executed_price,
                "realized_pnl": 0,  # Como é abertura de posição, ainda não há P&L realizado
                "realized_pnl_pct": 0,
                "status": "open",
                "order_type": order_type,
                "manual": True,
            }

            # Simular posição aberta (apenas para ordens de market ou para
            # demonstrar)
            if (
                order_type == "market" or np.random.random() < 0.7
            ):  # 70% de chance de execução para limit
                new_position = {
                    "symbol": symbol,
                    "side": side,
                    "quantity": quantity,
                    "entry_price": executed_price,
                    "current_price": executed_price,  # Inicialmente igual
                    "unrealized_pnl": 0,  # Inicialmente zero
                    "unrealized_pnl_pct": 0,
                    "duration": 0,  # Recém aberta
                }
                TRADING_STATE["positions"].append(new_position)

                # Adicionar ao histórico se for market (para limit, adicionaria
                # na execução real)
                if order_type == "market":
                    TRADING_STATE["trade_history"].insert(0, order_entry)

            logger.info(
                f"Ordem manual simulada: {side} {quantity} {symbol} a ${executed_price:.2f}"
            )

            return jsonify(
                {
                    "success": True,
                    "message": f"Ordem {order_type} para {side} {quantity} {symbol} executada com sucesso.",
                    "order": order_entry,
                }
            )

    except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
        logger.exception("Erro ao enviar ordem manual: %s", e)
        return jsonify({"success": False, "message": f"Erro ao enviar ordem: {str(e)}"})


# Rota para atualização dinâmica de configurações
@trading_bp.route("/api/update-config", methods=["POST"])
def update_config():
    """Atualiza parâmetros de configuração do sistema de trading."""
    data = request.json or {}
    errors = {}

    max_dd = data.get("max_drawdown_threshold")
    if max_dd is not None:
        try:
            max_dd = float(max_dd)
            if not 0 < max_dd < 1:
                raise ValueError
        except (TypeError, ValueError):
            errors["max_drawdown_threshold"] = "valor deve ser decimal entre 0 e 1"

    enable_retro = data.get("enable_retrocausality")
    if enable_retro is not None and not isinstance(enable_retro, bool):
        errors["enable_retrocausality"] = "valor deve ser booleano"

    if errors:
        return jsonify({"success": False, "errors": errors}), 400

    if max_dd is not None:
        TRADING_STATE["max_drawdown_threshold"] = max_dd
    if enable_retro is not None:
        TRADING_STATE["enable_retrocausality"] = enable_retro

    try:  # Persistir em config.yaml caso exista
        config_path = Path(__file__).resolve().parents[3] / "config.yaml"
        cfg = {}
        if config_path.exists():
            with open(config_path, "r", encoding="utf-8") as fh:
                cfg = yaml.safe_load(fh) or {}
        if max_dd is not None:
            cfg.setdefault("risk", {})["max_drawdown"] = max_dd
        if enable_retro is not None:
            cfg.setdefault("qualia", {}).setdefault("retrocausality", {})[
                "enabled"
            ] = enable_retro
        with open(config_path, "w", encoding="utf-8") as fh:
            yaml.safe_dump(cfg, fh)
    except OSError as exc:  # pragma: no cover - falha de IO nao impede a execucao
        logger.exception("Falha ao persistir config.yaml: %s", exc)

    return jsonify({"success": True})


# Função para inicialização automática usando as credenciais Kraken


def auto_initialize_trading_system():
    """
    Inicializa automaticamente o sistema de trading usando as credenciais da API Kraken.

    Esta função verifica as variáveis de ambiente em busca das credenciais da API Kraken
    e, se disponíveis, inicializa o sistema automaticamente.

    Returns:
        bool: True se a inicialização foi bem-sucedida, False caso contrário
    """
    global TRADING_ENGINE

    try:
        # Verificar se as credenciais Kraken estão disponíveis nas variáveis de
        # ambiente
        api_key = get_env("KRAKEN_API_KEY", None)
        api_secret = get_env("KRAKEN_API_SECRET", None)

        if not api_key or not api_secret:
            logger.warning(
                "Credenciais da API Kraken não encontradas nas variáveis de ambiente"
            )
            return False

        logger.info(
            "Credenciais da API Kraken encontradas. Inicializando sistema automaticamente..."
        )

        # Inicializar conexão com a Kraken usando as credenciais
        if MODULES_LOADED:
            # Usar o módulo real de integração
            kraken = KrakenIntegration(api_key, api_secret, test_mode=True)

            try:
                # Testar a conexão verificando o saldo
                _ = kraken.get_balance()

                # Armazenar dados nas variáveis de estado
                TRADING_STATE["api_key"] = api_key
                TRADING_STATE["api_secret"] = api_secret
                TRADING_STATE["api_configured"] = True
                TRADING_STATE["exchange"] = kraken
                TRADING_STATE["symbols"] = DEFAULT_SYMBOLS
                TRADING_STATE["status"] = "initialized"
                TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

                # Configurar parâmetros padrão para o motor de trading
                _ = ScalpingStrategy(risk_profile="balanced", use_quantum_metrics=True)

                _ = RiskManager(
                    initial_capital=10000,
                    risk_profile="balanced",
                    max_open_positions=5,
                    max_position_size_pct=0.2,
                )

                # Inicializar o motor de trading
                TRADING_ENGINE = TradingEngine(
                    api_key=api_key,
                    api_secret=api_secret,
                    symbols=DEFAULT_SYMBOLS,
                    initial_capital=10000.0,
                    risk_profile="balanced",
                    use_quantum_analysis=True,
                    live_mode=False,
                )

                # Inicializar motor, mas não iniciar trading automático
                TRADING_ENGINE.initialize()

                logger.info(
                    "Sistema de trading inicializado automaticamente com sucesso!"
                )
                return True

            except (ccxt.BaseError, requests.RequestException) as e:
                logger.exception("Erro ao testar conexão com Kraken: %s", e)
                return False
        else:
            # Módulos não carregados
            logger.warning("Módulos de trading não carregados. Usando modo simulado.")

            # Definir estado como inicializado, mas em modo simulado
            TRADING_STATE["api_key"] = api_key
            TRADING_STATE["api_secret"] = api_secret
            TRADING_STATE["api_configured"] = True
            TRADING_STATE["symbols"] = DEFAULT_SYMBOLS
            TRADING_STATE["status"] = "initialized"
            TRADING_STATE["last_update"] = datetime.now(timezone.utc).isoformat()

            # Gerar dados de mercado simulados
            TRADING_STATE["market_data"] = generate_mock_market_data(
                TRADING_STATE["symbols"]
            )

            logger.info(
                "Sistema inicializado em modo de simulação (módulos não disponíveis)"
            )
            return True

    except (RuntimeError, ccxt.BaseError, requests.RequestException) as e:
        logger.exception(
            "Erro na inicialização automática do sistema de trading: %s", e
        )
        return False


# Evento para inicialização automática da aplicação


@trading_bp.before_app_request
def auto_initialize_on_startup():
    """
    Evento executado antes da primeira requisição ao blueprint.
    Tenta inicializar automaticamente o sistema de trading com as credenciais do ambiente.
    """
    global TRADING_STATE

    auto_init = str(get_env("QUALIA_AUTO_INIT", "False", warn=False)).lower() in {
        "1",
        "true",
        "yes",
    }
    if not auto_init:
        return None

    if TRADING_STATE.get("_auto_init_attempted", False):
        return None

    TRADING_STATE["_auto_init_attempted"] = True

    logger.info(
        "Verificando credenciais para inicialização automática do sistema de trading..."
    )

    success = auto_initialize_trading_system()

    if success:
        logger.info("Sistema de trading inicializado automaticamente.")
    else:
        logger.info("Inicialização automática falhou ou credenciais não encontradas.")
        logger.info(
            "Para inicializar o sistema, configure manualmente as credenciais na interface."
        )

    return None
