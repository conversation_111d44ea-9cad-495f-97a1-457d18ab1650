from __future__ import annotations

"""Financial OTOC calculations for QUALIA.

This module implements utilities to compute out-of-time-order correlators
(OTOCs) on market return series. Such correlators diagnose the level of
scrambling in the time structure of returns, informing whether the market
behaves in a chaotic or exploitable regime.
"""

from typing import Sequence

import numpy as np
from numpy.typing import NDArray

from ..utils.numba_utils import optional_njit


@optional_njit(cache=True)
def _otoc_basic(r0: NDArray, rlag: NDArray) -> float:
    total = 0.0
    n = r0.size
    for i in range(n):
        prod = r0[i] * rlag[i]
        total += prod * prod
    return 0.0 if n == 0 else total / float(n)


def compute_otoc(returns: Sequence[float], lag: int) -> float:
    """Return the financial OTOC for ``returns`` at the specified ``lag``.

    Parameters
    ----------
    returns
        Sequence of market returns as decimal values.
    lag
        Temporal offset in number of samples between the correlated points.

    Returns
    -------
    float
        Mean value of ``r(t) * r(t+lag) * r(t) * r(t+lag)`` over the series.

    Raises
    ------
    ValueError
        If ``lag`` is not a positive integer strictly smaller than
        ``len(returns)``.
    """
    arr = np.asarray(returns, dtype=float)
    if lag <= 0 or lag >= arr.size:
        raise ValueError("lag must be between 1 and len(returns)-1")
    r0 = arr[:-lag]
    rlag = arr[lag:]
    return float(_otoc_basic(r0, rlag))


def sliding_otoc(returns: Sequence[float], lag: int, window_size: int) -> NDArray:
    """Compute OTOC values over sliding windows of ``returns``.

    Parameters
    ----------
    returns
        Sequence of market returns as decimal values.
    lag
        Temporal offset between points.
    window_size
        Number of samples in each sliding window.

    Returns
    -------
    ndarray
        Array with OTOC values for each window.

    Raises
    ------
    ValueError
        If ``window_size`` or ``lag`` are invalid or exceed the series length.
    """
    arr = np.asarray(returns, dtype=float)
    if window_size <= 0:
        raise ValueError("window_size must be positive")
    if lag <= 0 or window_size + lag > arr.size:
        raise ValueError("lag/window_size combination exceeds series length")
    n_windows = arr.size - lag - window_size + 1
    out = np.empty(n_windows, dtype=float)
    for i in range(n_windows):
        r0 = arr[i : i + window_size]
        rlag = arr[i + lag : i + lag + window_size]
        out[i] = _otoc_basic(r0, rlag)
    return out


__all__ = ["compute_otoc", "sliding_otoc"]
