"""
Logging Utilities

Provides centralized logging configuration and utilities for the QUALIA system.
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional, Iterable
from datetime import datetime, timezone
import inspect

from .correlation_id import get_correlation_id
from .text_sanitizer import sanitize_text
from .tracing import configure_tracing, instrument_logger, is_tracing_enabled

try:
    from pythonjsonlogger import jsonlogger

    class JSONLogFormatter(jsonlogger.JsonFormatter):
        """JSON formatter that includes ``correlation_id`` when available."""

        def add_fields(self, log_record: dict, record: logging.LogRecord, message_dict: dict) -> None:
            super().add_fields(log_record, record, message_dict)
            log_record.setdefault("timestamp", UTCFormatter().formatTime(record))
            log_record.setdefault("level", record.levelname)
            log_record.setdefault("name", record.name)
            log_record.setdefault("module", getattr(record, "module", record.name.split(".")[-1]))
            log_record.setdefault("function", record.funcName)
            log_record.setdefault("line", record.lineno)
            cid = get_correlation_id()
            if cid:
                log_record.setdefault("correlation_id", cid)

except ModuleNotFoundError:
    # Fallback to a standard formatter if python-json-logger is not installed.
    # This makes the system more resilient in environments where not all
    # production dependencies are available (e.g., some test environments).
    class JSONLogFormatter(logging.Formatter):
        """A fallback text-based formatter used when python-json-logger is not available."""
        def __init__(self, fmt=None, datefmt=None, style='%', validate=True):
            # Initialize with a default format that is informative.
            default_fmt = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            super().__init__(fmt if fmt else default_fmt, datefmt)


_INITIALIZED = False

# Loggers de bibliotecas externas que devem ser controlados pelo sistema.
_LIBRARY_LOGGERS: Iterable[str] = (
    "datadog",
    "datadog.dogstatsd",
    "statsd",
    "stevedore",
)


class UTCFormatter(logging.Formatter):
    """Formatter that outputs timestamps in ISO-8601 UTC format."""

    def converter(self, timestamp: float) -> datetime:
        """Return a timezone-aware :class:`datetime` in UTC."""

        return datetime.fromtimestamp(timestamp, tz=timezone.utc)

    def formatTime(self, record: logging.LogRecord, datefmt: str | None = None) -> str:  # type: ignore[override]
        """Format ``record.created`` as an ISO timestamp with millisecond precision."""

        dt = self.converter(record.created)
        if datefmt:
            return dt.strftime(datefmt)
        return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"


class CorrelationIdFilter(logging.Filter):
    """Inject ``correlation_id`` into log records."""

    def filter(self, record: logging.LogRecord) -> bool:
        """
        Ensures that a correlation_id is always present in the log record.

        If `get_correlation_id()` returns a value, it's used. Otherwise,
        a default empty string is set to prevent `KeyError` in formatters.
        """
        if not hasattr(record, "correlation_id"):
            cid = get_correlation_id()
            record.correlation_id = cid if cid else ""
        return True


def setup_logging(config: Optional[Dict[str, Any]] = None) -> bool:
    """
    Setup centralized logging for the QUALIA system

    Args:
        config: Logging configuration dictionary
    """
    global _INITIALIZED
    root_logger = logging.getLogger()
    if _INITIALIZED and not root_logger.handlers:
        _INITIALIZED = False
    if _INITIALIZED:
        return False
    if config is None:
        config = {}

    env_level = os.getenv("QUALIA_LOG_LEVEL")

    # Default configuration
    default_config = {
        "level": env_level or "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "json_format": "%(asctime)s %(name)s %(levelname)s %(correlation_id)s %(message)s",
        "log_dir": os.getenv("QUALIA_LOG_DIR", "logs"),
        "file": "qualia.log",
        "max_size": 10 * 1024 * 1024,  # 10MB
        "backup_count": 5,
        "console": True,
    }

    # Merge with provided config and allow env var to override
    log_config = {**default_config, **config}
    if env_level:
        log_config["level"] = env_level

    log_dir = Path(log_config.get("log_dir", "logs"))
    log_dir.mkdir(parents=True, exist_ok=True)

    log_file = Path(log_config["file"])
    if not log_file.is_absolute():
        log_file = log_dir / log_file
    log_file.parent.mkdir(parents=True, exist_ok=True)
    log_config["file"] = str(log_file)

    # Convert string level to logging constant
    level = getattr(logging, log_config["level"].upper(), logging.INFO)

    # Create formatters using UTC timestamps
    console_formatter = UTCFormatter(fmt=log_config["format"])
    json_formatter = JSONLogFormatter(log_config["json_format"])

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    if root_logger.handlers:
        root_logger.handlers.clear()
    root_logger.addFilter(CorrelationIdFilter())

    # Console handler
    if log_config.get("console", True):
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(level)
        console_handler.setFormatter(console_formatter)
        root_logger.addHandler(console_handler)

    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        filename=log_config["file"],
        maxBytes=log_config["max_size"],
        backupCount=log_config["backup_count"],
        encoding="utf-8",
    )
    file_handler.setLevel(level)
    file_handler.setFormatter(json_formatter)
    file_handler.addFilter(CorrelationIdFilter())
    root_logger.addHandler(file_handler)

    # Create specialized loggers for different components
    _setup_component_loggers(level, json_formatter, log_dir)

    # Configure loggers de bibliotecas externas
    _set_library_loggers(logging.WARNING)

    # Apply logging overrides from configuration
    logger = logging.getLogger(__name__)
    logging_overrides = log_config.get("logging_overrides", {})
    for logger_name, override_level in logging_overrides.items():
        override_logger = logging.getLogger(logger_name)
        if isinstance(override_level, str):
            override_level = getattr(logging, override_level.upper(), logging.INFO)
        override_logger.setLevel(override_level)
        logger.info(f"Applied logging override: {logger_name} -> {logging.getLevelName(override_level)}")

    tracing_cfg = log_config.get("tracing")
    exporter_env = os.getenv("QUALIA_TRACING_EXPORTER")
    if not tracing_cfg and exporter_env:
        tracing_cfg = {"exporter": exporter_env}
    if tracing_cfg:
        if configure_tracing(
            service_name=tracing_cfg.get("service_name", "qualia"),
            exporter=tracing_cfg.get("exporter", "console"),
        ):
            instrument_logger(root_logger)

    # Log initial message
    logger.info(f"QUALIA logging initialized - Level: {log_config['level']}")

    _INITIALIZED = True
    return True


def _set_library_loggers(level: int) -> None:
    """Configure external library loggers."""

    for name in _LIBRARY_LOGGERS:
        logging.getLogger(name).setLevel(level)


def _setup_component_loggers(level: int, formatter: logging.Formatter, log_dir: Path) -> None:
    """Setup specialized loggers for different system components"""

    # Define component-specific log files
    component_configs = {
        "qualia.core": {"file": "core.log", "level": level},
        "qualia.exchanges": {"file": "exchanges.log", "level": level},
        "qualia.trading": {"file": "trading.log", "level": level},
        "qualia.risk": {"file": "risk.log", "level": level},
        "qualia.signals": {"file": "signals.log", "level": level},
        "qualia.memory": {"file": "memory.log", "level": level},
        "qualia.metacognition": {"file": "metacognition.log", "level": level},
    }

    for logger_name, config in component_configs.items():
        # Create component logger
        component_logger = logging.getLogger(logger_name)
        component_logger.setLevel(config["level"])

        # Ensure log directory exists
        log_file = Path(config["file"])
        if not log_file.is_absolute():
            log_file = log_dir / log_file
        log_file.parent.mkdir(parents=True, exist_ok=True)
        config["file"] = str(log_file)

        # Create file handler for component
        component_handler = logging.handlers.RotatingFileHandler(
            filename=config["file"],
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=3,
            encoding="utf-8",
        )
        component_handler.setLevel(config["level"])
        if isinstance(formatter, JSONLogFormatter):
            component_handler.setFormatter(formatter)
        else:
            component_handler.setFormatter(formatter)

        # Add handler to component logger
        component_logger.addHandler(component_handler)

        # Prevent propagation to avoid duplicate messages
        component_logger.propagate = False


class QualiaLogger:
    """
    Enhanced logger class with QUALIA-specific features
    """

    def __init__(self, name: str, extra_context: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(name)
        self.extra_context = extra_context or {}

    def _format_message(self, message: str, context: Optional[Any] = None) -> str:
        """Format message with context information.

        Handles cases where *context* is not a dictionary. If a non-dict value is
        provided, it is converted to a printable string under the key
        ``context`` to avoid runtime errors while preserving the extra
        information for debugging.
        """
        full_context: Dict[str, Any] = {**self.extra_context}

        cid = get_correlation_id()
        if cid:
            full_context.setdefault("correlation_id", cid)

        if context is not None:
            if isinstance(context, dict):
                # Merge safely if it is a mapping
                full_context.update(context)
            else:
                # Fallback – store raw context under generic key
                full_context["context"] = str(context)

        if full_context:
            context_str = " | ".join([f"{k}={v}" for k, v in full_context.items()])
            return f"{message} | {context_str}"

        return message

    def debug(self, message: str, *args, context: Optional[Any] = None, **kwargs):
        """Log debug message with context"""
        if args or kwargs:
            # Handle old-style logging with format arguments
            self.logger.debug(sanitize_text(message), *args, **kwargs)
        else:
            sanitized = sanitize_text(message)
            self.logger.debug(self._format_message(sanitized, context))

    def info(
        self,
        message: str,
        *args,
        context: Optional[Any] = None,
        **kwargs,
    ):
        """Log info message with optional formatting args/kwargs and context"""
        if args or kwargs:
            # Preserve default logging behaviour when using printf-style formatting
            self.logger.info(sanitize_text(message), *args, **kwargs)
        else:
            sanitized = sanitize_text(message)
            self.logger.info(self._format_message(sanitized, context))

    def warning(
        self,
        message: str,
        *args,
        context: Optional[Any] = None,
        **kwargs,
    ):
        """Log warning message with context and full logging compatibility"""
        if args or kwargs:
            self.logger.warning(message, *args, **kwargs)
        else:
            self.logger.warning(self._format_message(message, context))

    def error(
        self,
        message: str,
        *args,
        context: Optional[Any] = None,
        **kwargs,
    ):
        """Log error message with full logging compatibility (exc_info etc.)"""
        if args or kwargs:
            self.logger.error(message, *args, **kwargs)
        else:
            self.logger.error(self._format_message(message, context))

    def critical(
        self,
        message: str,
        *args,
        context: Optional[Any] = None,
        **kwargs,
    ):
        """Log critical message with full logging compatibility"""
        if args or kwargs:
            self.logger.critical(message, *args, **kwargs)
        else:
            self.logger.critical(self._format_message(message, context))

    def exception(
        self,
        message: str,
        *args: Any,
        context: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Log exception with traceback, preserving extra kwargs."""

        self.logger.exception(self._format_message(message, context), *args, **kwargs)

    def addHandler(self, handler: logging.Handler) -> None:
        """Attach a handler to the underlying logger."""
        self.logger.addHandler(handler)

    def setLevel(self, level: int) -> None:
        """Set the logging level of the underlying logger."""
        self.logger.setLevel(level)

    def __getattr__(self, name: str) -> Any:
        """Delegate attribute access to the wrapped logger."""
        return getattr(self.logger, name)


class TradingLogger:
    """
    Specialized logger for trading operations
    """

    def __init__(self):
        self.logger = logging.getLogger("qualia.trading")

        # Create dedicated trading log file
        self._setup_trading_log()

    def _setup_trading_log(self):
        """Setup dedicated trading log file"""
        log_dir = Path(os.getenv("QUALIA_LOG_DIR", "logs"))
        trading_log_path = log_dir / "trading_operations.log"
        trading_log_path.parent.mkdir(parents=True, exist_ok=True)

        # Create formatter for trading operations
        trading_formatter = logging.Formatter("%(asctime)s | %(levelname)s | %(message)s", datefmt="%Y-%m-%d %H:%M:%S")

        # Create file handler
        trading_handler = logging.handlers.RotatingFileHandler(
            filename=trading_log_path,
            maxBytes=50 * 1024 * 1024,  # 50MB for trading logs
            backupCount=10,
            encoding="utf-8",
        )
        trading_handler.setFormatter(trading_formatter)

        # Add handler if not already present
        if not any(isinstance(h, logging.handlers.RotatingFileHandler) for h in self.logger.handlers):
            self.logger.addHandler(trading_handler)

    def log_signal(self, signal: Dict[str, Any]):
        """Log trading signal generation"""
        self.logger.info(
            f"SIGNAL | {signal.get('symbol', 'UNKNOWN')} | "
            f"{signal.get('action', 'UNKNOWN').upper()} | "
            f"Confidence: {signal.get('confidence', 0.0):.2f} | "
            f"Quantity: {signal.get('quantity', 0.0):.6f} | "
            f"Type: {signal.get('signal_type', 'unknown')}"
        )

    def log_trade_execution(self, trade: Dict[str, Any]):
        """Log trade execution"""
        success = trade.get("success", False)
        symbol = trade.get("symbol", "UNKNOWN")
        action = trade.get("side", "UNKNOWN")
        quantity = trade.get("quantity", 0.0)
        price = trade.get("price", 0.0)

        status = "SUCCESS" if success else "FAILED"

        self.logger.info(
            f"TRADE | {status} | {symbol} | {action.upper()} | " f"Qty: {quantity:.6f} | Price: {price:.2f}"
        )

    def log_risk_assessment(self, assessment: Dict[str, Any]):
        """Log risk assessment results"""
        approved_count = len(assessment.get("approved_signals", []))
        rejected_count = len(assessment.get("rejected_signals", []))

        self.logger.info(
            f"RISK | Approved: {approved_count} | Rejected: {rejected_count} | "
            f"Portfolio Risk: {assessment.get('risk_metrics', {}).get('current_drawdown', 0.0):.2%}"
        )

    def log_performance(self, metrics: Dict[str, Any]):
        """Log performance metrics"""
        total_trades = metrics.get("total_trades", 0)
        successful_trades = metrics.get("successful_trades", 0)
        success_rate = (successful_trades / total_trades * 100) if total_trades > 0 else 0

        self.logger.info(
            f"PERFORMANCE | Total Trades: {total_trades} | "
            f"Success Rate: {success_rate:.1f}% | "
            f"Uptime: {metrics.get('uptime', 0.0):.0f}s"
        )


class PerformanceLogger:
    """
    Logger for performance monitoring and profiling
    """

    def __init__(self):
        self.logger = logging.getLogger("qualia.performance")
        self._setup_performance_log()

    def _setup_performance_log(self):
        """Setup performance monitoring log"""
        log_dir = Path(os.getenv("QUALIA_LOG_DIR", "logs"))
        perf_log_path = log_dir / "performance.log"
        perf_log_path.parent.mkdir(parents=True, exist_ok=True)

        # Performance-specific formatter
        perf_formatter = logging.Formatter("%(asctime)s | %(message)s", datefmt="%Y-%m-%d %H:%M:%S.%f")

        perf_handler = logging.handlers.RotatingFileHandler(
            filename=perf_log_path,
            maxBytes=20 * 1024 * 1024,  # 20MB
            backupCount=5,
            encoding="utf-8",
        )
        perf_handler.setFormatter(perf_formatter)

        if not self.logger.handlers:
            self.logger.addHandler(perf_handler)
            # self.logger.setLevel(logging.INFO)

    def log_timing(self, operation: str, duration: float, context: Optional[Dict] = None):
        """Log operation timing"""
        context_str = ""
        if context:
            context_str = " | " + " | ".join([f"{k}={v}" for k, v in context.items()])

        self.logger.info(f"TIMING | {operation} | {duration:.4f}s{context_str}")

    def log_memory_usage(self, operation: str, memory_mb: float):
        """Log memory usage"""
        self.logger.info(f"MEMORY | {operation} | {memory_mb:.1f}MB")

    def log_system_stats(self, stats: Dict[str, Any]):
        """Log system statistics"""
        cpu_usage = stats.get("cpu_percent", 0.0)
        memory_usage = stats.get("memory_percent", 0.0)
        disk_usage = stats.get("disk_percent", 0.0)

        self.logger.info(
            f"SYSTEM | CPU: {cpu_usage:.1f}% | " f"Memory: {memory_usage:.1f}% | " f"Disk: {disk_usage:.1f}%"
        )


def get_logger(name: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> QualiaLogger:
    """Return a wrapped logger for QUALIA.

    The returned :class:`QualiaLogger` exposes the standard ``logging.Logger``
    interface, allowing direct management of handlers and log level.

    Args:
        name: Logger name. If ``None``, defaults to the caller's module name.
        context: Additional context to include in all messages.

    Returns:
        QualiaLogger: Configured logger wrapper.
    """

    if name is None:
        frame = inspect.currentframe()
        if frame is not None and frame.f_back:
            module_name = frame.f_back.f_globals.get("__name__", "__main__")
        else:
            module_name = "__main__"
        name = module_name

    qualia_logger = QualiaLogger(name, context)
    if is_tracing_enabled():
        instrument_logger(qualia_logger.logger)
    return qualia_logger


def get_trading_logger() -> TradingLogger:
    """Get the trading logger instance"""
    return TradingLogger()


def get_performance_logger() -> PerformanceLogger:
    """Get the performance logger instance"""
    return PerformanceLogger()


class LogContext:
    """
    Context manager for adding temporary context to log messages
    """

    def __init__(self, logger: QualiaLogger, context: Dict[str, Any]):
        self.logger = logger
        self.context = context
        self.original_context = logger.extra_context.copy()

    def __enter__(self):
        self.logger.extra_context.update(self.context)
        return self.logger

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.logger.extra_context = self.original_context


def log_with_context(logger: QualiaLogger, context: Dict[str, Any]) -> LogContext:
    """
    Create a context manager for temporary logging context

    Args:
        logger: QualiaLogger instance
        context: Temporary context to add

    Returns:
        LogContext manager
    """
    return LogContext(logger, context)