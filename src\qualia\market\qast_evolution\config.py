"""Configuration utilities for QAST evolution."""

from __future__ import annotations

from dataclasses import asdict, dataclass
from pathlib import Path
from typing import Any

try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()

from ...config import config as settings


@dataclass
class QASTEvolutionConfig:
    """Parameters controlling the QAST evolutionary process."""

    population_size: int = 32
    mutation_rate: float = 0.3
    crossover_rate: float = 0.7
    generations_per_call: int = 5
    backtest_results_dir: str = settings.qast_backtest_results_dir

    @classmethod
    def from_yaml(cls, filepath: str) -> "QASTEvolutionConfig":
        """Create a configuration instance from a YAML file."""
        with open(filepath, "r", encoding="utf-8") as f:
            data: Any = yaml.safe_load(f) or {}
        return cls(**data)

    def to_yaml(self, filepath: str) -> None:
        """Serialize the configuration instance to ``filepath`` as YAML."""
        path = Path(filepath)
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, "w", encoding="utf-8") as f:
            yaml.safe_dump(asdict(self), f)
