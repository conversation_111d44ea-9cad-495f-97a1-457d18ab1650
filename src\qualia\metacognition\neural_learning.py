from __future__ import annotations

from typing import Any, Dict, List, <PERSON><PERSON>

import os
import joblib  # type: ignore

from ..config import config
from ..utils.logger import get_logger

import numpy as np
from sklearn.neural_network import MLPClassifier  # type: ignore

logger = get_logger(__name__)

# Keys used to extract numerical metrics for the neural model
METRIC_KEYS: Tuple[str, ...] = ("similarity", "entropy", "otoc", "coherence")
# Default values for missing metrics
METRIC_DEFAULTS: Tuple[float, ...] = (0.0, 0.5, 0.5, 0.0)


class MetacognitionNeuralLearner:
    """Simple wrapper around :class:`~sklearn.neural_network.MLPClassifier`.

    This learner converts the metrics produced by the metacognitive layer
    into feature vectors and predicts symbolic labels (``"low"``, ``"medium"`` or
    ``"high"``). A small multilayer perceptron is used by default but
    ``hidden_layer_sizes`` can be customized.
    """

    def __init__(
        self,
        hidden_layer_sizes: Tuple[int, ...] = (8, 4),
        random_state: int = 42,
        base_dir: str | None = None,
    ) -> None:
        self.model = MLPClassifier(
            hidden_layer_sizes=hidden_layer_sizes,
            max_iter=500,
            random_state=random_state,
        )
        self.label_to_index = {"low": 0, "medium": 1, "high": 2}
        self.index_to_label = {v: k for k, v in self.label_to_index.items()}
        self.base_dir = base_dir or config.results_dir

    def fit(self, data: List[Dict[str, Any]]) -> None:
        """Train the neural network using evaluation data."""

        inputs: List[List[float]] = []
        targets: List[int] = []
        for entry in data:
            label = entry.get("symbolic_label")
            if label not in self.label_to_index:
                logger.debug("Skipping entry with unknown label: %s", label)
                continue
            inputs.append(
                [entry.get(k, d) for k, d in zip(METRIC_KEYS, METRIC_DEFAULTS)]
            )
            targets.append(self.label_to_index[label])

        if not inputs:
            raise ValueError("Training data must contain at least one valid entry")

        self.model.fit(np.array(inputs), np.array(targets))
        logger.info("Neural learner trained on %d samples", len(inputs))

    def predict_label(self, metrics: Dict[str, Any]) -> str:
        """Predict a symbolic label given metric values."""

        sample = np.array(
            [[metrics.get(k, d) for k, d in zip(METRIC_KEYS, METRIC_DEFAULTS)]]
        )
        index = int(self.model.predict(sample)[0])
        label = self.index_to_label.get(index, "low")
        logger.debug("Predicted label %s for metrics %s", label, metrics)
        return label

    def save(self, path: str | None = None) -> str:
        """Persist the underlying ``MLPClassifier`` to disk."""

        path = path or os.path.join(self.base_dir, "metacognition_model.joblib")
        dir_name = os.path.dirname(path)
        if dir_name:
            os.makedirs(dir_name, exist_ok=True)
        joblib.dump(self.model, path)
        logger.info("Model saved to %s", path)
        return path

    def load(self, path: str | None = None) -> None:
        """Load a saved ``MLPClassifier`` from disk."""

        path = path or os.path.join(self.base_dir, "metacognition_model.joblib")
        self.model = joblib.load(path)
        logger.info("Model loaded from %s", path)
