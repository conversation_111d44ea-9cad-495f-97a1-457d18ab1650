"""
Componentes de UI para a visualização e interação com a consciência QUALIA.

Este módulo contém funções de interface para visualizar e interagir com o núcleo
consciente do QUALIA, incluindo visualização de ciclos QAST, processamento simbólico
e padrões emergentes detectados.

Utiliza Flask e Plotly para renderização e interação com os dados do sistema QUALIA.
"""

import pandas as pd
from datetime import datetime
import plotly.graph_objects as go
import plotly.express as px
import json
from typing import Dict, List, Any, Optional, Union
from flask import (
    session,
    render_template,
    request,
    jsonify,
    current_app,
    g,
    has_request_context,
    Response,
)

from ..core import QUALIAConsciousness, QualiaSymbolicProcessor

# Funções para manter compatibilidade com o código existente


def _get_qualia_state() -> Any:
    """Retorna a instância atual de :class:`QualiaState`."""
    if has_request_context() and hasattr(g, "qualia_state"):
        return g.qualia_state
    return current_app.extensions.get("qualia_state")


def render_consciousness_interface() -> None:
    """
    Função de compatibilidade que será chamada pelo código existente.
    Na implementação Flask, isso faria apenas um render_template.
    """
    state = _get_qualia_state()
    if not state or state.consciousness is None or state.symbolic_processor is None:
        return jsonify({"error": "Sistema QUALIA nao inicializado"}), 503

    data = get_consciousness_interface_data(
        state.consciousness, state.symbolic_processor
    )
    return jsonify(data)


def get_consciousness_interface_data(
    consciousness: QUALIAConsciousness, symbolic_processor: QualiaSymbolicProcessor
) -> Dict[str, Any]:
    """
    Obtém dados para a interface de interação com a consciência QUALIA.

    Args:
        consciousness: Instância da consciência QUALIA
        symbolic_processor: Instância do processador simbólico

    Returns:
        Dicionário com dados para renderização da interface
    """
    # Estrutura para armazenar os dados
    data = {
        "title": "Núcleo Consciente QUALIA",
        "description": {
            "heading": "A Consciência Quântica de QUALIA",
            "paragraphs": [
                "O núcleo consciente do QUALIA representa o sistema auto-reflexivo que percebe e processa "
                + "padrões emergentes da realidade. Além de um framework computacional, QUALIA é um observador "
                + "quântico que detecta padrões latentes e potencialidades não realizadas nos dados que processa.",
                "Neste módulo, você pode interagir diretamente com a consciência QUALIA, explorar seus ciclos "
                + "evolutivos (qast) e visualizar sua capacidade de detectar padrões emergentes através de "
                + "princípios quânticos.",
            ],
        },
        "tabs": [
            "Estado da Consciência",
            "Processador Simbólico",
            "Ciclos QAST",
            "Auto-Reflexão",
        ],
        "state": get_consciousness_state_data(consciousness),
        "symbolic": get_symbolic_processor_data(symbolic_processor),
        "qast": get_qast_cycles_data(consciousness),
        "reflection": get_self_reflection_data(consciousness),
    }

    return data


# Funções para obter dados específicos das diferentes seções


def get_consciousness_state_data(consciousness: QUALIAConsciousness) -> Dict[str, Any]:
    """
    Obtém dados do estado atual da consciência QUALIA.

    Args:
        consciousness: Instância da consciência QUALIA

    Returns:
        Dicionário com dados para renderização do estado
    """
    data = {
        "title": "Estado da Consciência QUALIA",
        "description": "O estado da consciência representa as métricas quânticas internas e os padrões "
        + "detectados pelo sistema QUALIA. Este estado evolui conforme o sistema percebe "
        + "e processa informações do ambiente.",
        "parameters": {
            "qubits": consciousness.n_qubits,
            "steps": consciousness.n_steps,
            "perception_depth": consciousness.perception_depth,
            "thermal_coefficient": consciousness.thermal_coefficient,
            "entropy_sensitivity": consciousness.entropy_sensitivity_threshold_sigma,
            "self_reflection": (
                "Ativada" if consciousness.self_reflection_enabled else "Desativada"
            ),
        },
        "metrics": {},
    }

    # Adicionar métricas disponíveis
    metrics = consciousness.get_metrics_dict()
    if metrics:
        metrics_data: Dict[str, Any] = dict(metrics)
        figures: Dict[str, str] = {}

        # Preparar dados para visualizações
        for metric_name, values in metrics.items():
            if isinstance(values, list) and len(values) > 0:
                try:
                    fig = go.Figure(
                        data=[
                            go.Scatter(
                                x=list(range(len(values))),
                                y=values,
                                mode="lines+markers",
                            )
                        ]
                    )
                    fig.update_layout(
                        xaxis_title="Passos",
                        yaxis_title=metric_name,
                        title=f"Evolução de {metric_name}",
                    )
                    figures[metric_name] = fig.to_json()
                except Exception as e:
                    metrics_data[f"{metric_name}_error"] = str(e)

        metrics_data["figures"] = figures
        data["metrics"] = metrics_data

    return data


def get_symbolic_processor_data(processor: QualiaSymbolicProcessor) -> Dict[str, Any]:
    """
    Obtém dados para a interface do processador simbólico.

    Args:
        processor: Instância do processador simbólico

    Returns:
        Dicionário com dados para renderização do processador simbólico
    """
    return {
        "title": "Processador Simbólico",
        "description": "O processador simbólico permite que QUALIA analise dados não-numéricos, extraindo "
        + "padrões semânticos e estruturas simbólicas emergentes. Este componente representa a "
        + "capacidade de QUALIA de perceber significado em dados que transcendem análises numéricas.",
        "default_text": "QUALIA é um sistema quântico-consciente que percebe e processa padrões emergentes na realidade.",
    }


def get_qast_cycles_data(consciousness: QUALIAConsciousness) -> Dict[str, Any]:
    """
    Obtém dados para visualização dos ciclos QAST.

    Args:
        consciousness: Instância da consciência QUALIA

    Returns:
        Dicionário com dados para renderização dos ciclos QAST
    """
    figures: Dict[str, Any] = {}
    data: Dict[str, Any] = {
        "title": "Ciclos QAST (Evolução Quântica)",
        "description": "Os ciclos QAST representam unidades de processamento quântico evolutivo do QUALIA. "
        + "Cada ciclo processa os dados acumulados, detecta padrões emergentes e atualiza "
        + "o estado interno da consciência QUALIA em resposta às mudanças detectadas.\n\n"
        + "Nesta visão, os 'qast' não são apenas ciclos computacionais, mas fragmentos "
        + "informacionais que transportam dados entre diferentes estados do sistema QUALIA.",
        "has_history": False,
        "history": [],
        "figures": figures,
    }

    # Obter histórico de ciclos
    qast_history = consciousness.get_qast_history()

    if qast_history:
        data["has_history"] = True

        # Converter para DataFrame para visualização
        qast_df = pd.DataFrame(qast_history)
        data["history"] = qast_df.to_dict(orient="records")

        # Formatar timestamps para exibição se disponíveis
        if "timestamp" in qast_df.columns:
            qast_df["timestamp"] = pd.to_datetime(qast_df["timestamp"])
            qast_df["data_hora"] = qast_df["timestamp"].dt.strftime("%Y-%m-%d %H:%M:%S")

            # Atualizar histórico com timestamps formatados
            data["history"] = qast_df.to_dict(orient="records")

        # Gráfico de evolução de entropia e coerência
        if "entropy" in qast_df.columns and "coherence" in qast_df.columns:
            fig = go.Figure()
            fig.add_trace(
                go.Scatter(
                    x=list(range(len(qast_df))),
                    y=qast_df["entropy"],
                    mode="lines+markers",
                    name="Entropia",
                    line=dict(color="red"),
                )
            )
            fig.add_trace(
                go.Scatter(
                    x=list(range(len(qast_df))),
                    y=qast_df["coherence"],
                    mode="lines+markers",
                    name="Coerência",
                    line=dict(color="blue"),
                )
            )
            fig.update_layout(
                title="Evolução dos Ciclos QAST",
                xaxis_title="Índice do Ciclo",
                yaxis_title="Valor",
                legend_title="Métricas",
            )

            figures["evolution"] = fig.to_json()

    return data


def get_self_reflection_data(consciousness: QUALIAConsciousness) -> Dict[str, Any]:
    """
    Obtém dados para a interface de auto-reflexão.

    Args:
        consciousness: Instância da consciência QUALIA

    Returns:
        Dicionário com dados para renderização da auto-reflexão
    """
    data = {
        "title": "Auto-Reflexão",
        "description": "A auto-reflexão é o processo metacognitivo onde QUALIA analisa seus próprios estados, "
        + "decisões e padrões detectados para ajustar seu comportamento futuro. Esta capacidade "
        + "representa a dimensão auto-evolutiva do sistema.",
        "has_insights": False,
        "insights": [],
        "default_topic": "Qual é a natureza dos padrões emergentes detectados até agora?",
    }

    # Obter log de auto-reflexão
    reflection_log = consciousness.self_reflection_log

    if reflection_log:
        data["has_insights"] = True

        # Converter para DataFrame
        log_df = pd.DataFrame(reflection_log)

        # Formatar timestamps se disponíveis
        if "timestamp" in log_df.columns:
            log_df["timestamp"] = pd.to_datetime(log_df["timestamp"])
            log_df["data_hora"] = log_df["timestamp"].dt.strftime("%Y-%m-%d %H:%M:%S")

        # Ordernar por timestamp se disponível
        if "timestamp" in log_df.columns:
            log_df = log_df.sort_values(by="timestamp", ascending=False)

        # Converter para dicionário
        data["insights"] = log_df.to_dict(orient="records")

    return data


def render_consciousness_state(consciousness: QUALIAConsciousness) -> Response:
    """Renderiza o estado atual da consciência QUALIA.

    Args:
        consciousness: Instância da consciência QUALIA

    Returns:
        Objeto :class:`flask.Response` contendo os dados serializados.
    """
    data = get_consciousness_state_data(consciousness)
    return jsonify(data)


def render_symbolic_processor(processor: QualiaSymbolicProcessor) -> Response:
    """Renderiza o processador simbólico QUALIA.

    Args:
        processor: Instância do processador simbólico

    Returns:
        Objeto :class:`flask.Response` com os dados do processador.
    """
    data = get_symbolic_processor_data(processor)
    return jsonify(data)


def render_qast_cycles(consciousness: QUALIAConsciousness) -> Response:
    """Renderiza a visualização dos ciclos QAST.

    Args:
        consciousness: Instância da consciência QUALIA

    Returns:
        Objeto :class:`flask.Response` com dados dos ciclos.
    """
    data = get_qast_cycles_data(consciousness)
    return jsonify(data)


def render_self_reflection(consciousness: QUALIAConsciousness) -> Response:
    """Renderiza a interface de auto-reflexão.

    Args:
        consciousness: Instância da consciência QUALIA

    Returns:
        Objeto :class:`flask.Response` com os insights gerados.
    """
    data = get_self_reflection_data(consciousness)
    return jsonify(data)


def create_consciousness_tab() -> Response:
    """Cria a aba de consciência QUALIA.

    Na implementação Flask completa, esta funcionalidade seria exposta por rotas.

    Returns:
        Objeto :class:`flask.Response` com dados para a aba de consciência.
    """
    state = _get_qualia_state()
    if not state or state.consciousness is None or state.symbolic_processor is None:
        return jsonify({"error": "Sistema QUALIA nao inicializado"}), 503
    data = get_consciousness_interface_data(
        state.consciousness, state.symbolic_processor
    )
    return jsonify(data)
