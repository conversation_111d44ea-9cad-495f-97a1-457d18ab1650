from __future__ import annotations

"""IntentioWaveletEngine - análise de intenções por transformadas wavelet."""

import base64
from typing import Dict, List, Sequence

import numpy as np
import pywt

from ..utils.logger import get_logger


class IntentioWaveletEngine:
    """Motor de análise wavelet para campos de intenção."""

    def __init__(self, wavelet: str = "haar", level: int = 3) -> None:
        """Inicializa o engine.

        Parameters
        ----------
        wavelet
            Nome da wavelet a ser utilizada.
        level
            Nível máximo de decomposição.
        """

        self.wavelet = wavelet
        self.level = int(level)
        self.logger = get_logger(__name__)

    def analyze_field(self, field: np.ndarray) -> np.ndarray:
        """Aplica transformada wavelet discreta em ``field``.

        Parameters
        ----------
        field
            Matriz ou vetor numérico representando o campo de intenções.

        Returns
        -------
        ndarray
            Vetor com a energia relativa de cada nível da decomposição.
        """
        arr = np.asarray(field, dtype=float)
        if arr.ndim > 2:
            arr = arr.reshape(arr.shape[0], -1)

        try:
            if arr.ndim == 1:
                if hasattr(pywt, "dwt_max_level"):
                    max_level = min(
                        self.level,
                        pywt.dwt_max_level(len(arr), self.wavelet),
                    )
                else:
                    max_level = min(self.level, int(np.log2(len(arr))) or 1)
                coeffs = pywt.wavedec(arr, self.wavelet, level=max_level)
                if len(coeffs) == 0:
                    fft_vals = np.fft.fft(arr)
                    energies = np.abs(fft_vals)[: self.level + 1] ** 2
                else:
                    energies = np.array([np.sum(c**2) for c in coeffs])
            else:
                if hasattr(pywt, "dwt_max_level"):
                    max_level = min(
                        self.level,
                        pywt.dwt_max_level(min(arr.shape), self.wavelet),
                    )
                else:
                    max_level = min(self.level, int(np.log2(min(arr.shape))) or 1)

                if hasattr(pywt, "wavedec2"):
                    coeffs = pywt.wavedec2(arr, self.wavelet, level=max_level)
                    if len(coeffs) == 0:
                        fft_vals = np.fft.fft2(arr)
                        energies = np.abs(fft_vals).ravel()[: self.level + 1] ** 2
                    else:
                        energies = [np.sum(coeffs[0] ** 2)]
                        for detail in coeffs[1:]:
                            if isinstance(detail, tuple):
                                energies.append(sum(np.sum(d**2) for d in detail))
                            else:
                                energies.append(np.sum(detail**2))
                        energies = np.array(energies)
                else:
                    flat = arr.ravel()
                    coeffs = pywt.wavedec(flat, self.wavelet, level=max_level)
                    if len(coeffs) == 0:
                        fft_vals = np.fft.fft(flat)
                        energies = np.abs(fft_vals)[: self.level + 1] ** 2
                    else:
                        energies = np.array([np.sum(c**2) for c in coeffs])

            total = float(np.sum(energies))
            if total > 0:
                energies /= total
            return energies
        except Exception as exc:  # pragma: no cover - defensive
            self.logger.error("Erro na análise wavelet: %s", exc)
            return np.array([])

    def detect_hotspots(self, data: np.ndarray) -> List[Dict[str, float]]:
        """Identifica regiões de energia elevada em ``data``.

        Parameters
        ----------
        data
            Série de energias calculadas por :meth:`analyze_field`.

        Returns
        -------
        list of dict
            Cada item contém ``index`` e ``intensity`` do hotspot.
        """
        arr = np.asarray(data, dtype=float).ravel()
        if arr.size == 0:
            return []

        threshold = float(arr.mean() + arr.std())
        hotspots: List[Dict[str, float]] = []
        for idx, value in enumerate(arr):
            if value > threshold:
                hotspots.append({"index": int(idx), "intensity": float(value)})
        return hotspots

    def encode_token(self, pattern: np.ndarray) -> str:
        """Codifica ``pattern`` em uma string base64.

        Parameters
        ----------
        pattern
            Dados numéricos a serem codificados.

        Returns
        -------
        str
            Representação codificada em ASCII.
        """
        arr = np.asarray(pattern, dtype=float).ravel()
        byte_data = arr.tobytes()
        return base64.b64encode(byte_data).decode("ascii")


# ---------------------------------------------------------------------------
# SIM-01 scaffolding (dev-core)
# ---------------------------------------------------------------------------
from .tokenizer import QuantumSymbolicTokenizer

# ----- QuantumSymbolicTokenizer moved to tokenizer.py (SIM-12) -----

# Public interface of this module ------------------------------------------------
# ---------------------------------------------------------------------------
# SIM-02: Volatility/Volume Z-Score Hotspot Detector (async)
# ---------------------------------------------------------------------------
class VolZScoreHotspotDetector:  # noqa: D101 - placeholder implementation
    """Detect hotspots in a numerical *series* using Z-score > ``threshold``.

    The detector is asynchronous to fit into QUALIA's async data pipeline.
    A *snapshot* is assumed to be an ``Iterable[float]`` (e.g. recent
    volatility or volume values).
    """

    def __init__(self, threshold: float = 2.5) -> None:  # noqa: D401 simple
        self.threshold = float(threshold)
        self.logger = get_logger(__name__)

    async def detect(self, snapshot: Sequence[float] | np.ndarray) -> list[dict[str, float]]:  # noqa: D401
        """Return hotspots whose absolute Z-score exceeds ``self.threshold``.

        Parameters
        ----------
        snapshot
            Numerical series (latest values). If empty or degenerate, returns
            an empty list.

        Returns
        -------
        list of dict
            Each dict has ``index`` (int) and ``zscore`` (float).
        """
        arr = np.asarray(snapshot, dtype=float).ravel()
        if arr.size == 0 or np.allclose(arr.std(), 0):
            self.logger.debug("VolZScoreHotspotDetector: série vazia ou variância zero")
            return []

        mean = float(arr.mean())
        std = float(arr.std())
        zscores = (arr - mean) / std

        hotspots: list[dict[str, float]] = []
        for idx, z in enumerate(zscores):
            if abs(z) > self.threshold:
                hotspots.append({"index": int(idx), "zscore": float(z)})
        return hotspots


# Update public interface
__all__: list[str] = [
    "HotspotDetector",
    "VolZScoreHotspotDetector",
    "QuantumSymbolicTokenizer",
]



class HotspotDetector:
    """Detect regions of high intensity in wavelet coefficients."""

    def __init__(self, threshold_sigma: float = 3.0) -> None:
        self.threshold_sigma = float(threshold_sigma)
        self.logger = get_logger(__name__)

    def detect(self, coeffs: np.ndarray) -> List[Dict[str, float]]:
        """Return hotspots exceeding ``threshold_sigma`` standard deviations.

        Parameters
        ----------
        coeffs
            Coefficients produced by :meth:`IntentioWaveletEngine.analyze_field`.

        Returns
        -------
        list of dict
            Each dictionary contains ``index`` and ``intensity``.
        """

        arr = np.asarray(coeffs, dtype=float).ravel()
        if arr.size == 0:
            self.logger.warning("Nenhum coeficiente fornecido para detecção")
            return []

        mean = float(arr.mean())
        std = float(arr.std())
        threshold = mean + self.threshold_sigma * std

        hotspots: List[Dict[str, float]] = []
        for idx, value in enumerate(arr):
            if value > threshold:
                hotspots.append({"index": int(idx), "intensity": float(value)})
        return hotspots


class Base64PatternEncoder:
    """Encode numerical patterns as ASCII tokens."""

    def __init__(self) -> None:
        self.logger = get_logger(__name__)

    def encode(self, pattern: Sequence[float] | np.ndarray) -> str:
        """Return a base64 ASCII token from ``pattern``.

        Parameters
        ----------
        pattern
            Numeric values to be encoded.

        Returns
        -------
        str
            Base64-encoded representation.
        """

        try:
            arr = np.asarray(pattern, dtype=float).ravel()
            token = base64.b64encode(arr.tobytes()).decode("ascii")
            return token
        except Exception as exc:  # pragma: no cover - defensive
            self.logger.error("Falha ao codificar token: %s", exc)
            return ""
