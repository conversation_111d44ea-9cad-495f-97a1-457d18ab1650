"""
Sistema de Gerenciamento Principal do QUALIA
Integra health check com todos os componentes críticos.
"""

import asyncio
import importlib
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone

from ..monitoring.health_check import (
    SystemHealthChecker,
    ReadinessReport,
    SystemReadiness,
    ComponentStatus,
)

logger = logging.getLogger(__name__)


class QUALIASystemManager:
    """
    Gerenciador principal do sistema QUALIA com health check integrado.
    Coordena inicialização, monitoramento e shutdown de todos os componentes.
    """

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.components: Dict[str, Any] = {}
        self.health_checker = SystemHealthChecker()
        self.is_running = False
        self.startup_complete = False
        self.shutdown_requested = False

        # Configurações de health check
        self.health_check_interval = config.get("health_check_interval", 60)
        self.startup_timeout = config.get("startup_timeout", 300)  # 5 minutos
        self.require_all_healthy = config.get("require_all_healthy_for_trading", False)

        # Callbacks para eventos de saúde
        self.health_callbacks: List[callable] = []

    async def initialize_system(self) -> bool:
        """
        Inicializa todos os componentes do sistema QUALIA.
        Retorna True se a inicialização foi bem-sucedida.
        """
        logger.info("🚀 Iniciando sistema QUALIA...")

        try:
            # 1. Inicializar componentes em ordem de dependência
            await self._initialize_data_collector()
            await self._initialize_quantum_layer()
            await self._initialize_holographic_universe()
            await self._initialize_strategies()
            await self._initialize_risk_manager()
            await self._initialize_additional_components()

            # 2. Registrar componentes no health checker
            self._register_components_for_health_check()

            # 3. Verificar saúde inicial do sistema
            initial_report = await self.health_checker.get_readiness_report()

            # 4. Avaliar se o sistema está pronto
            if self._evaluate_startup_readiness(initial_report):
                self.startup_complete = True
                self.is_running = True
                logger.info("✅ Sistema QUALIA inicializado com sucesso!")

                # Iniciar monitoramento contínuo
                asyncio.create_task(self._continuous_health_monitoring())

                return True
            else:
                logger.error(
                    "❌ Sistema QUALIA não passou na verificação inicial de saúde"
                )
                await self._handle_startup_failure(initial_report)
                return False

        except Exception as e:
            logger.error(f"❌ Erro durante inicialização do sistema: {e}")
            await self._cleanup_partial_initialization()
            return False

    async def _initialize_data_collector(self):
        """Inicializa o coletor de dados."""
        logger.info("📊 Inicializando Data Collector...")

        try:
            # Importar e criar data collector
            from ..consciousness.real_data_collectors import RealDataCollector
            from ..consciousness.enhanced_data_collector import EnhancedDataCollector

            # Configurar coletores
            real_collector = RealDataCollector(
                config=self.config.get("data_collection", {})
            )

            enhanced_collector = EnhancedDataCollector(
                config=self.config.get("enhanced_data", {})
            )

            # Inicializar coletores
            await real_collector.initialize()
            await enhanced_collector.initialize()

            self.components["real_data_collector"] = real_collector
            self.components["enhanced_data_collector"] = enhanced_collector
            self.components["data_collector"] = real_collector  # Alias principal

            logger.info("✅ Data Collector inicializado")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Data Collector: {e}")
            raise

    async def _initialize_quantum_layer(self):
        """Inicializa a camada quântica."""
        logger.info("⚛️ Inicializando Quantum Layer...")

        try:
            from ..metacognition.metacognition_trading import QUALIAMetacognitionTrading
            from ..memory import get_qpm_instance

            # Criar QPM
            qpm_config = self.config.get("quantum_pattern_memory", {})
            qpm = get_qpm_instance(qpm_config)

            # Criar metacognição com quantum layer
            metacog_config = self.config.get("metacognition", {})
            metacognition = QUALIAMetacognitionTrading(
                adaptive_consciousness_evolution=None,  # Será definido depois
                qpm_memory=qpm,
                config=metacog_config,
            )

            self.components["quantum_pattern_memory"] = qpm
            self.components["metacognition"] = metacognition
            self.components["quantum_layer"] = metacognition.meta_layer

            logger.info("✅ Quantum Layer inicializada")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Quantum Layer: {e}")
            raise

    async def _initialize_holographic_universe(self):
        """Inicializa o universo holográfico."""
        logger.info("🌌 Inicializando Holographic Universe...")

        try:
            from ..consciousness.holographic_universe import HolographicMarketUniverse

            holographic_config = self.config.get("holographic_universe", {})
            holographic_universe = HolographicMarketUniverse(config=holographic_config)

            await holographic_universe.initialize()

            self.components["holographic_universe"] = holographic_universe

            logger.info("✅ Holographic Universe inicializado")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Holographic Universe: {e}")
            raise

    async def _initialize_strategies(self):
        """Inicializa as estratégias de trading."""
        logger.info("📈 Inicializando Trading Strategies...")

        try:
            from ..strategies.nova_estrategia_qualia.core import QualiaTSVFStrategy

            strategies = {}
            strategy_configs = self.config.get("strategies", {})

            for symbol, strategy_config in strategy_configs.items():
                strategy = QualiaTSVFStrategy(
                    symbol=symbol,
                    timeframe=strategy_config.get("timeframe", "5m"),
                    params=strategy_config.get("params", {}),
                    config_manager=None,  # Será definido se necessário
                )

                # Setup inicial da estratégia
                # await strategy.setup(initial_data)  # Será feito quando houver dados

                strategies[symbol] = strategy

            self.components["strategies"] = strategies

            logger.info(f"✅ {len(strategies)} Trading Strategies inicializadas")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Trading Strategies: {e}")
            raise

    async def _initialize_risk_manager(self):
        """Inicializa o gerenciador de risco."""
        logger.info("🛡️ Inicializando Risk Manager...")

        try:
            from ..risk_management.advanced_risk_manager import AdvancedRiskManager

            risk_config = self.config.get("risk_management", {})
            risk_manager = AdvancedRiskManager(
                config=risk_config,
                initial_capital=self.config.get("initial_capital", 10000.0),
            )

            await risk_manager.initialize()

            self.components["risk_manager"] = risk_manager

            logger.info("✅ Risk Manager inicializado")

        except Exception as e:
            logger.error(f"❌ Erro ao inicializar Risk Manager: {e}")
            raise

    async def _initialize_additional_components(self):
        """Inicializa componentes adicionais configurados."""
        logger.info("🔧 Inicializando componentes adicionais...")

        additional_configs = self.config.get("additional_components", {})

        for name, component_config in additional_configs.items():
            try:
                # Implementar factory pattern para componentes customizados
                component = await self._create_component_from_config(
                    name, component_config
                )
                if component:
                    self.components[name] = component
                    logger.info(f"✅ Componente adicional '{name}' inicializado")

            except Exception as e:
                logger.warning(f"⚠️ Falha ao inicializar componente '{name}': {e}")

    async def _create_component_from_config(
        self, name: str, config: Dict[str, Any]
    ) -> Optional[Any]:
        """Cria componente a partir de configuração.

        Parameters
        ----------
        name
            Nome do componente a ser criado. Utilizado apenas para mensagens de erro
            e logs.
        config
            Dicionário de configuração para o componente. Para tipos ``custom`` é
            esperado ter as chaves ``module`` e ``class`` com o caminho do módulo
            e o nome da classe a ser importada. Parâmetros adicionais para o
            construtor devem ser fornecidos em ``params``.

        Returns
        -------
        Optional[Any]
            Instância do componente criado ou ``None`` se o tipo não é
            reconhecido.

        Raises
        ------
        ImportError
            Se o módulo ou a classe não puderem ser importados.
        RuntimeError
            Se ocorrer erro ao instanciar o componente.
        """

        component_type = config.get("type")
        if component_type != "custom":
            return None

        module_path = config.get("module")
        class_name = config.get("class")
        params: Dict[str, Any] = config.get("params", {})

        if not module_path or not class_name:
            raise ValueError(
                f"Componente custom '{name}' requer 'module' e 'class' no config"
            )

        try:
            module = importlib.import_module(module_path)
        except Exception as exc:  # noqa: BLE001
            raise ImportError(
                f"Falha ao importar módulo '{module_path}' para componente '{name}': {exc}"
            ) from exc

        try:
            component_cls = getattr(module, class_name)
        except AttributeError as exc:
            raise ImportError(
                f"Classe '{class_name}' não encontrada em '{module_path}'"
            ) from exc

        try:
            return component_cls(**params)
        except Exception as exc:  # noqa: BLE001
            raise RuntimeError(
                f"Erro ao instanciar componente '{name}' ({class_name}): {exc}"
            ) from exc

    def _register_components_for_health_check(self):
        """Registra todos os componentes no health checker."""
        logger.info("🏥 Registrando componentes para health check...")

        for name, component in self.components.items():
            self.health_checker.register_component(name, component)

        logger.info(f"✅ {len(self.components)} componentes registrados")

    def _evaluate_startup_readiness(self, report: ReadinessReport) -> bool:
        """Avalia se o sistema está pronto para operar após startup."""

        # Verificar se há erros críticos
        if report.overall_status == SystemReadiness.CRITICAL_ERROR:
            logger.error("❌ Sistema com erros críticos - não pode iniciar")
            return False

        # Verificar componentes obrigatórios
        required_components = ["data_collector", "strategies", "risk_manager"]

        for comp_name in required_components:
            comp_info = report.components.get(comp_name)
            if not comp_info or comp_info.status in [
                ComponentStatus.ERROR,
                ComponentStatus.NOT_INITIALIZED,
            ]:
                logger.error(
                    f"❌ Componente obrigatório '{comp_name}' não está operacional"
                )
                return False

        # Verificar score mínimo
        min_score = 0.6 if self.require_all_healthy else 0.4
        if report.readiness_score < min_score:
            logger.error(
                f"❌ Score de readiness muito baixo: {report.readiness_score:.2%} < {min_score:.2%}"
            )
            return False

        logger.info(f"✅ Sistema pronto - Score: {report.readiness_score:.2%}")
        return True

    async def _handle_startup_failure(self, report: ReadinessReport):
        """Trata falhas durante o startup."""
        logger.error("❌ FALHA NO STARTUP DO SISTEMA")

        # Imprimir relatório detalhado
        self.health_checker.print_health_report(report)

        # Tentar correções automáticas básicas
        await self._attempt_auto_corrections(report)

        # Cleanup
        await self._cleanup_partial_initialization()

    async def _attempt_auto_corrections(self, report: ReadinessReport):
        """Tenta correções automáticas para problemas comuns."""
        logger.info("🔧 Tentando correções automáticas...")

        for name, info in report.components.items():
            if info.status == ComponentStatus.NOT_INITIALIZED:
                try:
                    # Tentar reinicializar componente
                    await self._reinitialize_component(name)
                except Exception as e:
                    logger.warning(f"⚠️ Falha ao reinicializar {name}: {e}")

    async def _reinitialize_component(self, component_name: str):
        """Tenta reinicializar um componente específico."""
        logger.info(f"🔄 Reinicializando componente: {component_name}")

        # Implementar lógica específica de reinicialização
        component = self.components.get(component_name)

        if component and hasattr(component, "initialize"):
            await component.initialize()
            logger.info(f"✅ Componente {component_name} reinicializado")

    async def _cleanup_partial_initialization(self):
        """Limpa recursos de inicialização parcial."""
        logger.info("🧹 Limpando recursos de inicialização parcial...")

        for name, component in self.components.items():
            try:
                if hasattr(component, "cleanup"):
                    await component.cleanup()
                elif hasattr(component, "shutdown"):
                    await component.shutdown()
            except Exception as e:
                logger.warning(f"⚠️ Erro ao limpar componente {name}: {e}")

        self.components.clear()

    async def _continuous_health_monitoring(self):
        """Executa monitoramento contínuo de saúde."""
        logger.info(
            f"🔄 Iniciando monitoramento contínuo (intervalo: {self.health_check_interval}s)"
        )

        while self.is_running and not self.shutdown_requested:
            try:
                report = await self.health_checker.get_readiness_report()

                # Processar relatório
                await self._process_health_report(report)

                # Chamar callbacks registrados
                for callback in self.health_callbacks:
                    try:
                        await callback(report)
                    except Exception as e:
                        logger.warning(f"⚠️ Erro em callback de health: {e}")

                await asyncio.sleep(self.health_check_interval)

            except Exception as e:
                logger.error(f"❌ Erro no monitoramento de saúde: {e}")
                await asyncio.sleep(self.health_check_interval)

    async def _process_health_report(self, report: ReadinessReport):
        """Processa relatório de saúde e toma ações necessárias."""

        # Log de mudanças significativas
        if hasattr(self, "_last_health_status"):
            if self._last_health_status != report.overall_status:
                logger.info(
                    f"🔄 Status de saúde mudou: {self._last_health_status.value} → {report.overall_status.value}"
                )

        self._last_health_status = report.overall_status

        # Ações baseadas no status
        if report.overall_status == SystemReadiness.CRITICAL_ERROR:
            logger.error("🚨 ERRO CRÍTICO DETECTADO!")
            if self.config.get("auto_shutdown_on_critical", False):
                logger.warning("⚠️ Iniciando shutdown automático devido a erro crítico")
                await self.shutdown_system()

        elif len(report.critical_issues) > 0:
            logger.warning(
                f"⚠️ {len(report.critical_issues)} problemas críticos detectados"
            )

            # Tentar correções automáticas
            if self.config.get("auto_corrections_enabled", True):
                await self._attempt_auto_corrections(report)

    def register_health_callback(self, callback: callable):
        """Registra callback para eventos de saúde."""
        self.health_callbacks.append(callback)
        logger.info("✅ Callback de saúde registrado")

    async def get_current_health_report(self) -> ReadinessReport:
        """Retorna relatório atual de saúde."""
        return await self.health_checker.get_readiness_report()

    def get_health_summary(self) -> Dict[str, Any]:
        """Retorna resumo rápido da saúde do sistema."""
        summary = self.health_checker.get_health_summary()
        summary.update(
            {
                "system_running": self.is_running,
                "startup_complete": self.startup_complete,
                "shutdown_requested": self.shutdown_requested,
            }
        )
        return summary

    async def shutdown_system(self):
        """Executa shutdown gracioso do sistema."""
        logger.info("🛑 Iniciando shutdown do sistema QUALIA...")

        self.shutdown_requested = True
        self.is_running = False

        # Parar componentes em ordem reversa
        shutdown_order = [
            "strategies",
            "risk_manager",
            "holographic_universe",
            "quantum_layer",
            "data_collector",
        ]

        for component_name in shutdown_order:
            component = self.components.get(component_name)
            if component:
                try:
                    if hasattr(component, "shutdown"):
                        await component.shutdown()
                    elif hasattr(component, "stop"):
                        await component.stop()

                    logger.info(f"✅ Componente {component_name} parado")

                except Exception as e:
                    logger.warning(f"⚠️ Erro ao parar {component_name}: {e}")

        logger.info("✅ Sistema QUALIA parado com sucesso")

    def is_ready_for_trading(self) -> bool:
        """Verifica se o sistema está pronto para trading."""
        if not self.startup_complete or not self.is_running:
            return False

        # Verificação rápida baseada no último relatório
        if hasattr(self, "_last_health_status"):
            return self._last_health_status in [
                SystemReadiness.READY,
                SystemReadiness.DEGRADED_READY,
            ]

        return False
