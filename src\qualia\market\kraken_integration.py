"""Integração simplificada com a Kraken."""

from __future__ import annotations

import os
from typing import Any, Optional

import numpy as np
import pandas as pd

import ccxt.async_support as ccxt_async
from ..config import load_market_defaults
from ..config.settings import get_env
from ..utils.logger import get_logger
from ..utils.validation import (
    validate_non_negative_int,
    validate_positive_float,
)
from .base_integration import CryptoDataFet<PERSON>


def _validate_non_empty_str(value: Any, name: str) -> str:
    """Return ``value`` as ``str`` ensuring it is not empty."""

    if not isinstance(value, str):
        raise EnvironmentError(f"{name} must be a non-empty string")
    if not value.strip():
        raise EnvironmentError(f"{name} must be a non-empty string")
    return value.strip()


logger = get_logger(__name__)


def _market_defaults() -> dict:
    return load_market_defaults()


def _load_exchange_credentials(
    *,
    prefix: str,
    api_key: Optional[str] = None,
    api_secret: Optional[str] = None,
    password: Optional[str] = None,
    secret_env: Optional[str] = None,
    secret_fallback_env: Optional[str] = None,
    password_env: Optional[str] = None,
    ticker_timeout: Optional[float] = None,
    ticker_timeout_env: str = "TICKER_TIMEOUT",
    default_ticker_timeout: float = 30.0,
    ticker_retries: Optional[int] = None,
    ticker_retries_env: str = "TICKER_RETRY_ATTEMPTS",
    default_ticker_retries: int = 3,
    ticker_backoff_base: Optional[float] = None,
    ticker_backoff_env: str = "TICKER_BACKOFF_BASE",
    default_backoff_base: float = 1.0,
    conn_timeout: Optional[float] = None,
    conn_timeout_env: str = "EXCHANGE_CONN_TIMEOUT",
    default_conn_timeout: float = 30.0,
    conn_retries: Optional[int] = None,
    conn_retries_env: str = "EXCHANGE_CONN_RETRIES",
    default_conn_retries: int = 3,
) -> dict[str, Any]:
    """Carrega credenciais e parâmetros de conexão de variáveis de ambiente."""

    if api_key is None:
        api_key = get_env(f"{prefix}_API_KEY")
    api_key = _validate_non_empty_str(api_key, f"{prefix}_API_KEY")

    if api_secret is None:
        secret_name = secret_env or f"{prefix}_API_SECRET"
        try:
            api_secret = get_env(secret_name)
        except EnvironmentError:
            if secret_fallback_env is not None:
                api_secret = get_env(secret_fallback_env)
            else:  # pragma: no cover - validated via EnvironmentError
                raise
    api_secret = _validate_non_empty_str(
        api_secret, secret_env or f"{prefix}_API_SECRET"
    )

    if password is None and password_env is not None:
        password = get_env(password_env)
    if password is not None:
        env_name = password_env or f"{prefix}_PASSWORD"
        password = _validate_non_empty_str(password, env_name)

    if conn_timeout is None:
        raw_conn_timeout = get_env(
            conn_timeout_env, str(default_conn_timeout), warn=False
        )
        try:
            conn_timeout = validate_positive_float(raw_conn_timeout, conn_timeout_env)
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc
    else:
        try:
            conn_timeout = validate_positive_float(conn_timeout, conn_timeout_env)
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc

    if conn_retries is None:
        raw_conn_retries = get_env(
            conn_retries_env, str(default_conn_retries), warn=False
        )
        try:
            conn_retries = validate_non_negative_int(raw_conn_retries, conn_retries_env)
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc
    else:
        try:
            conn_retries = validate_non_negative_int(conn_retries, conn_retries_env)
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc

    if ticker_timeout is None:
        raw_ticker_timeout = get_env(
            ticker_timeout_env, str(default_ticker_timeout), warn=False
        )
        try:
            ticker_timeout = validate_positive_float(
                raw_ticker_timeout, ticker_timeout_env
            )
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc
    else:
        try:
            ticker_timeout = validate_positive_float(ticker_timeout, ticker_timeout_env)
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc

    if ticker_retries is None:
        raw_ticker_retries = get_env(
            ticker_retries_env, str(default_ticker_retries), warn=False
        )
        try:
            ticker_retries = validate_non_negative_int(
                raw_ticker_retries, ticker_retries_env
            )
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc
    else:
        try:
            ticker_retries = validate_non_negative_int(
                ticker_retries, ticker_retries_env
            )
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc

    if ticker_backoff_base is None:
        raw_backoff_base = get_env(
            ticker_backoff_env, str(default_backoff_base), warn=False
        )
        try:
            ticker_backoff_base = validate_positive_float(
                raw_backoff_base, ticker_backoff_env
            )
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc
    else:
        try:
            ticker_backoff_base = validate_positive_float(
                ticker_backoff_base, ticker_backoff_env
            )
        except ValueError as exc:
            raise EnvironmentError(str(exc)) from exc

    return {
        "api_key": api_key,
        "api_secret": api_secret,
        "password": password,
        "conn_timeout": conn_timeout,
        "conn_retries": conn_retries,
        "ticker_timeout": ticker_timeout,
        "ticker_retries": ticker_retries,
        "ticker_backoff_base": ticker_backoff_base,
    }


# Alias para manter compatibilidade com o código existente
class KrakenIntegration(CryptoDataFetcher):
    """Wrapper fino em torno de :class:`CryptoDataFetcher`."""

    def __init__(
        self,
        api_key: Optional[str] = None,
        api_secret: Optional[str] = None,
        rate_limit: Optional[float] = None,
        conn_timeout: Optional[float] = None,
        conn_retries: Optional[int] = None,
        ticker_timeout: Optional[float] = None,
        ticker_retries: Optional[int] = None,
        ticker_backoff_base: Optional[float] = None,
        use_websocket: bool = False,
        fail_threshold: Optional[int] = None,
        recovery_timeout: Optional[float] = None,
    ) -> None:
        """Inicializa a integração com a Kraken."""

        creds = _load_exchange_credentials(
            prefix="KRAKEN",
            api_key=api_key,
            api_secret=api_secret,
            secret_env="KRAKEN_API_SECRET",
            secret_fallback_env="KRAKEN_SECRET_KEY",
            ticker_timeout=ticker_timeout,
            ticker_timeout_env="TICKER_TIMEOUT",
            default_ticker_timeout=_market_defaults()
            .get("timeouts", {})
            .get("ticker", 30.0),
            ticker_retries=ticker_retries,
            ticker_backoff_base=ticker_backoff_base,
            conn_timeout=conn_timeout
            or _market_defaults().get("timeouts", {}).get("connection", 30.0),
            conn_retries=conn_retries
            or _market_defaults().get("retries", {}).get("conn", 3),
        )

        if rate_limit is None:
            try:
                rate_limit = float(
                    get_env(
                        "RATE_LIMIT",
                        str(
                            _market_defaults().get("rate_limits", {}).get("global", 5.0)
                        ),
                        warn=False,
                    )
                )
            except ValueError:
                rate_limit = (
                    _market_defaults().get("rate_limits", {}).get("global", 5.0)
                )

        if fail_threshold is None:
            try:
                fail_threshold = int(os.getenv("API_FAIL_THRESHOLD", "5"))
            except ValueError:
                fail_threshold = 5
        if recovery_timeout is None:
            try:
                recovery_timeout = float(os.getenv("API_RECOVERY_TIMEOUT", "60"))
            except ValueError:
                recovery_timeout = 60.0

        super().__init__(
            api_key=creds["api_key"],
            api_secret=creds["api_secret"],
            exchange_id="kraken",
            rate_limit=rate_limit,
            conn_timeout=creds["conn_timeout"],
            conn_retries=creds["conn_retries"],
            ticker_timeout=creds["ticker_timeout"],
            ticker_retries=creds["ticker_retries"],
            ticker_backoff_base=creds.get("ticker_backoff_base", ticker_backoff_base),
            use_websocket=use_websocket,
            fail_threshold=fail_threshold,
            recovery_timeout=recovery_timeout,
        )


def returns(series: pd.Series, periods: int = 1) -> pd.Series:
    """
    Calcula retornos percentuais para uma série temporal.

    Args:
        series: Série de preços
        periods: Número de períodos para o cálculo

    Returns:
        Série de retornos
    """
    return series.pct_change(periods=periods).fillna(0)


def rolling_vol(series: pd.Series, window: int = 24) -> pd.Series:
    """
    Calcula volatilidade móvel para uma série temporal.

    Args:
        series: Série de preços ou retornos
        window: Tamanho da janela

    Returns:
        Série de volatilidade móvel
    """
    # Se for série de preços, converte para retornos
    if series.mean() > 0.1:  # Heurística para identificar preços vs retornos
        r = returns(series)
    else:
        r = series.copy()

    return r.rolling(window=window).std() * np.sqrt(365 * 24 if window == 24 else 365)
