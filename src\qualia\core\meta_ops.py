import os
from typing import Any

import numpy as np


def _validate_array(field: Any) -> np.ndarray:
    """Return ``field`` as array or raise ``TypeError``."""
    if not isinstance(field, np.ndarray):
        raise TypeError("field must be a numpy array")
    return field.astype(float, copy=False)


def apply_collapse(field: np.ndarray, p: float | None = 0.5) -> np.ndarray:
    """Apply a simple collapse scaling.

    Parameters
    ----------
    field
        Input field array.
    p
        Collapse probability in ``[0, 1]``. ``None`` uses environment variable
        ``QUALIA_COLLAPSE_P``.
    """
    arr = _validate_array(field)

    if p is None:
        p = float(os.getenv("QUALIA_COLLAPSE_P", "0.5"))
    elif not isinstance(p, (int, float)):
        raise TypeError("p must be a float")
    elif not 0.0 <= float(p) <= 1.0:
        raise ValueError("p must be between 0 and 1")

    return (1.0 - float(p)) * arr


def apply_decoherence(field: np.ndarray, rate: float | None = 0.1) -> np.ndarray:
    """Apply exponential decoherence.

    Parameters
    ----------
    field
        Input field array.
    rate
        Decoherence rate. ``None`` uses ``QUALIA_DECOHERENCE_RATE``.
    """
    arr = _validate_array(field)

    if rate is None:
        rate = float(os.getenv("QUALIA_DECOHERENCE_RATE", "0.1"))
    elif not isinstance(rate, (int, float)):
        raise TypeError("rate must be a float")
    elif rate < 0:
        raise ValueError("rate must be non-negative")

    return np.exp(-float(rate)) * arr


def apply_transcendence(field: np.ndarray, delta: float | None = 1.0) -> np.ndarray:
    """Offset field by ``delta``.

    Parameters
    ----------
    field
        Input field array.
    delta
        Amount to add. ``None`` uses ``QUALIA_TRANSCENDENCE_DELTA``.
    """
    arr = _validate_array(field)

    if delta is None:
        delta = float(os.getenv("QUALIA_TRANSCENDENCE_DELTA", "1.0"))
    elif not isinstance(delta, (int, float)):
        raise TypeError("delta must be a float")

    return arr + float(delta)


def apply_retardo(field: np.ndarray, lag: int | None = 1) -> np.ndarray:
    """Shift ``field`` forward by ``lag`` positions.

    Parameters
    ----------
    field
        Input field array.
    lag
        Number of positions to shift. ``None`` uses ``QUALIA_RETARDO_LAG``.
    """
    arr = _validate_array(field)

    if lag is None:
        lag = int(os.getenv("QUALIA_RETARDO_LAG", "1"))
    elif not isinstance(lag, int):
        raise TypeError("lag must be an int")
    elif lag < 0:
        raise ValueError("lag must be non-negative")

    return np.roll(arr, int(lag))


def apply_acceleration(field: np.ndarray, factor: float | None = 1.0) -> np.ndarray:
    """Multiply field by ``1 + factor``.

    Parameters
    ----------
    field
        Input field array.
    factor
        Acceleration factor. ``None`` uses ``QUALIA_ACCELERATION_FACTOR``.
    """
    arr = _validate_array(field)

    if factor is None:
        factor = float(os.getenv("QUALIA_ACCELERATION_FACTOR", "1.0"))
    elif not isinstance(factor, (int, float)):
        raise TypeError("factor must be a float")
    elif factor < 0:
        raise ValueError("factor must be non-negative")

    return (1.0 + float(factor)) * arr


def apply_anacronism(field: np.ndarray, steps: int | None = 1) -> np.ndarray:
    """Shift ``field`` backward by ``steps`` positions.

    Parameters
    ----------
    field
        Input field array.
    steps
        Number of positions to shift backwards. ``None`` uses
        ``QUALIA_ANACRONISM_STEPS``.
    """
    arr = _validate_array(field)

    if steps is None:
        steps = int(os.getenv("QUALIA_ANACRONISM_STEPS", "1"))
    elif not isinstance(steps, int):
        raise TypeError("steps must be an int")
    elif steps < 0:
        raise ValueError("steps must be non-negative")

    return np.roll(arr, -int(steps))
