"""Market sentiment analysis utilities."""

from __future__ import annotations

import logging
from typing import Dict, List

import numpy as np


class SentimentSignalAnalyzer:
    """Analyzer for market sentiment."""

    def __init__(self) -> None:
        self.logger = logging.getLogger(__name__)

    async def analyze(self, market_data: Dict, temporal_results: Dict) -> List[Dict]:
        """Analyze market sentiment and temporal patterns."""
        signals = []

        try:
            # Analyze temporal patterns
            if "patterns" in temporal_results:
                pattern_signals = self._analyze_temporal_patterns(
                    temporal_results["patterns"]
                )
                signals.extend(pattern_signals)

            # Analyze market momentum
            momentum_signals = self._analyze_momentum(market_data)
            signals.extend(momentum_signals)

            # Analyze volatility sentiment
            volatility_signals = self._analyze_volatility_sentiment(market_data)
            signals.extend(volatility_signals)

            return signals

        except Exception as e:
            self.logger.error("Error analyzing sentiment data: %s", e)
            return []

    def _analyze_temporal_patterns(self, patterns: List[Dict]) -> List[Dict]:
        """Analyze temporal patterns for sentiment signals."""
        signals = []

        try:
            for pattern in patterns:
                pattern_type = pattern.get("type", "")
                confidence = pattern.get("confidence", 0.0)

                if confidence > 0.6:
                    if pattern_type == "trend" and pattern.get("subtype") == "bullish":
                        signals.append(
                            {
                                "action": "buy",
                                "confidence": confidence * 0.8,
                                "strength": pattern.get("strength", 0.5),
                                "factors": ["temporal_trend"],
                                "metadata": pattern,
                            }
                        )

                    elif (
                        pattern_type == "trend" and pattern.get("subtype") == "bearish"
                    ):
                        signals.append(
                            {
                                "action": "sell",
                                "confidence": confidence * 0.8,
                                "strength": pattern.get("strength", 0.5),
                                "factors": ["temporal_trend"],
                                "metadata": pattern,
                            }
                        )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing temporal patterns: %s", e)
            return []

    def _analyze_momentum(self, market_data: Dict) -> List[Dict]:
        """Analyze price momentum for sentiment."""
        signals = []

        try:
            prices = market_data.get("prices", [])
            if len(prices) < 10:
                return signals

            # Calculate momentum
            short_momentum = (
                (prices[-1] - prices[-5]) / prices[-5] if len(prices) >= 5 else 0
            )
            long_momentum = (
                (prices[-1] - prices[-10]) / prices[-10] if len(prices) >= 10 else 0
            )

            # Strong positive momentum
            if short_momentum > 0.02 and long_momentum > 0.05:
                signals.append(
                    {
                        "action": "buy",
                        "confidence": 0.6,
                        "strength": min(1.0, (short_momentum + long_momentum) * 10),
                        "factors": ["positive_momentum"],
                        "metadata": {
                            "short_momentum": short_momentum,
                            "long_momentum": long_momentum,
                        },
                    }
                )

            # Strong negative momentum
            elif short_momentum < -0.02 and long_momentum < -0.05:
                signals.append(
                    {
                        "action": "sell",
                        "confidence": 0.6,
                        "strength": min(1.0, abs(short_momentum + long_momentum) * 10),
                        "factors": ["negative_momentum"],
                        "metadata": {
                            "short_momentum": short_momentum,
                            "long_momentum": long_momentum,
                        },
                    }
                )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing momentum: %s", e)
            return []

    def _analyze_volatility_sentiment(self, market_data: Dict) -> List[Dict]:
        """Analyze volatility for sentiment signals."""
        signals = []

        try:
            prices = market_data.get("prices", [])
            if len(prices) < 20:
                return signals

            # Calculate recent vs historical volatility
            recent_returns = np.diff(prices[-10:]) / prices[-11:-1]
            historical_returns = np.diff(prices[-20:-10]) / prices[-21:-11]

            recent_vol = np.std(recent_returns)
            historical_vol = np.std(historical_returns)

            if historical_vol > 0:
                vol_ratio = recent_vol / historical_vol

                # Decreasing volatility after high volatility (consolidation)
                if vol_ratio < 0.7 and historical_vol > 0.02:
                    signals.append(
                        {
                            "action": "buy",  # Consolidation often precedes breakout
                            "confidence": 0.5,
                            "strength": 1.0 - vol_ratio,
                            "factors": ["volatility_consolidation"],
                            "metadata": {
                                "vol_ratio": vol_ratio,
                                "recent_vol": recent_vol,
                                "historical_vol": historical_vol,
                            },
                        }
                    )

            return signals

        except Exception as e:
            self.logger.error("Error analyzing volatility sentiment: %s", e)
            return []
