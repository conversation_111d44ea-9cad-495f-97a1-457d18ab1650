#!/usr/bin/env python3
"""
Teste simples para verificar se a correção do método generate_trading_signal funcionou
"""

import sys
import os
from pathlib import Path

# Adicionar o diretório src ao path para imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_method_exists():
    """Testa se o método correto existe na classe"""
    try:
        from qualia.binance_system import QualiaBinanceCorrectedSystem
        print("✓ Importação bem-sucedida!")
        
        # Criar instância
        system = QualiaBinanceCorrectedSystem()
        print("✓ Instância criada!")
        
        # Verificar métodos
        if hasattr(system, 'validate_trading_signal'):
            print("✓ Método validate_trading_signal encontrado!")
        else:
            print("✗ Método validate_trading_signal NÃO encontrado!")
            return False
            
        if hasattr(system, 'test_buy_only_mode'):
            print("✓ Método test_buy_only_mode encontrado!")
        else:
            print("✗ Método test_buy_only_mode NÃO encontrado!")
            return False
            
        if hasattr(system, 'generate_trading_signal'):
            print("✗ Método generate_trading_signal encontrado (não deveria existir)!")
            return False
        else:
            print("✓ Método generate_trading_signal não existe (correto)!")
            
        print("✓ Teste de importação concluído com sucesso!")
        return True
        
    except Exception as e:
        print(f"✗ Erro: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_validate_trading_signal():
    """Testa se o método validate_trading_signal funciona corretamente"""
    try:
        from qualia.binance_system import QualiaBinanceCorrectedSystem
        from datetime import datetime
        
        system = QualiaBinanceCorrectedSystem()
        
        # Dados de teste
        mock_market_data = {
            'symbol': 'TEST/USDT',
            'price': 100.0,
            'volume': 1000000,
            'timestamp': datetime.now()
        }
        
        # Teste com momentum negativo (não deve gerar sinal)
        mock_quantum_metrics_negative = {
            'momentum': -0.001,  # Momentum negativo
            'consciousness': 0.7,
            'coherence': 0.6,
            'confidence': 0.65,
            'volume_surge': 1.5
        }
        
        signal1 = system.validate_trading_signal(mock_market_data, mock_quantum_metrics_negative)
        if signal1 is None:
            print("✓ Momentum negativo não gerou sinal (correto)")
        else:
            print("✗ Momentum negativo gerou sinal (incorreto)")
            return False
        
        # Teste com momentum positivo (deve gerar sinal)
        mock_quantum_metrics_positive = {
            'momentum': 0.001,  # Momentum positivo
            'consciousness': 0.8,
            'coherence': 0.8,
            'confidence': 0.8,
            'volume_surge': 2.0
        }
        
        signal2 = system.validate_trading_signal(mock_market_data, mock_quantum_metrics_positive)
        if signal2 is not None and signal2.direction == 'buy':
            print("✓ Momentum positivo gerou sinal BUY (correto)")
        else:
            print("✗ Momentum positivo não gerou sinal BUY (incorreto)")
            return False
            
        print("✓ Teste do método validate_trading_signal passou!")
        return True
        
    except Exception as e:
        print(f"✗ Erro no teste validate_trading_signal: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("TESTE DE CORREÇÃO DO SISTEMA QUALIA")
    print("=" * 60)
    
    success = True
    
    print("\n1. Testando existência dos métodos...")
    success &= test_method_exists()
    
    print("\n2. Testando funcionalidade do validate_trading_signal...")
    success &= test_validate_trading_signal()
    
    print("\n" + "=" * 60)
    if success:
        print("✓ TODOS OS TESTES PASSARAM! Correção bem-sucedida!")
    else:
        print("✗ ALGUNS TESTES FALHARAM! Verificar implementação.")
    print("=" * 60)
