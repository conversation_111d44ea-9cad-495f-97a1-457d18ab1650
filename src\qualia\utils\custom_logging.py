"""Formatadores de logging customizados para QUALIA."""

#!/usr/bin/env python3

import logging
import json
import yaml
import re
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any

from .correlation_id import get_correlation_id


def _logs_dir() -> Path:
    """Return log directory from environment or default."""

    return Path(os.getenv("QUALIA_LOGS_DIR", "logs"))


# Expressão regular para remover códigos de escape ANSI
ANSI_ESCAPE_PATTERN = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")


def _utc_now_iso_ms() -> str:
    """Return current UTC time formatted as ISO-8601 with milliseconds."""

    return datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"


class ColoredJSONFormatter(logging.Formatter):
    """Formatador de log que suporta saída compacta ou detalhada."""

    def __init__(self, compact: bool = False, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.is_tty = sys.stdout.isatty()
        self.compact = compact

    def format(self, record: logging.LogRecord) -> str:
        message = record.getMessage()

        if self.is_tty:
            if self.compact:
                return f"{record.levelname[0]} - {message}"
            return f"{_utc_now_iso_ms()} - {record.name} - {message}"

        clean_message = ANSI_ESCAPE_PATTERN.sub("", message)
        log_data: dict[str, Any] = {
            "timestamp": _utc_now_iso_ms(),
            "level": record.levelname,
            "name": record.name,
            "message": clean_message,
        }

        if not self.compact:
            log_data.update(
                {
                    "module": getattr(record, "module", record.name.split(".")[-1]),
                    "function": record.funcName,
                    "line": record.lineno,
                }
            )
        if record.exc_info:
            log_data["exception"] = self.formatException(record.exc_info)
        correlation_id = get_correlation_id()
        if correlation_id:
            log_data["correlation_id"] = correlation_id

        return json.dumps(log_data, ensure_ascii=False)


DEFAULT_CONFIG: dict[str, Any] = {
    "log_level": "INFO",
    "verbose_ticker_logging": False,
    "dedup_interval": 5.0,
    "handlers": {
        "console": {"level": "INFO"},
        "rotating_file": {
            "level": "DEBUG",
            "filename": "logs/qualia.log",
            "maxBytes": 1_048_576,
            "backupCount": 3,
        },
    },
}

DEFAULT_CONFIG_PATH = Path("config/logging.yaml")
ENV_LOGGING_CONFIG = "QUALIA_LOGGING_CONFIG"


def load_logging_config(path: Path | str | None = None) -> dict:
    """Return merged logging configuration.

    Parameters
    ----------
    path
        Optional YAML/JSON file overriding :data:`DEFAULT_CONFIG`.

    Returns
    -------
    dict
        Final configuration with relative file paths expanded to
        the directory returned by :func:`_logs_dir`.
    """

    cfg = json.loads(json.dumps(DEFAULT_CONFIG))
    cfg_path = Path(
        os.getenv(ENV_LOGGING_CONFIG)
        or (str(path) if path else str(DEFAULT_CONFIG_PATH))
    )
    try:
        with open(cfg_path) as fh:
            if cfg_path.suffix in {".yaml", ".yml"}:
                user_cfg = yaml.safe_load(fh) or {}
            else:
                user_cfg = json.load(fh)
    except FileNotFoundError:  # pragma: no cover - fallback when missing
        return cfg
    except Exception as exc:  # pragma: no cover - invalid file
        logging.getLogger(__name__).error("Erro ao ler %s: %s", cfg_path, exc)
        return cfg

    cfg.update({k: v for k, v in user_cfg.items() if k != "handlers"})
    if "handlers" in user_cfg:
        for name, handler_cfg in user_cfg["handlers"].items():
            merged = cfg["handlers"].get(name, {}).copy()
            merged.update(handler_cfg)
            cfg["handlers"][name] = merged

    # Resolve relative file paths against configured logs directory
    for handler in cfg.get("handlers", {}).values():
        filename = handler.get("filename")
        if isinstance(filename, str) and filename:
            log_path = Path(filename)
            if not log_path.is_absolute():
                handler["filename"] = str(_logs_dir() / log_path)
            else:
                handler["filename"] = str(log_path)
    return cfg


__all__ = [
    "ColoredJSONFormatter",
    "load_logging_config",
    "_utc_now_iso_ms",
    "DEFAULT_CONFIG_PATH",
    "ENV_LOGGING_CONFIG",
]
