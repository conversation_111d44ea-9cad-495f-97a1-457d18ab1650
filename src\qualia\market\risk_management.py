from typing import Any, Optional

"""Funções utilitárias de risco acopladas ao barramento de eventos."""

from ..risk.manager import (
    QUALIARiskManagerBase,
    fetch_exchange_min_lot_size,
    clear_min_lot_size_cache,
    purge_expired_min_lot_entries,
    MIN_LOT_TTL,
)
from ..core.qualia_logger import log_event
from ..memory.event_bus import SimpleEventBus
from ..events import RiskUpdateEvent
from ..utils.tracing import get_tracer
from ..config.feature_flags import feature_toggle
from ..risk_management.risk_manager_base import (
    PositionSizingResult,
    CapitalUpdateResult,
)

__all__ = [
    "fetch_exchange_min_lot_size",
    "clear_min_lot_size_cache",
    "purge_expired_min_lot_entries",
    "MIN_LOT_TTL",
    "calculate_position_size",
    "update_capital",
]


def calculate_position_size(
    risk_manager: QUALIARiskManagerBase,
    symbol: str,
    current_price: float,
    stop_loss_price: float,
    **kwargs: Any,
) -> PositionSizingResult:
    """Return position size and emit a ``risk.update`` event.

    Parameters
    ----------
    risk_manager:
        Instance implementing the position sizing logic.
    symbol:
        Asset identifier, e.g. ``"BTC/USDT"``.
    current_price:
        Latest market price.
    stop_loss_price:
        Price level for stop loss calculation.
    **kwargs:
        Additional arguments forwarded to ``risk_manager.calculate_position_size``.

    Returns
    -------
    PositionSizingResult
        Dictionary with sizing information from the risk manager.
    """

    sizing = risk_manager.calculate_position_size(
        symbol, current_price, stop_loss_price, **kwargs
    )
    log_event(
        event_type="risk.update",
        payload={"symbol": symbol, "sizing": sizing},
        source="market.risk_management",
        level="info",
    )
    return sizing


def update_capital(
    risk_manager: QUALIARiskManagerBase,
    new_capital: float,
    *,
    event_bus: Optional[SimpleEventBus] = None,
) -> CapitalUpdateResult:
    """Update risk manager capital with tracing and event publication.

    Parameters
    ----------
    risk_manager:
        Instance responsible for managing trading risk.
    new_capital:
        Latest account balance.
    event_bus:
        Optional :class:`SimpleEventBus` used to publish ``risk.update``.

    Returns
    -------
    CapitalUpdateResult
        Mapping with the updated capital information.
    """

    tracer = get_tracer(__name__)
    with tracer.start_as_current_span(
        "risk.update_capital", attributes={"new_capital": new_capital}
    ):
        result = risk_manager.update_capital(new_capital)
    if event_bus:
        event_bus.publish(
            "risk.update",
            RiskUpdateEvent(new_capital=result.get("current_capital", new_capital)),
        )
    return result
