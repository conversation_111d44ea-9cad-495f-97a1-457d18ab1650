from __future__ import annotations

from typing import List, Sequence

import json
from ..utils.logger import get_logger
from pathlib import Path

import numpy as np

logger = get_logger(__name__)


def flatten_vector(vec: Sequence[object]) -> List[float]:
    """Flatten a serialized vector structure.

    Parameters
    ----------
    vec : Sequence
        Sequence of numbers or dictionaries with ``real`` and ``imag`` keys.

    Returns
    -------
    list of float
        Vector flattened into a numeric list.

    Examples
    --------
    >>> flatten_vector([{"real": 1, "imag": 2}, [3, 4], 5])
    [1.0, 2.0, 3.0, 4.0, 5.0]
    """
    flat: List[float] = []
    for item in vec:
        if isinstance(item, dict) and "real" in item and "imag" in item:
            flat.extend([float(item["real"]), float(item["imag"])])
        elif (
            isinstance(item, list)
            and len(item) == 2
            and all(isinstance(v, (int, float)) for v in item)
        ):
            flat.extend([float(item[0]), float(item[1])])
        else:
            flat.append(float(item))
    return flat


def load_qpm_vectors(
    path: str | Path, *, fallback_size: int = 30, dim: int = 8
) -> List[np.ndarray]:
    """Load pattern vectors from disk or generate synthetic data.

    Parameters
    ----------
    path : str or Path
        Location of the persistence JSON file.
    fallback_size : int, optional
        Number of vectors to generate when the file is missing.
    dim : int, optional
        Dimensionality of the synthetic vectors.

    Returns
    -------
    list of ndarray
        Each stored vector as a ``numpy.ndarray``.
    """
    path = Path(path)
    if not path.exists():
        logger.warning(
            "QPM persistence file '%s' not found; generating %d synthetic vectors",
            path,
            fallback_size,
        )
        rng = np.random.default_rng(42)
        return [rng.normal(size=dim) for _ in range(fallback_size)]

    try:
        with path.open("r", encoding="utf-8") as f:
            data = json.load(f)
    except json.JSONDecodeError as exc:
        logger.error("Invalid JSON in '%s': %s", path, exc)
        return []
    memory = data.get("memory", {})
    vectors: List[np.ndarray] = []
    for patterns in memory.values():
        for pattern in patterns:
            qsp = pattern.get("quantum_signature_packet", {})
            vec = qsp.get("vector")
            if vec is not None:
                vectors.append(np.asarray(flatten_vector(vec), dtype=float))
    return vectors


def compute_similarity_distribution(vectors: List[np.ndarray]) -> np.ndarray:
    """Compute pairwise cosine similarity distribution.

    Parameters
    ----------
    vectors : list of ndarray
        Vectors to compare.

    Returns
    -------
    ndarray
        Array containing the upper triangular values of the cosine
        similarity matrix.

    Examples
    --------
    >>> compute_similarity_distribution([np.array([1, 0]), np.array([1, 0])])
    array([1.])
    """
    if len(vectors) < 2:
        return np.array([])

    lengths = {vec.size for vec in vectors}
    if len(lengths) > 1:
        max_dim = max(lengths)
        logger.warning(
            "Vectors with differing lengths detected; normalizing to size %d",
            max_dim,
        )
        normalized = []
        for vec in vectors:
            if vec.size == 0:
                logger.warning("Skipping empty vector during similarity computation")
                continue
            if vec.size < max_dim:
                vec = np.pad(vec, (0, max_dim - vec.size))
            elif vec.size > max_dim:
                vec = vec[:max_dim]
            normalized.append(vec)
        vectors = normalized
        if len(vectors) < 2:
            return np.array([])

    stacked = np.stack(vectors)
    norms = np.linalg.norm(stacked, axis=1, keepdims=True)
    norms[norms < 1e-9] = 1.0
    normalized = stacked / norms
    sims = normalized @ normalized.T
    return sims[np.triu_indices_from(sims, k=1)]
