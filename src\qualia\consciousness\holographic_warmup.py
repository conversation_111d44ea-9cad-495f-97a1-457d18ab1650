"""
QUALIA Holographic Warmup Manager.

Este módulo é responsável por "aquecer" o HolographicMarketUniverse,
alimentando-o com dados históricos antes do início do trading em tempo real.
Isso garante que o campo holográfico não comece "frio", permitindo a
detecção de padrões significativos desde o primeiro momento.
"""

from __future__ import annotations

import asyncio
import time
from typing import TYPE_CHECKING, Optional

from ..utils.logger import get_logger
from .enhanced_data_collector import EnhancedDataCollector
from .real_data_collectors import RealDataCollector

if TYPE_CHECKING:
    from .holographic_universe import HolographicMarketUniverse

logger = get_logger(__name__)


class HolographicWarmupManager:
    """
    Gerencia o processo de aquecimento do universo holográfico.

    Busca dados históricos de mercado e notícias e os injeta sequencialmente
    no universo para simular um período passado, criando um estado de campo
    com memória e padrões preexistentes.
    """

    def __init__(
        self,
        holographic_universe: "HolographicMarketUniverse",
        enhanced_data_collector: Optional[EnhancedDataCollector] = None,
        real_data_collector: Optional[RealDataCollector] = None,
    ):
        """
        Inicializa o Warmup Manager.

        Args:
            holographic_universe: A instância do universo a ser aquecida.
            enhanced_data_collector: Coletor para dados de mercado históricos (OHLCV).
            real_data_collector: Coletor para eventos de notícias históricos.
        """
        self.universe = holographic_universe
        self.enhanced_collector = enhanced_data_collector
        self.real_collector = real_data_collector
        logger.info("HolographicWarmupManager inicializado.")

    async def perform_warmup(
        self,
        warmup_period_hours: int = 72,
        simulation_step_seconds: int = 600,
    ) -> bool:
        """
        Executa o processo de aquecimento.

        Args:
            warmup_period_hours: O período histórico a ser simulado, em horas.
            simulation_step_seconds: A resolução da simulação, em segundos (ex: 600s = 10 min).

        Returns:
            True se o aquecimento foi bem-sucedido, False caso contrário.
        """
        if not self.enhanced_collector or not self.real_collector:
            logger.warning(
                "Coletores de dados não fornecidos. Não é possível executar o warm-up."
            )
            return False

        logger.info(
            f"🔥 Iniciando aquecimento holográfico para as últimas {warmup_period_hours} horas..."
        )
        logger.info(f"   Resolução da simulação: {simulation_step_seconds} segundos por passo.")

        end_time = int(time.time())
        start_time = end_time - (warmup_period_hours * 3600)
        total_steps = (end_time - start_time) // simulation_step_seconds

        logger.info(f"   Período: {time.ctime(start_time)} -> {time.ctime(end_time)}")
        logger.info(f"   Total de passos de simulação: {total_steps}")

        try:
            # 1. Obter dados históricos de mercado (OHLCV)
            logger.info("   1. Buscando dados históricos de mercado (OHLCV)...")
            historical_market_data = await self.enhanced_collector.fetch_historical_data_for_warmup(
                start_timestamp_ms=start_time * 1000,
                end_timestamp_ms=end_time * 1000,
            )
            if not historical_market_data:
                logger.warning("Nenhum dado de mercado histórico encontrado para o warm-up.")
                # Continua mesmo sem dados de mercado, pode usar apenas notícias
            else:
                logger.info(f"   ✅ {len(historical_market_data)} registros de mercado encontrados.")


            # 2. Obter eventos de notícias históricos (simulado por agora)
            # Uma implementação real usaria uma API de notícias com busca histórica
            logger.info("   2. Buscando eventos de notícias históricos...")
            historical_news = await self.real_collector.collect_news_events() # Reutiliza o coletor de tempo real por simplicidade
            logger.info(f"   ✅ {len(historical_news)} eventos de notícias encontrados para simulação.")


            # 3. Executar a simulação de aquecimento
            logger.info("   3. Executando simulação de aquecimento...")
            for i in range(total_steps):
                current_simulation_time = start_time + (i * simulation_step_seconds)

                # Encontrar dados relevantes para este passo de tempo
                market_data_slice = self._get_slice(
                    historical_market_data, current_simulation_time, simulation_step_seconds
                )
                news_slice = self._get_news_slice(
                    historical_news, current_simulation_time, simulation_step_seconds, total_steps
                )

                # Converter e injetar eventos
                events = self.real_collector.convert_to_holographic_events(
                    market_data_slice, news_slice, self.universe.field_size
                )

                for event in events:
                    # Ajusta o tempo do evento para o tempo da simulação
                    event.time = current_simulation_time
                    await self.universe.inject_holographic_event(event)

                # Evoluir o campo
                await self.universe.step_evolution(current_simulation_time)

                if (i + 1) % (total_steps // 10 or 1) == 0:
                    progress = ((i + 1) / total_steps) * 100
                    logger.info(f"      Progresso do warm-up: {progress:.0f}% completo...")

            final_summary = self.universe.get_field_summary()
            logger.info("   ✅ Simulação de aquecimento concluída.")
            logger.info(f"      Resumo final do campo: Energia={final_summary.get('field_energy', 0):.2f}, Entropia={final_summary.get('field_entropy', 0):.2f}")
            logger.info("🔥 Universo Holográfico aquecido e pronto para operação.")
            return True

        except Exception as e:
            logger.error(f"❌ Erro durante o warm-up holográfico: {e}", exc_info=True)
            return False

    def _get_slice(
        self, data: list, current_time: int, interval: int
    ) -> list:
        """Filtra dados para o intervalo de tempo atual."""
        return [
            item
            for item in data
            if current_time <= item.timestamp < current_time + interval
        ]

    def _get_news_slice(self, news: list, current_time: int, interval: int, total_steps: int) -> list:
        """Distribui as notícias coletadas ao longo do período de simulação."""
        if not news or not total_steps:
            return []
        
        # Simplesmente distribui as notícias uniformemente ao longo do tempo
        news_per_step = len(news) / total_steps
        current_step_index = (current_time - (time.time() - 72*3600)) // interval
        
        start_index = int(current_step_index * news_per_step)
        end_index = int((current_step_index + 1) * news_per_step)
        
        return news[start_index:end_index] 