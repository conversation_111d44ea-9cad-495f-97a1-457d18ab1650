"""Order Journal module."""

from __future__ import annotations

import json
from pathlib import Path
from threading import Lock
from typing import Any, Dict, List

from ..memory.event_bus import SimpleEventBus
from ..events import OrderJournalAppended, OrderJournalLoaded


class OrderJournal:
    """Append-only JSONL order journal."""

    def __init__(
        self, file_path: str, event_bus: SimpleEventBus | None = None
    ) -> None:
        self.path = Path(file_path)
        self.path.parent.mkdir(parents=True, exist_ok=True)
        self._lock = Lock()
        self.event_bus = event_bus

    def append(self, entry: Dict[str, Any]) -> None:
        """Append an entry to the journal."""
        with self._lock:
            with self.path.open("a", encoding="utf-8") as f:
                f.write(json.dumps(entry, default=str) + "\n")
        if self.event_bus:
            self.event_bus.publish(
                "persistence.order_journal.appended",
                OrderJournalAppended(entry=entry, path=self.path.as_posix()),
            )

    def load(self) -> List[Dict[str, Any]]:
        """Load all entries from the journal."""
        if not self.path.exists():
            return []
        with self.path.open("r", encoding="utf-8") as f:
            entries = [json.loads(line) for line in f if line.strip()]
        if self.event_bus:
            self.event_bus.publish(
                "persistence.order_journal.loaded",
                OrderJournalLoaded(path=self.path.as_posix(), entries=len(entries)),
            )
        return entries
