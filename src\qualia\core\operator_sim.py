from __future__ import annotations

"""Symbolic Intention Modulation utilities.

This module implements :class:`QuantumSymbolicTokenizer` and
:class:`OperatorSIM`, providing a bridge between symbolic intention tokens
and the :class:`~qualia.core.universe.QUALIAQuantumUniverse` parameters.
"""

from typing import Sequence, Dict, List, Any, Callable, Tuple
import logging
import operator
import time

from ..utils.logger import get_logger
from ..metrics.symbolic import statsd_increment, statsd_gauge
from .sim_graph import add_path as _sim_add_path
from .symbolic_intention_operator import SymbolicIntentionOperator
from .universe import QUALIAQuantumUniverse

logger = get_logger(__name__)  # structured QUALIA logger


class OperatorSIM:
    """
    Simulates the effect of high-level intentions by modulating
    the parameters of the quantum universe.
    """
    def __init__(self, cooldown: float = 10.0) -> None:  # noqa: D401 simple
        self.cooldown_seconds = float(cooldown)
        self._last_mod_ts: float = 0.0
        self.tokens: List[str] = []
        self.modulation_map: Dict[str, Tuple[str, float, Callable[[float, float], float]]] = {
            "AMPLIFY": ("base_lambda", 1.1, operator.mul),
            "ATTENUATE": ("base_lambda", 0.9, operator.mul),
            "DEEPEN": ("scr_depth", 1.2, operator.mul),
            "SHALLOW": ("scr_depth", 0.8, operator.mul),
            "FOCUS": ("thermal_coefficient", 0.9, operator.mul),
        }
        logger.info("OperatorSIM inicializado com mapa de modulação.")

    def listen(self, token: str) -> None:
        """Add an intentional token to the buffer.

        Logs a structured message when a valid token is buffered.
        """
        if token in self.modulation_map:
            self.tokens.append(token)
            logger.debug({
                "event": "operator_sim.listen",
                "token": token,
                "buffer_size": len(self.tokens),
            })

    def modulate_field(self, universe: Any) -> None:
        """Apply buffered tokens, clamp parameters, and emit structured logs."""
        now = time.time()
        if now - self._last_mod_ts < self.cooldown_seconds:
            statsd_increment("operator_sim.cooldown_hit_total")
            return
        self._last_mod_ts = now
        if not self.tokens:
            return

        logger.info({"event": "operator_sim.modulate_start", "tokens": list(self.tokens)})
        try:
            for token in self.tokens:
                if token not in self.modulation_map:
                    continue

                param, factor, op = self.modulation_map[token]
                if not hasattr(universe, param):
                    logger.warning({
                        "event": "operator_sim.param_missing",
                        "token": token,
                        "param": param,
                    })
                    continue

                old_val = getattr(universe, param)
                new_val = op(old_val, factor)

                # Clamp rules --------------------------------------------------
                if param == "base_lambda":
                    new_val = max(0.01, min(1.0, new_val))
                elif param == "scr_depth":
                    new_val = max(1, min(10, int(round(new_val))))

                setattr(universe, param, new_val)

                # Metrics ---------------------------------------------------
                statsd_increment("operator_sim.modulations_total", 1, [f"token:{token}", f"param:{param}"])
                # Expose gauges for parameters of interest
                if param == "scr_depth":
                    statsd_gauge("universe.scr_depth", new_val)
                elif param == "base_lambda":
                    statsd_gauge("universe.base_lambda", new_val)
                elif param == "thermal_coefficient":
                    statsd_gauge("universe.thermal_coefficient", new_val)

                logger.info({
                    "event": "operator_sim.param_modulated",
                    "token": token,
                    "param": param,
                    "old": old_val,
                    "new": new_val,
                })
        except Exception as exc:  # pragma: no cover - defensive logging
            logger.error({
                "event": "operator_sim.error",
                "error": str(exc),
            }, exc_info=True)
        finally:
            # Update token-transition graph for UI visualisation
            _sim_add_path(list(self.tokens))
            self.tokens.clear()
            logger.debug({"event": "operator_sim.buffer_cleared"})
