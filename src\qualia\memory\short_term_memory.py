"""Simple in-memory key-value store with expiration support."""

from __future__ import annotations

import threading
import time
from collections import OrderedDict
from typing import Any, Dict, List, Optional, Tuple

from datadog import DogStatsd

from ..risk_management.risk_manager_base import QUALIARiskManagerBase

from ..config.settings import stm_metrics_enabled

from ..utils.logger import get_logger
from ..memory.base_memory import BaseMemory
from ..memory.locking import with_lock

logger = get_logger(__name__)


class ShortTermMemory(BaseMemory):
    """In-memory key-value store with optional expiration.

    When ``thread_safe`` is ``True`` the instance serializes write operations
    using a :class:`threading.Lock`.
    """

    def __init__(
        self,
        capacity: int = 1024,
        default_ttl: float = 60.0,
        *,
        thread_safe: bool = False,
        statsd_client: Optional[DogStatsd] = None,
        risk_manager: Optional[QUALIARiskManagerBase] = None,
    ) -> None:
        """Initialize the memory store.

        Args:
            capacity: Maximum number of elements kept in memory.
            default_ttl: Default time to live in seconds for stored values.
            thread_safe: If ``True``, mutations are guarded by a ``threading.Lock``.
            statsd_client: Optional DogStatsd instance for emitting metrics.
        """
        self._capacity = capacity
        self._default_ttl = default_ttl
        self._data: OrderedDict[str, Any] = OrderedDict()
        self._expiry: Dict[str, float] = {}
        self._expiry_heap: List[Tuple[float, str]] = []
        self._heap_index: Dict[str, int] = {}
        self._lock: Optional[threading.Lock] = threading.Lock() if thread_safe else None
        self.statsd = statsd_client or (DogStatsd() if stm_metrics_enabled else None)
        self.risk_manager = risk_manager
        logger.info(
            "ShortTermMemory initialized with capacity=%s, default_ttl=%s",
            capacity,
            default_ttl,
        )

    def _require_risk_manager(self) -> None:
        """Ensure a valid ``QUALIARiskManagerBase`` is configured."""

        if not isinstance(self.risk_manager, QUALIARiskManagerBase):
            logger.error("RiskManager instance not configured")
            raise RuntimeError("RiskManager instance required")

    # Heap management utilities -------------------------------------------------

    def _sift_up(self, idx: int) -> None:
        """Bubble the element at ``idx`` up and update index mapping."""

        heap = self._expiry_heap
        item = heap[idx]
        while idx > 0:
            parent = (idx - 1) >> 1
            parent_item = heap[parent]
            if item[0] < parent_item[0]:
                heap[idx] = parent_item
                self._heap_index[parent_item[1]] = idx
                idx = parent
                continue
            break
        heap[idx] = item
        self._heap_index[item[1]] = idx

    def _sift_down(self, idx: int) -> None:
        """Push the element at ``idx`` down and update index mapping."""

        heap = self._expiry_heap
        end = len(heap)
        item = heap[idx]
        child = 2 * idx + 1
        while child < end:
            right = child + 1
            if right < end and heap[right][0] < heap[child][0]:
                child = right
            child_item = heap[child]
            if child_item[0] < item[0]:
                heap[idx] = child_item
                self._heap_index[child_item[1]] = idx
                idx = child
                child = 2 * idx + 1
            else:
                break
        heap[idx] = item
        self._heap_index[item[1]] = idx

    def _heap_push(self, expiry: float, key: str) -> None:
        """Push ``(expiry, key)`` onto the heap updating index mapping."""

        heap = self._expiry_heap
        heap.append((expiry, key))
        idx = len(heap) - 1
        self._heap_index[key] = idx
        self._sift_up(idx)

    def _heap_pop(self) -> Tuple[float, str]:
        """Pop the smallest item from the heap updating index mapping."""

        heap = self._expiry_heap
        last_item = heap.pop()
        if not heap:
            self._heap_index.pop(last_item[1], None)
            return last_item

        return_item = heap[0]
        self._heap_index.pop(return_item[1], None)
        heap[0] = last_item
        self._heap_index[last_item[1]] = 0
        self._sift_down(0)
        return return_item

    def _remove_obsolete_entries(self, key: str) -> None:
        """Remove ``key`` from the expiry heap in ``O(log n)``."""

        index = self._heap_index.pop(key, None)
        if index is None:
            return

        last_item = self._expiry_heap.pop()
        if index == len(self._expiry_heap):
            return

        self._expiry_heap[index] = last_item
        self._heap_index[last_item[1]] = index

        parent = (index - 1) >> 1
        if index > 0 and self._expiry_heap[index][0] < self._expiry_heap[parent][0]:
            self._sift_up(index)
        else:
            self._sift_down(index)

    def _prune_expired(self) -> None:
        """Incrementally remove expired keys using a heap."""
        now = time.monotonic()
        while self._expiry_heap and self._expiry_heap[0][0] <= now:
            expiry, key = self._heap_pop()
            if self._expiry.get(key) == expiry:
                self._data.pop(key, None)
                self._expiry.pop(key, None)
                if self.statsd:
                    self.statsd.increment("stm.expired")

    @with_lock
    def store(
        self, key: str, value: Any, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """Store a value with an optional TTL.

        Args:
            key: Identifier for the value.
            value: Object to store.
            metadata: Extra data. When it contains ``ttl`` the value overrides
                ``default_ttl``.
        """

        self._require_risk_manager()
        ttl = metadata.get("ttl", self._default_ttl) if metadata else self._default_ttl
        expiry = time.monotonic() + ttl
        self._prune_expired()
        if key in self._data:
            self._data.pop(key)
            self._expiry.pop(key, None)
            self._remove_obsolete_entries(key)
        elif len(self._data) >= self._capacity:
            oldest_key, _ = self._data.popitem(last=False)
            self._expiry.pop(oldest_key, None)
            self._remove_obsolete_entries(oldest_key)
            logger.debug("Evicted oldest key: %s", oldest_key)
            if self.statsd:
                self.statsd.increment("stm.evicted")
        self._data[key] = value
        self._expiry[key] = expiry
        self._heap_push(expiry, key)
        logger.debug("Stored key=%s with ttl=%s", key, ttl)
        if self.statsd:
            self.statsd.increment("stm.store")

    @with_lock
    def retrieve(self, key: str) -> Optional[Any]:
        """Retrieve a value if present and not expired."""

        self._require_risk_manager()
        self._prune_expired()
        if key not in self._data:
            if self.statsd:
                self.statsd.increment("stm.miss")
            return None
        self._data.move_to_end(key)
        if self.statsd:
            self.statsd.increment("stm.hit")
        return self._data[key]

    @with_lock
    def delete(self, key: str) -> bool:
        """Delete a key and its value.

        Args:
            key: Identifier of the item to remove.

        Returns:
            ``True`` if the key existed.
        """

        self._require_risk_manager()
        existed = key in self._data
        self._data.pop(key, None)
        self._expiry.pop(key, None)
        self._remove_obsolete_entries(key)
        if existed:
            logger.debug("Deleted key=%s", key)
            if self.statsd:
                self.statsd.increment("stm.delete")
        return existed

    @with_lock
    def list_keys(self, prefix: Optional[str] = None) -> List[str]:
        """Return currently stored keys.

        Args:
            prefix: Only return keys starting with this prefix when provided.

        Returns:
            List of key names.
        """

        self._prune_expired()
        keys = list(self._data.keys())
        if prefix:
            keys = [k for k in keys if k.startswith(prefix)]
        return keys

    @with_lock
    def clear(self) -> None:
        """Remove all keys from memory."""

        self._require_risk_manager()
        self._data.clear()
        self._expiry.clear()
        self._expiry_heap.clear()
        self._heap_index.clear()
        logger.debug("Cleared all short term memory")
        if self.statsd:
            self.statsd.increment("stm.clear")

    @with_lock
    def get_info(self) -> Dict[str, Any]:
        """Return metadata about the store."""

        self._prune_expired()
        size = len(self._data)
        return {"type": "ShortTermMemory", "size": size}
