"""Utilities to load QUALIA configuration files."""

from __future__ import annotations

import json
try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()
import os
import threading
from importlib import resources
from pathlib import Path
from typing import Any, Dict, Optional

from importlib import import_module
from ..utils.logger import get_logger

try:
    from dotenv import load_dotenv
except Exception:  # pragma: no cover - optional dependency

    def load_dotenv(*args: Any, **kwargs: Any) -> None:  # type: ignore[misc]
        return None


from .settings import settings, get_env, REPO_ROOT

logger = get_logger(__name__)

# Default resource to the JSON schema used for configuration validation
_SCHEMA_RESOURCE = resources.files(__package__).joinpath("strategy_schema.json")


def validate_config(data: Dict[str, Any], schema_path: Optional[str] = None) -> None:
    """Validate configuration against the strategy JSON schema.

    Parameters
    ----------
    data:
        Configuration dictionary to validate.
    schema_path:
        Optional path to a JSON schema file. Defaults to the packaged
        ``qualia.config/strategy_schema.json`` resource.

    Raises
    ------
    jsonschema.ValidationError
        If the configuration does not match the schema.
    FileNotFoundError
        If ``schema_path`` does not exist.
    """

    if schema_path is not None:
        schema_file = Path(schema_path)
        if not schema_file.exists():
            raise FileNotFoundError(f"Schema file not found: {schema_file}")
        with schema_file.open("r", encoding="utf-8") as fh:
            schema = json.load(fh)
    else:
        schema_file = _SCHEMA_RESOURCE
        with schema_file.open("r", encoding="utf-8") as fh:
            schema = json.load(fh)

    try:
        jsonschema_validate = import_module("jsonschema").validate
    except ModuleNotFoundError:  # pragma: no cover - optional dependency
        logger.warning(
            "jsonschema not installed; skipping configuration validation"
        )
        return
    jsonschema_validate(instance=data, schema=schema)
    logger.debug("Configuration validated against schema %s", schema_file)


class ConfigLoader:
    """Load QUALIA configuration from JSON files.

    Parameters
    ----------
    config_path:
        Optional path to the configuration file. If ``None`` the value from
        :class:`src.qualia.config.settings.Settings` is used.
    """

    def __init__(self, config_path: Optional[str] = None) -> None:
        self.config_path = os.path.abspath(
            config_path or settings.strategy_parameters_path
        )
        self._last_modified: Optional[int] = None
        self._config: Dict[str, Any] = {}
        self._lock = threading.Lock()

    # Public API -----------------------------------------------------------------
    @property
    def data(self) -> Dict[str, Any]:
        """Return the loaded configuration, loading it if necessary."""
        return self.load()

    def load(self, force_reload: bool = False) -> Dict[str, Any]:
        """Load the configuration from disk.

        Parameters
        ----------
        force_reload:
            Reload even if the file modification time did not change.
        """
        path = self.config_path
        if not os.path.exists(path):
            logger.warning(
                "Configuration file not found: %s. Using empty defaults.", path
            )
            with self._lock:
                self._config = {}
                self._last_modified = None
            return self._config

        current_mtime = os.stat(path).st_mtime_ns
        with self._lock:
            if (
                self._config
                and not force_reload
                and self._last_modified == current_mtime
            ):
                return self._config

            with open(path, "r", encoding="utf-8") as fh:
                try:
                    if path.endswith((".yaml", ".yml")):
                        self._config = yaml.safe_load(fh) or {}
                    else:
                        self._config = json.load(fh)
                except Exception as exc:
                    logger.error("Failed to parse config at %s", path)
                    raise ValueError(
                        f"Invalid configuration file {path}: {exc}"
                    ) from exc
            self._last_modified = current_mtime
            logger.debug("Loaded configuration from %s", path)
            return self._config

    def reload(self) -> Dict[str, Any]:
        """Reload the configuration from disk ignoring caches."""
        return self.load(force_reload=True)

    def get_section(self, section: str, default: Optional[Any] = None) -> Any:
        """Return a configuration subsection."""
        return self.data.get(section, default)

    def get(self, key: str, default: Optional[Any] = None) -> Any:
        """Convenience wrapper around ``dict.get`` for the loaded configuration."""
        return self.data.get(key, default)

    def validate(self, schema_path: Optional[str] = None) -> None:
        """Validate the loaded configuration using :func:`validate_config`."""
        validate_config(self.data, schema_path)


def load_env_and_json(
    env_path: Optional[str] = None, json_path: Optional[str] = None
) -> Dict[str, Any]:
    """Load environment variables from ``.env`` and a configuration file.

    Parameters
    ----------
    env_path
        Optional path to the ``.env`` file. Defaults to ``QUALIA_ENV_PATH`` or
        ``<repo>/.env``.
    json_path
        Optional path to the configuration file. Defaults to
        :class:`~src.qualia.config.settings.Settings.strategy_parameters_path`.

    Returns
    -------
    Dict[str, Any]
        Parsed configuration dictionary.
    """

    env_file = Path(
        env_path or get_env("QUALIA_ENV_PATH", str(REPO_ROOT / ".env"), warn=False)
    )
    if env_file.exists():
        logger.info("Carregando variáveis de ambiente do arquivo: %s", env_file)
        load_dotenv(dotenv_path=str(env_file), override=True)
    else:
        logger.warning("Arquivo .env não encontrado: %s", env_file)

    loader = ConfigLoader(json_path or settings.strategy_parameters_path)
    return loader.load()


__all__ = ["ConfigLoader", "validate_config", "load_env_and_json"]
