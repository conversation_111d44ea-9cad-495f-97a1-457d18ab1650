from __future__ import annotations

"""Dashboard de sinais do HolographicUniverse."""

from typing import Any, List

import dash
from dash import dcc, html, Input, Output

from ...utils.event_bus import EventBus, HolographicSignalsUpdated
from ...utils.logger import get_logger

logger = get_logger(__name__)

_SIGNALS: List[Any] = []


def init_app(bus: EventBus) -> dash.Dash:
    """Inicializa aplicativo Dash para exibir sinais holográficos.

    Parameters
    ----------
    bus
        Barramento de eventos utilizado para receber ``HolographicSignalsUpdated``.

    Returns
    -------
    dash.Dash
        Instância do aplicativo Dash configurado.
    """
    app = dash.Dash(__name__)
    app.layout = html.Div(
        [
            html.H3("Holographic Universe – Sinais"),
            html.Div(id="signals-table"),
            dcc.Interval(id="signals-interval", interval=2000, n_intervals=0),
        ]
    )

    @app.callback(
        Output("signals-table", "children"), Input("signals-interval", "n_intervals")
    )
    def _update(_: int) -> html.Table:  # noqa: D401
        rows = [
            html.Tr(
                [html.Td(s.symbol), html.Td(s.action), html.Td(f"{s.confidence:.2f}")]
            )
            for s in _SIGNALS[-10:]
        ]
        header = html.Thead(
            html.Tr([html.Th("Símbolo"), html.Th("Ação"), html.Th("Confiança")])
        )
        body = html.Tbody(rows)
        return html.Table([header, body])

    def _on_signals(event: HolographicSignalsUpdated) -> None:
        for sig in event.signals.values():
            _SIGNALS.append(sig)
        del _SIGNALS[:-50]
        logger.debug("Recebidos %d sinais holográficos", len(event.signals))

    bus.subscribe("holographic.signals.updated", _on_signals)
    logger.info("Dashboard holográfico inicializado e inscrito em eventos")
    return app
