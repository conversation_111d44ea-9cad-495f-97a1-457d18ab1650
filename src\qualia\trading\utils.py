"""Utility helpers for trading calculations."""

from __future__ import annotations

import base64
import binascii
from datetime import datetime

from ..config.settings import get_env
from ..utils.logger import get_logger
from ..utils.timeframe import (
    timeframe_to_minutes,
    timeframe_to_pandas_freq,
    timeframe_to_milliseconds,
)

logger = get_logger(__name__)


def next_candle_time(now: datetime, timeframe: str) -> datetime:
    """Return the open time of the next candle for ``timeframe``."""
    minutes = timeframe_to_minutes(timeframe)
    if minutes <= 0:
        minutes = 1

    interval_sec = minutes * 60
    current_ts = int(now.timestamp())
    next_ts = ((current_ts // interval_sec) + 1) * interval_sec

    if now.tzinfo is not None:
        return datetime.fromtimestamp(next_ts, tz=now.tzinfo)

    return datetime.utcfromtimestamp(next_ts)


def validate_secret_key() -> None:
    """Ensure that ``QUALIA_SECRET_KEY`` is not weak or malformed."""
    key = get_env("QUALIA_SECRET_KEY", None, warn=False)
    if key is None:
        return

    if key == "dev_secret_key":
        logger.error(
            "QUALIA_SECRET_KEY não deve ser 'dev_secret_key'. Gere uma chave "
            "segura com: python -c 'from cryptography.fernet import Fernet; "
            "print(Fernet.generate_key().decode())'"
        )
        raise SystemExit(1)

    try:
        decoded = base64.urlsafe_b64decode(key)
    except (binascii.Error, ValueError) as exc:
        logger.exception(
            "QUALIA_SECRET_KEY inválida. Gere uma chave URL-safe base64 de "
            "32 bytes com: python -c 'from cryptography.fernet import Fernet; "
            "print(Fernet.generate_key().decode())'",
            exc_info=exc,
        )
        raise SystemExit(1) from exc

    if len(decoded) != 32:
        logger.error(
            "QUALIA_SECRET_KEY inválida. Gere uma chave URL-safe base64 de "
            "32 bytes com: python -c 'from cryptography.fernet import Fernet; "
            "print(Fernet.generate_key().decode())'"
        )
        raise SystemExit(1)


__all__ = [
    "timeframe_to_minutes",
    "timeframe_to_milliseconds",
    "next_candle_time",
    "validate_secret_key",
]
