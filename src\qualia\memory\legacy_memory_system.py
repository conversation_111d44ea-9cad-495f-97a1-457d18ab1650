"""
Memory system for QUALIA trading
"""

import logging
from typing import Dict, Any, List
import time

logger = logging.getLogger(__name__)


class LegacyMemorySystem:
    """Simple memory system used in early versions of QUALIA."""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.memories: List[Dict[str, Any]] = []
        self.max_memories = config.get("max_memories", 10000)

    async def initialize(self):
        """Initialize the memory system"""
        logger.info("Memory system initialized")

    async def store_experience(self, experience: Dict[str, Any]):
        """Store a trading experience in memory."""

        try:
            experience["stored_at"] = time.time()
            self.memories.append(experience)

            # Limit memory size
            if len(self.memories) > self.max_memories:
                self.memories.pop(0)

        except (TypeError, ValueError, IOError) as exc:
            logger.error("Error storing experience: %s", exc, exc_info=True)

    async def retrieve_similar_experiences(
        self, query: Dict[str, Any], limit: int = 10
    ) -> List[Dict[str, Any]]:
        """Retrieve similar experiences from memory."""
        try:
            # Simple retrieval - return most recent experiences
            # This would be enhanced with similarity matching
            return self.memories[-limit:] if self.memories else []

        except (TypeError, ValueError, IOError) as exc:
            logger.error("Error retrieving experiences: %s", exc, exc_info=True)
            return []

    async def shutdown(self):
        """Shutdown the memory system"""
        logger.info("Memory system shutdown")
