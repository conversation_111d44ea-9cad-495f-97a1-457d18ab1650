#!/usr/bin/env python3
"""
Script de Análise de Resultados de Calibração QUALIA
Analisa e compara resultados de calibrações anteriores
"""

import json
import sys
import argparse
from pathlib import Path
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib.pyplot as plt
import seaborn as sns

def parse_arguments():
    """Parse argumentos da linha de comando"""
    parser = argparse.ArgumentParser(description="Análise de Resultados de Calibração QUALIA")
    
    parser.add_argument('--data-dir', type=str, default='data/calibration',
                       help='Diretório com dados de calibração')
    parser.add_argument('--compare', action='store_true',
                       help='Comparar múltiplas calibrações')
    parser.add_argument('--plot', action='store_true',
                       help='Gerar gráficos de análise')
    parser.add_argument('--export', type=str,
                       help='Exportar análise para arquivo')
    
    return parser.parse_args()

def load_calibration_data(data_dir):
    """Carrega dados de calibração"""
    data_path = Path(data_dir)
    
    if not data_path.exists():
        print(f" Diretório não encontrado: {data_path}")
        return []
    
    # Buscar arquivos de calibração
    calibration_files = list(data_path.glob("*calibration*.json"))
    
    if not calibration_files:
        print(f" Nenhum arquivo de calibração encontrado em: {data_path}")
        return []
    
    # Carregar dados
    calibrations = []
    for file_path in sorted(calibration_files):
        try:
            with open(file_path, 'r') as f:
                data = json.load(f)
                data['file_path'] = str(file_path)
                data['file_name'] = file_path.name
                calibrations.append(data)
        except Exception as e:
            print(f" Erro carregando {file_path}: {e}")
    
    return calibrations

def analyze_single_calibration(calibration_data):
    """Analisa uma única calibração"""
    print(f"\n ANÁLISE: {calibration_data.get('file_name', 'Unknown')}")
    print("=" * 60)
    
    # Informações básicas
    timestamp = calibration_data.get('timestamp', 'Unknown')
    print(f" Timestamp: {timestamp}")
    
    # Thresholds agregados
    aggregated = calibration_data.get('aggregated_thresholds', {})
    if aggregated:
        print("\n THRESHOLDS AGREGADOS:")
        for metric, value in aggregated.items():
            print(f"   {metric}: {value:.3f}")
    
    # Estatísticas por ativo
    individual = calibration_data.get('individual_results', {})
    if individual:
        success_rates = [result['success_rate'] for result in individual.values()]
        total_points = [result['total_points'] for result in individual.values()]
        profitable_points = [result['profitable_points'] for result in individual.values()]
        
        print(f"\n ESTATÍSTICAS ({len(individual)} ativos):")
        print(f"   Taxa de sucesso média: {np.mean(success_rates):.1%}")
        print(f"   Taxa de sucesso mediana: {np.median(success_rates):.1%}")
        print(f"   Pontos totais: {sum(total_points):,}")
        print(f"   Pontos lucrativos: {sum(profitable_points):,}")
        print(f"   Taxa geral: {sum(profitable_points)/sum(total_points):.1%}")
        
        # Top e bottom performers
        sorted_assets = sorted(individual.items(), key=lambda x: x[1]['success_rate'], reverse=True)
        
        print(f"\n TOP 3 ATIVOS:")
        for i, (symbol, data) in enumerate(sorted_assets[:3]):
            print(f"   {i+1}. {symbol}: {data['success_rate']:.1%} "
                  f"({data['profitable_points']}/{data['total_points']})")
        
        print(f"\ BOTTOM 3 ATIVOS:")
        for i, (symbol, data) in enumerate(sorted_assets[-3:]):
            print(f"   {i+1}. {symbol}: {data['success_rate']:.1%} "
                  f"({data['profitable_points']}/{data['total_points']})")

def compare_calibrations(calibrations):
    """Compara múltiplas calibrações"""
    if len(calibrations) < 2:
        print(" Precisa de pelo menos 2 calibrações para comparar")
        return
    
    print(f"\n COMPARAÇÃO DE {len(calibrations)} CALIBRAÇÕES")
    print("=" * 60)
    
    # Comparar thresholds agregados
    all_metrics = set()
    for cal in calibrations:
        if 'aggregated_thresholds' in cal:
            all_metrics.update(cal['aggregated_thresholds'].keys())
    
    if all_metrics:
        print("\n EVOLUÇÃO DOS THRESHOLDS:")
        for metric in sorted(all_metrics):
            print(f"\n   {metric.upper()}:")
            values = []
            for i, cal in enumerate(calibrations):
                thresh = cal.get('aggregated_thresholds', {})
                if metric in thresh:
                    timestamp = cal.get('timestamp', f'Cal_{i+1}')[:16]  # Truncar timestamp
                    value = thresh[metric]
                    values.append(value)
                    print(f"      {timestamp}: {value:.3f}")
            
            if len(values) > 1:
                change = values[-1] - values[0]
                change_pct = (change / values[0]) * 100 if values[0] != 0 else 0
                print(f"      Mudança: {change:+.3f} ({change_pct:+.1f}%)")
    
    # Comparar performance geral
    print(f"\n EVOLUÇÃO DA PERFORMANCE:")
    for i, cal in enumerate(calibrations):
        individual = cal.get('individual_results', {})
        if individual:
            success_rates = [result['success_rate'] for result in individual.values()]
            avg_success = np.mean(success_rates)
            timestamp = cal.get('timestamp', f'Cal_{i+1}')[:16]
            print(f"   {timestamp}: {avg_success:.1%} taxa média")

def generate_plots(calibrations):
    """Gera gráficos de análise"""
    if not calibrations:
        print(" Nenhum dado para plotar")
        return
    
    try:
        # Configurar estilo
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Análise de Calibração QUALIA', fontsize=16)
        
        # 1. Evolução dos thresholds principais
        ax1 = axes[0, 0]
        main_metrics = ['consciousness', 'coherence', 'confidence']
        
        for metric in main_metrics:
            values = []
            timestamps = []
            for cal in calibrations:
                thresh = cal.get('aggregated_thresholds', {})
                if metric in thresh:
                    values.append(thresh[metric])
                    timestamps.append(cal.get('timestamp', '')[:10])
            
            if values:
                ax1.plot(range(len(values)), values, marker='o', label=metric)
        
        ax1.set_title('Evolução dos Thresholds Principais')
        ax1.set_xlabel('Calibração')
        ax1.set_ylabel('Valor do Threshold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. Distribuição de taxa de sucesso por ativo
        ax2 = axes[0, 1]
        all_success_rates = []
        
        for cal in calibrations:
            individual = cal.get('individual_results', {})
            success_rates = [result['success_rate'] for result in individual.values()]
            all_success_rates.extend(success_rates)
        
        if all_success_rates:
            ax2.hist(all_success_rates, bins=20, alpha=0.7, edgecolor='black')
            ax2.set_title('Distribuição de Taxa de Sucesso')
            ax2.set_xlabel('Taxa de Sucesso')
            ax2.set_ylabel('Frequência')
            ax2.axvline(np.mean(all_success_rates), color='red', linestyle='--', 
                       label=f'Média: {np.mean(all_success_rates):.1%}')
            ax2.legend()
        
        # 3. Correlação entre métricas (última calibração)
        ax3 = axes[1, 0]
        if calibrations:
            last_cal = calibrations[-1]
            individual = last_cal.get('individual_results', {})
            
            # Criar DataFrame com thresholds recomendados
            threshold_data = []
            for symbol, result in individual.items():
                if 'recommended_thresholds' in result:
                    row = result['recommended_thresholds'].copy()
                    row['symbol'] = symbol
                    threshold_data.append(row)
            
            if threshold_data:
                df = pd.DataFrame(threshold_data)
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                
                if len(numeric_cols) > 1:
                    corr_matrix = df[numeric_cols].corr()
                    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0,
                               ax=ax3, square=True)
                    ax3.set_title('Correlação entre Métricas')
        
        # 4. Performance por ativo (última calibração)
        ax4 = axes[1, 1]
        if calibrations:
            last_cal = calibrations[-1]
            individual = last_cal.get('individual_results', {})
            
            if individual:
                symbols = list(individual.keys())[:10]  # Top 10
                success_rates = [individual[s]['success_rate'] for s in symbols]
                
                bars = ax4.bar(range(len(symbols)), success_rates)
                ax4.set_title('Performance por Ativo (Top 10)')
                ax4.set_xlabel('Ativo')
                ax4.set_ylabel('Taxa de Sucesso')
                ax4.set_xticks(range(len(symbols)))
                ax4.set_xticklabels(symbols, rotation=45)
                
                # Colorir barras baseado na performance
                for i, bar in enumerate(bars):
                    if success_rates[i] > 0.3:
                        bar.set_color('green')
                    elif success_rates[i] > 0.2:
                        bar.set_color('orange')
                    else:
                        bar.set_color('red')
        
        plt.tight_layout()
        
        # Salvar gráfico
        plot_path = Path('data/calibration/analysis_plots.png')
        plot_path.parent.mkdir(parents=True, exist_ok=True)
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f" Gráficos salvos em: {plot_path}")
        
        plt.show()
        
    except Exception as e:
        print(f" Erro gerando gráficos: {e}")

def export_analysis(calibrations, export_path):
    """Exporta análise para arquivo"""
    try:
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'total_calibrations': len(calibrations),
            'summary': {}
        }
        
        if calibrations:
            # Análise da última calibração
            last_cal = calibrations[-1]
            individual = last_cal.get('individual_results', {})
            
            if individual:
                success_rates = [result['success_rate'] for result in individual.values()]
                analysis['summary'] = {
                    'average_success_rate': float(np.mean(success_rates)),
                    'median_success_rate': float(np.median(success_rates)),
                    'total_assets': len(individual),
                    'best_asset': max(individual.items(), key=lambda x: x[1]['success_rate'])[0],
                    'worst_asset': min(individual.items(), key=lambda x: x[1]['success_rate'])[0]
                }
            
            # Thresholds atuais
            analysis['current_thresholds'] = last_cal.get('aggregated_thresholds', {})
        
        # Salvar
        with open(export_path, 'w') as f:
            json.dump(analysis, f, indent=2, default=str)
        
        print(f" Análise exportada para: {export_path}")
         
    except Exceptionas e:
        print(f" Erro exportando análise: {e}")

def main():
    """Função principal"""
    args = parse_arguments()
    
    print(" QUALIA - Análise de Resultados de Calibração")
    print("=" * 60)
    
    # Carregar dados
    calibrations = load_calibration_data(args.data_dir)
    
    if not calibrations:
        return
    
    print(f" Carregadas {len(calibrations)} calibrações")
    
    # Análise individual
    for calibration in calibrations:
        analyze_single_calibration(calibration)
    
    # Comparação
    if args.compare and len(calibrations) > 1:
        compare_calibrations(calibrations)
    
    # Gráficos
    if args.plot:
        generate_plots(calibrations)
    
    # Exportar
    if args.export:
        export_analysis(calibrations, args.export)

if __name__ == "__main__":
    main()
