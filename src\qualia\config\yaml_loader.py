from __future__ import annotations

"""Helper to load YAML configuration files with environment overrides."""

import os
from pathlib import Path
from typing import Any, Dict

try:
    import yaml
except ModuleNotFoundError:
    class YAMLError(Exception):
        pass

    class DummyYAML:
        def safe_load(self, stream):
            return {}
        @property
        def YAMLError(self):
            return YAMLError

    yaml = DummyYAML()


def load_yaml_config(env_var: str, default_path: Path, *, logger) -> Dict[str, Any]:
    """Load YAML data from ``env_var`` path or ``default_path``.

    Parameters
    ----------
    env_var:
        Environment variable providing the path to the YAML file.
    default_path:
        Path used when the environment variable is not set.
    logger:
        Logger instance used for warnings and errors.

    Returns
    -------
    Dict[str, Any]
        Mapping with the YAML contents or an empty mapping on error.
    """

    path_value = os.getenv(env_var)
    config_path = Path(path_value) if path_value else default_path
    try:
        with config_path.open("r", encoding="utf-8") as fh:
            data = yaml.safe_load(fh) or {}
        if not isinstance(data, dict):
            logger.error("Arquivo YAML invalido: %s", config_path)
            return {}
        return data
    except FileNotFoundError:
        logger.warning("Arquivo YAML nao encontrado em %s", config_path)
    except Exception as exc:  # pragma: no cover - unexpected errors
        logger.error("Falha ao carregar YAML %s: %s", config_path, exc)
    return {}


__all__ = ["load_yaml_config"]
