#!/usr/bin/env python3
"""
Script para forçar adaptação de thresholds quando o sistema está travado
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.adaptive_threshold_system import TradingMode
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def force_adaptation():
    """Força adaptação de thresholds"""
    
    print("🚨 QUALIA - Forçar Adaptação de Thresholds")
    print("=" * 50)
    
    try:
        # Inicializar sistema
        logger.info("🔧 Inicializando sistema QUALIA...")
        trading_system = QualiaBinanceCorrectedSystem()
        await trading_system.initialize()
        
        # Mostrar estado atual
        print("\n📊 ESTADO ATUAL:")
        print(f"   Modo: {trading_system.adaptive_manager.current_mode.value}")
        print(f"   Ciclos sem sinais: {trading_system.adaptive_manager.cycles_without_signals}")
        
        current_thresholds = trading_system.adaptive_manager.current_thresholds
        print(f"   Consciousness: {current_thresholds.consciousness:.3f}")
        print(f"   Coherence: {current_thresholds.coherence:.3f}")
        print(f"   Confidence: {current_thresholds.confidence:.3f}")
        print(f"   Volume Surge: {current_thresholds.volume_surge_min:.3f}")
        print(f"   Momentum: {current_thresholds.momentum_min:.4f}")
        
        # Opções de adaptação
        print("\n🎯 OPÇÕES DE ADAPTAÇÃO:")
        print("1. Forçar modo AGGRESSIVE")
        print("2. Aplicar thresholds empíricos otimizados")
        print("3. Aplicar thresholds ultra-otimizados")
        print("4. Reset sistema inteligente")
        print("5. Cancelar")
        
        choice = input("\nEscolha uma opção (1-5): ").strip()
        
        if choice == "1":
            # Forçar AGGRESSIVE
            trading_system.adaptive_manager.adapt_thresholds(force_mode=TradingMode.AGGRESSIVE)
            print("✅ Modo AGGRESSIVE aplicado!")
            
        elif choice == "2":
            # Thresholds empíricos
            trading_system.adaptive_manager.current_thresholds = trading_system.adaptive_manager.empirical_thresholds
            print("✅ Thresholds empíricos aplicados!")
            
        elif choice == "3":
            # Thresholds ultra-otimizados
            trading_system.adaptive_manager.current_thresholds = trading_system.adaptive_manager.ultra_optimized_thresholds
            print("✅ Thresholds ultra-otimizados aplicados!")
            
        elif choice == "4":
            # Reset sistema inteligente
            trading_system.adaptive_manager.intelligent_adaptation.metrics.cycles_since_last_adaptation = 0
            trading_system.adaptive_manager.intelligent_adaptation.metrics.consecutive_low_rate_cycles = 5
            print("✅ Sistema inteligente resetado!")
            
        elif choice == "5":
            print("❌ Operação cancelada")
            return
        else:
            print("❌ Opção inválida")
            return
        
        # Mostrar novo estado
        print("\n📊 NOVO ESTADO:")
        new_thresholds = trading_system.adaptive_manager.current_thresholds
        print(f"   Consciousness: {new_thresholds.consciousness:.3f}")
        print(f"   Coherence: {new_thresholds.coherence:.3f}")
        print(f"   Confidence: {new_thresholds.confidence:.3f}")
        print(f"   Volume Surge: {new_thresholds.volume_surge_min:.3f}")
        print(f"   Momentum: {new_thresholds.momentum_min:.4f}")
        
        # Atualizar sistema principal
        trading_system.quantum_thresholds.update({
            'consciousness': new_thresholds.consciousness,
            'coherence': new_thresholds.coherence,
            'confidence': new_thresholds.confidence,
            'volume_surge_min': new_thresholds.volume_surge_min,
            'momentum_min': new_thresholds.momentum_min,
            'spectral_phi_alignment_min': new_thresholds.spectral_phi_alignment_min,
            'golden_symmetry_min': new_thresholds.golden_symmetry_min
        })
        
        print("\n🎯 Adaptação forçada concluída!")
        print("   O sistema deve encontrar sinais no próximo ciclo.")
        
    except Exception as e:
        logger.error(f"❌ Erro durante adaptação forçada: {e}")
        raise
    
    finally:
        if 'trading_system' in locals():
            await trading_system.cleanup()

def main():
    """Wrapper principal"""
    try:
        asyncio.run(force_adaptation())
    except KeyboardInterrupt:
        print("\n⚠️ Operação interrompida pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
