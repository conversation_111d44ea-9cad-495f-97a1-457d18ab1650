"""Topological metrics for QUALIA."""

from __future__ import annotations

from typing import Set, <PERSON><PERSON>

import numpy as np
from scipy.spatial import Delaunay


def compute_topological_coherence(points: np.ndarray) -> float:
    """Compute coherence of ``points`` based on Delaunay edge uniformity.

    The metric is ``1 - std(lengths) / mean(lengths)`` where ``lengths`` are the
    unique edge lengths of the Delaunay triangulation. Values are clipped to the
    range ``[0.0, 1.0]``.

    Parameters
    ----------
    points
        Array of shape ``(n_points, dimensions)`` with point coordinates.

    Returns
    -------
    float
        Coherence score in ``[0.0, 1.0]``. Returns ``0.0`` when ``points`` are
        insufficient for triangulation or when the triangulation fails.
    """

    pts = np.asarray(points, dtype=float)
    if pts.ndim != 2 or pts.shape[0] < 3:
        return 0.0

    try:
        tri = Delaunay(pts)
    except Exception:
        return 0.0

    edges: Set[Tuple[int, int]] = set()
    for simplex in tri.simplices:
        for i in range(len(simplex)):
            for j in range(i + 1, len(simplex)):
                a, b = sorted((int(simplex[i]), int(simplex[j])))
                edges.add((a, b))

    if not edges:
        return 0.0

    lengths = np.array([np.linalg.norm(pts[a] - pts[b]) for a, b in edges])
    mean_length = lengths.mean()
    if mean_length == 0:
        return 1.0

    coherence = 1.0 - lengths.std() / mean_length
    return float(np.clip(coherence, 0.0, 1.0))


__all__ = ["compute_topological_coherence"]
