from __future__ import annotations

"""Cross-modal coherence monitor for QUALIA.

Escuta múltiplos *streams* no ``SimpleEventBus`` (NEWS, AUDIO, VIDEO, IMAGE)
e calcula a métrica ``cross_modal_coherence`` sobre uma janela deslizante de
N segundos, publicando o valor agregado para quem se interessar.

Este é um componente opcional, com dependência leve: apenas *numpy* e o próprio
``performance_metrics.cross_modal_coherence``.
"""

import time
from collections import deque
from typing import Any, Dict, List, Optional

import numpy as np

from qualia.memory.event_bus import (
    SimpleEventBus,
    NEWS_STREAM,
    AUDIO_STREAM,
    VIDEO_STREAM,
    IMAGE_STREAM,
)
from qualia.events import CrossModalCoherenceEvent
from qualia.metrics.performance_metrics import cross_modal_coherence
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

# Último valor calculado – acessível para endpoints REST.
LATEST_COHERENCE: float | None = None


class CoherenceMonitor:
    """Agregador online de coerência multimodal."""

    _MAX_WINDOW_SEC = 30.0

    def __init__(
        self,
        event_bus: SimpleEventBus,
        publish_event: str = "nexus.cross_modal_coherence",
        window_seconds: float = 5.0,
    ) -> None:
        if window_seconds <= 0 or window_seconds > self._MAX_WINDOW_SEC:
            raise ValueError("window_seconds fora de intervalo aceitável (0, 30]")
        self.event_bus = event_bus
        self.publish_event = publish_event
        self.window_sec = window_seconds
        # Buffer de (timestamp, vector)
        self._buffer: deque[tuple[float, np.ndarray]] = deque()
        # Registrar handlers
        for topic in (NEWS_STREAM, AUDIO_STREAM, VIDEO_STREAM, IMAGE_STREAM):
            self.event_bus.subscribe(topic, self._on_snapshot)
        logger.info(
            "CoherenceMonitor inscrito em %s",
            [NEWS_STREAM, AUDIO_STREAM, VIDEO_STREAM, IMAGE_STREAM],
        )

    # ------------------------------------------------------------------

    def _on_snapshot(self, payload: Dict[str, Any]) -> None:
        """Recebe payloads dos encoders multimodais."""
        vec = payload.get("vector")
        if vec is None:
            return
        try:
            vec = np.asarray(vec, dtype=float)
        except Exception as exc:  # pragma: no cover – dados malformados
            logger.warning("CoherenceMonitor: snapshot inválido %s", exc)
            return
        now = time.time()
        self._buffer.append((now, vec))
        self._evict_old(now)
        self._compute_and_publish()

    def _evict_old(self, now: float) -> None:
        while self._buffer and (now - self._buffer[0][0]) > self.window_sec:
            self._buffer.popleft()

    def _compute_and_publish(self) -> None:
        if len(self._buffer) < 2:
            return
        vectors = [v for _, v in self._buffer]
        coherence = cross_modal_coherence(vectors)
        global LATEST_COHERENCE
        LATEST_COHERENCE = coherence
        self.event_bus.publish(
            self.publish_event,
            CrossModalCoherenceEvent(coherence=coherence),
        )
        logger.debug("CoherenceMonitor publicou coerência %.3f", coherence)
