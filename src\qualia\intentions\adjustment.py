r"""Interfaces para ajustes din\303\242micos do :class:`IntentionEnvelope`."""
from __future__ import annotations

from typing import Protocol

from .envelope import IntentionEnvelope


class EnvelopeAdjustmentProvider(Protocol):
    """Provedor de ajustes para :class:`IntentionEnvelope`."""

    async def adjust_envelope(self, envelope: IntentionEnvelope) -> IntentionEnvelope:
        """Retorna um envelope possivelmente ajustado."""
        ...
