#!/usr/bin/env python3
"""
QUALIA Execution Interface

Interface de execução simplificada que atua apenas como "braço" executor
das decisões do QAST Oracle. Remove toda lógica de decisão duplicada,
mantendo apenas:

- Execução de trades (paper/live)
- Gerenciamento de posições
- Monitoramento de performance
- Interface com exchanges
- Logging e métricas

A consciência e decisão ficam centralizadas no QASTOracleDecisionEngine.
"""

from __future__ import annotations

import asyncio
import time
from typing import Dict, List, Any, Optional, TYPE_CHECKING
from dataclasses import dataclass
from datetime import datetime, timezone
import uuid

if TYPE_CHECKING:
    from .qast_oracle_decision_engine import QASTOracleDecisionEngine, OracleDecision
from ..market.base_integration import CryptoDataFetcher
from ..trader.execution_engine import open_position, execute_close_order
from ..utils.logger import get_logger
from ..strategies.adaptive_liquidity_manager import AdaptiveLiquidityManager

logger = get_logger(__name__)


@dataclass
class ExecutionPosition:
    """Posição gerenciada pela interface de execução."""

    order_id: str
    symbol: str
    side: str  # 'buy' ou 'sell'
    size: float
    entry_price: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    timestamp: float
    oracle_decision: OracleDecision
    current_pnl: float = 0.0
    unrealized_pnl: float = 0.0


@dataclass
class ExecutionMetrics:
    """Métricas de execução."""

    total_trades: int
    winning_trades: int
    losing_trades: int
    total_pnl: float
    total_pnl_pct: float
    max_drawdown: float
    current_drawdown: float
    win_rate: float
    average_win: float
    average_loss: float
    sharpe_ratio: float
    total_fees: float


class QUALIAExecutionInterface:
    """
    Interface de execução pura para QUALIA.

    Funciona como "braço" executor das decisões do oráculo QAST:
    - Recebe decisões do oráculo
    - Executa trades (paper/live)
    - Gerencia posições
    - Monitora performance
    - Não toma decisões próprias
    """

    def __init__(
        self,
        oracle_engine: "QASTOracleDecisionEngine",
        mode: str = "paper_trading",  # "paper_trading" ou "live"
        exchange_config: Optional[Dict[str, Any]] = None,
        capital: float = 10000.0,
        execution_interval: float = 1.0,
        liquidity_manager: Optional[AdaptiveLiquidityManager] = None,
    ):
        self.oracle_engine = oracle_engine
        self.mode = mode
        self.exchange_config = exchange_config or {}
        self.initial_capital = capital
        self.execution_interval = execution_interval
        self.liquidity_manager = liquidity_manager

        # Estado de execução
        self.running = False
        self.current_capital = capital
        self.available_cash = capital

        # Posições abertas
        self.open_positions: Dict[str, ExecutionPosition] = {}

        # Histórico
        self.trade_history: List[Dict[str, Any]] = []
        self.execution_history: List[Dict[str, Any]] = []

        # Exchange client
        self.exchange_client: Optional[CryptoDataFetcher] = None

        # Métricas
        self.metrics = ExecutionMetrics(
            total_trades=0,
            winning_trades=0,
            losing_trades=0,
            total_pnl=0.0,
            total_pnl_pct=0.0,
            max_drawdown=0.0,
            current_drawdown=0.0,
            win_rate=0.0,
            average_win=0.0,
            average_loss=0.0,
            sharpe_ratio=0.0,
            total_fees=0.0,
        )

        # Controle de execução
        self.execution_task: Optional[asyncio.Task] = None

        # Pending decisions queue
        self.pending_decisions: List[OracleDecision] = []

        # Último erro ocorrido durante a execução
        self.last_error: Optional[str] = None

        logger.info(f"🔧 QUALIA Execution Interface inicializada (modo: {mode})")

    async def initialize(self):
        """Inicializa interface de execução."""

        logger.info("🔧 Inicializando interface de execução...")

        try:
            # Inicializa exchange client se necessário
            if self.mode == "live" and self.exchange_config:
                self.exchange_client = CryptoDataFetcher(
                    api_key=self.exchange_config.get("api_key", ""),
                    api_secret=self.exchange_config.get("api_secret", ""),
                    exchange_id=self.exchange_config.get("exchange_id", "kucoin"),
                )
                await self.exchange_client.initialize_connection()
                logger.info("✅ Exchange client conectado")

            logger.info("✅ Interface de execução inicializada")

        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ Erro inicializando interface de execução: {e}")
            raise

    async def start_execution_loop(self):
        """Inicia loop de execução contínua."""

        if self.running:
            logger.warning("Loop de execução já está rodando")
            return

        self.running = True
        logger.info("🚀 Iniciando loop de execução QUALIA...")

        try:
            self.execution_task = asyncio.create_task(self._execution_loop())
            await self.execution_task

        except asyncio.CancelledError:
            logger.info("Loop de execução cancelado")
        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ Erro no loop de execução: {e}")
        finally:
            self.running = False

    async def _execution_loop(self):
        """Loop principal de execução."""

        while self.running:
            try:
                # 1. Consulta oráculo para decisões
                logger.debug("🧠 Consultando oráculo QAST...")
                oracle_decisions = await self.oracle_engine.consult_oracle()

                # 2. Executa decisões
                for decision in oracle_decisions:
                    await self._execute_oracle_decision(decision)

                # 3. Monitora posições existentes
                await self._monitor_positions()

                # 4. Atualiza métricas
                self._update_metrics()

                # 5. Log de status
                if len(oracle_decisions) > 0:
                    logger.info(
                        f"📊 Executadas {len(oracle_decisions)} decisões. "
                        f"Posições abertas: {len(self.open_positions)}, "
                        f"Capital: ${self.current_capital:.2f}, "
                        f"PnL: ${self.metrics.total_pnl:.2f}"
                    )

                # Aguarda próximo ciclo
                await asyncio.sleep(self.execution_interval)

            except Exception as e:
                self.last_error = str(e)
                logger.error(f"❌ Erro no ciclo de execução: {e}")
                await asyncio.sleep(self.execution_interval)

    async def _execute_oracle_decision(self, decision: OracleDecision):
        """Executa decisão específica do oráculo."""

        try:
            symbol = decision.symbol
            action = decision.action

            logger.info(
                f"🎯 Executando decisão do oráculo: {action} {symbol} "
                f"(conf: {decision.confidence:.2f}, size: {decision.size:.4f})"
            )

            if self.liquidity_manager:
                current_price = await self._get_current_price(symbol)
                adjusted = self.liquidity_manager.calculate_order_size(
                    self.available_cash,
                    current_price,
                )
                if adjusted < decision.size:
                    logger.debug(
                        "LiquidityManager adjusted size from %.6f to %.6f",
                        decision.size,
                        adjusted,
                    )
                    decision.size = adjusted

            # Registra tentativa de execução
            execution_record = {
                "timestamp": time.time(),
                "symbol": symbol,
                "action": action,
                "decision": decision,
                "status": "pending",
            }

            if action == "BUY":
                await self._execute_buy_order(decision, execution_record)
            elif action == "SELL":
                await self._execute_sell_order(decision, execution_record)
            elif action == "CLOSE":
                await self._execute_close_orders(decision, execution_record)
            elif action == "HOLD":
                execution_record["status"] = "hold"
                logger.debug(f"HOLD para {symbol} - nenhuma ação necessária")

            # Adiciona ao histórico
            self.execution_history.append(execution_record)
            if len(self.execution_history) > 1000:
                self.execution_history.pop(0)

        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ Erro executando decisão para {decision.symbol}: {e}")

    async def _execute_buy_order(
        self, decision: OracleDecision, execution_record: Dict[str, Any]
    ):
        """Executa ordem de compra."""

        symbol = decision.symbol
        size = decision.size

        if size <= 0:
            execution_record["status"] = "rejected"
            execution_record["reason"] = "Invalid size"
            logger.warning(
                f"Ordem BUY rejeitada para {symbol}: tamanho inválido {size}"
            )
            return

        if self.available_cash < size * 100:  # Estimativa conservadora
            execution_record["status"] = "rejected"
            execution_record["reason"] = "Insufficient funds"
            logger.warning(f"Ordem BUY rejeitada para {symbol}: fundos insuficientes")
            return

        try:
            # Gera ID da ordem
            order_id = str(uuid.uuid4())

            # Executa ordem (simulada ou real)
            if self.mode == "paper_trading":
                execution_price = await self._get_current_price(symbol)
                success = True
            else:
                # Execução real via exchange
                execution_price, success = await self._place_live_buy_order(
                    symbol, size, decision.stop_loss
                )

            if success:
                # Cria posição
                position = ExecutionPosition(
                    order_id=order_id,
                    symbol=symbol,
                    side="buy",
                    size=size,
                    entry_price=execution_price,
                    stop_loss=decision.stop_loss,
                    take_profit=decision.take_profit,
                    timestamp=time.time(),
                    oracle_decision=decision,
                )

                self.open_positions[order_id] = position

                # Atualiza capital
                cost = size * execution_price
                self.available_cash -= cost

                # Registra trade
                trade_record = {
                    "order_id": order_id,
                    "symbol": symbol,
                    "side": "buy",
                    "size": size,
                    "entry_price": execution_price,
                    "timestamp": time.time(),
                    "oracle_confidence": decision.confidence,
                    "reasoning": decision.reasoning,
                }
                self.trade_history.append(trade_record)

                execution_record["status"] = "executed"
                execution_record["order_id"] = order_id
                execution_record["execution_price"] = execution_price

                logger.info(
                    f"✅ BUY executado: {size:.4f} {symbol} @ ${execution_price:.4f}"
                )

            else:
                execution_record["status"] = "failed"
                execution_record["reason"] = "Execution failed"
                logger.error(f"❌ Falha executando BUY para {symbol}")

        except Exception as e:
            execution_record["status"] = "error"
            execution_record["error"] = str(e)
            self.last_error = str(e)
            logger.error(f"❌ Erro executando BUY para {symbol}: {e}")

    async def _execute_sell_order(
        self, decision: OracleDecision, execution_record: Dict[str, Any]
    ):
        """Executa ordem de venda."""

        # Implementação similar ao BUY, mas para venda
        # Por simplicidade, implementando apenas fechamento de posições BUY existentes

        symbol = decision.symbol

        # Encontra posições BUY abertas para o símbolo
        buy_positions = [
            pos
            for pos in self.open_positions.values()
            if pos.symbol == symbol and pos.side == "buy"
        ]

        if not buy_positions:
            execution_record["status"] = "no_positions"
            execution_record["reason"] = "No BUY positions to close"
            logger.debug(f"Nenhuma posição BUY aberta para {symbol}")
            return

        # Fecha primeira posição encontrada
        position = buy_positions[0]
        await self._close_position(position, execution_record, reason="oracle_sell")

    async def _execute_close_orders(
        self, decision: OracleDecision, execution_record: Dict[str, Any]
    ):
        """Executa fechamento de todas as posições do símbolo."""

        symbol = decision.symbol

        # Encontra todas as posições abertas para o símbolo
        symbol_positions = [
            pos for pos in self.open_positions.values() if pos.symbol == symbol
        ]

        if not symbol_positions:
            execution_record["status"] = "no_positions"
            execution_record["reason"] = "No positions to close"
            logger.debug(f"Nenhuma posição aberta para {symbol}")
            return

        # Fecha todas as posições
        closed_count = 0
        for position in symbol_positions:
            try:
                await self._close_position(
                    position, execution_record, reason="oracle_close"
                )
                closed_count += 1
            except Exception as e:
                self.last_error = str(e)
                logger.error(f"Erro fechando posição {position.order_id}: {e}")

        execution_record["status"] = "executed"
        execution_record["closed_positions"] = closed_count
        logger.info(f"✅ Fechadas {closed_count} posições para {symbol}")

    async def _close_position(
        self,
        position: ExecutionPosition,
        execution_record: Dict[str, Any],
        reason: str = "manual",
    ):
        """Fecha posição específica."""

        try:
            # Obtém preço atual
            current_price = await self._get_current_price(position.symbol)

            # Executa fechamento (simulado ou real)
            if self.mode == "paper_trading":
                close_price = current_price
                success = True
            else:
                # Fechamento real via exchange
                close_price, success = await self._place_live_close_order(position)

            if success:
                # Calcula PnL
                if position.side == "buy":
                    pnl = (close_price - position.entry_price) * position.size
                else:
                    pnl = (position.entry_price - close_price) * position.size

                # Atualiza capital
                if position.side == "buy":
                    self.available_cash += position.size * close_price
                else:
                    self.available_cash += position.size * position.entry_price + pnl

                self.current_capital += pnl

                # Remove posição
                del self.open_positions[position.order_id]

                # Atualiza histórico de trade
                for trade in self.trade_history:
                    if trade.get("order_id") == position.order_id:
                        trade.update(
                            {
                                "close_price": close_price,
                                "close_timestamp": time.time(),
                                "pnl": pnl,
                                "pnl_pct": (
                                    pnl / (position.entry_price * position.size)
                                )
                                * 100,
                                "close_reason": reason,
                                "status": "closed",
                            }
                        )
                        break

                logger.info(
                    f"✅ Posição fechada: {position.symbol} {position.side} "
                    f"${position.entry_price:.4f} → ${close_price:.4f} "
                    f"PnL: ${pnl:.2f}"
                )

            else:
                logger.error(f"❌ Falha fechando posição {position.order_id}")

        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ Erro fechando posição {position.order_id}: {e}")
            raise

    async def _monitor_positions(self):
        """Monitora posições abertas para stop-loss e take-profit."""

        for position in list(self.open_positions.values()):
            try:
                current_price = await self._get_current_price(position.symbol)

                # Atualiza PnL não realizado
                if position.side == "buy":
                    position.unrealized_pnl = (
                        current_price - position.entry_price
                    ) * position.size
                else:
                    position.unrealized_pnl = (
                        position.entry_price - current_price
                    ) * position.size

                # Verifica stop-loss
                should_close = False
                close_reason = ""

                if position.stop_loss:
                    if position.side == "buy" and current_price <= position.stop_loss:
                        should_close = True
                        close_reason = "stop_loss"
                    elif (
                        position.side == "sell" and current_price >= position.stop_loss
                    ):
                        should_close = True
                        close_reason = "stop_loss"

                # Verifica take-profit
                if position.take_profit and not should_close:
                    if position.side == "buy" and current_price >= position.take_profit:
                        should_close = True
                        close_reason = "take_profit"
                    elif (
                        position.side == "sell"
                        and current_price <= position.take_profit
                    ):
                        should_close = True
                        close_reason = "take_profit"

                # Fecha posição se necessário
                if should_close:
                    execution_record = {
                        "timestamp": time.time(),
                        "symbol": position.symbol,
                        "action": "CLOSE",
                        "status": "triggered",
                    }
                    await self._close_position(position, execution_record, close_reason)

            except Exception as e:
                self.last_error = str(e)
                logger.error(f"Erro monitorando posição {position.order_id}: {e}")

    async def _get_current_price(self, symbol: str) -> float:
        """Obtém preço atual do símbolo."""

        # Implementação simplificada - usa preço fixo para demo
        # Em produção, consultaria exchange real
        price_map = {
            "BTCUSDT": 107500.0,
            "ETHUSDT": 2435.0,
            "ADAUSDT": 1.25,
            "SOLUSDT": 235.0,
        }

        return price_map.get(symbol, 100.0)

    async def _place_live_buy_order(
        self, symbol: str, size: float, stop_loss: Optional[float]
    ) -> Tuple[float, bool]:
        """Coloca ordem de compra real na exchange."""

        # Placeholder para implementação real
        # Retorna preço de execução e sucesso
        current_price = await self._get_current_price(symbol)
        return current_price, True

    async def _place_live_close_order(
        self, position: ExecutionPosition
    ) -> Tuple[float, bool]:
        """Coloca ordem de fechamento real na exchange."""

        # Placeholder para implementação real
        current_price = await self._get_current_price(position.symbol)
        return current_price, True

    def _update_metrics(self):
        """Atualiza métricas de performance."""

        # Calcula métricas dos trades fechados
        closed_trades = [t for t in self.trade_history if "pnl" in t]

        if not closed_trades:
            return

        # Métricas básicas
        self.metrics.total_trades = len(closed_trades)
        self.metrics.winning_trades = len([t for t in closed_trades if t["pnl"] > 0])
        self.metrics.losing_trades = len([t for t in closed_trades if t["pnl"] < 0])

        # PnL
        self.metrics.total_pnl = sum(t["pnl"] for t in closed_trades)
        self.metrics.total_pnl_pct = (
            self.metrics.total_pnl / self.initial_capital
        ) * 100

        # Win rate
        self.metrics.win_rate = (
            self.metrics.winning_trades / self.metrics.total_trades
        ) * 100

        # Médias
        winning_trades = [t for t in closed_trades if t["pnl"] > 0]
        losing_trades = [t for t in closed_trades if t["pnl"] < 0]

        self.metrics.average_win = (
            sum(t["pnl"] for t in winning_trades) / len(winning_trades)
            if winning_trades
            else 0.0
        )

        self.metrics.average_loss = (
            sum(t["pnl"] for t in losing_trades) / len(losing_trades)
            if losing_trades
            else 0.0
        )

        # Drawdown
        running_pnl = 0.0
        peak = 0.0
        max_dd = 0.0

        for trade in closed_trades:
            running_pnl += trade["pnl"]
            if running_pnl > peak:
                peak = running_pnl
            drawdown = peak - running_pnl
            if drawdown > max_dd:
                max_dd = drawdown

        self.metrics.max_drawdown = max_dd
        self.metrics.current_drawdown = peak - running_pnl

    async def stop_execution_loop(self):
        """Para loop de execução."""

        logger.info("🛑 Parando loop de execução...")

        self.running = False

        if self.execution_task:
            self.execution_task.cancel()
            try:
                await self.execution_task
            except asyncio.CancelledError:
                pass

        logger.info("✅ Loop de execução parado")

    async def shutdown(self):
        """Encerra interface de execução."""

        logger.info("🔧 Encerrando interface de execução...")

        try:
            # Para loop de execução
            await self.stop_execution_loop()

            # Fecha posições abertas
            for position in list(self.open_positions.values()):
                execution_record = {
                    "timestamp": time.time(),
                    "symbol": position.symbol,
                    "action": "CLOSE",
                    "status": "shutdown",
                }
                await self._close_position(position, execution_record, "shutdown")

            # Fecha conexão exchange
            if self.exchange_client:
                await self.exchange_client.close()

            logger.info("✅ Interface de execução encerrada")

        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ Erro encerrando interface de execução: {e}")

    def get_execution_status(self) -> Dict[str, Any]:
        """
        Retorna status atual da interface de execução.

        Returns:
            Dict com informações de status
        """
        return {
            "pending_decisions": len(self.pending_decisions),
            "open_positions": len(self.open_positions),
            "current_capital": self.current_capital,
            "available_cash": self.available_cash,
            "total_pnl": self.metrics.total_pnl,
            "total_trades": self.metrics.total_trades,
            "win_rate": self.metrics.win_rate,
            "last_error": self.last_error,
        }

    async def process_decisions(self, decisions: List[OracleDecision]):
        """
        Processa decisões do oráculo adicionando-as à fila de execução.

        Args:
            decisions: Lista de decisões do oráculo para executar
        """
        try:
            logger.info(f"📥 Recebendo {len(decisions)} decisões para execução")

            # Adiciona decisões à fila
            self.pending_decisions.extend(decisions)

            # Log das decisões recebidas
            for i, decision in enumerate(decisions):
                logger.info(
                    f"   📥 Decisão {i+1}: {decision.symbol} {decision.action} "
                    f"conf={decision.confidence:.3f} size={decision.size:.4f}"
                )

            logger.info(f"✅ {len(decisions)} decisões adicionadas à fila de execução")

        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ Erro processando decisões: {e}")

    async def execute_pending_decisions(self):
        """
        Executa todas as decisões pendentes na fila.
        """
        try:
            if not self.pending_decisions:
                logger.debug("🔄 Nenhuma decisão pendente para executar")
                return

            pending_count = len(self.pending_decisions)
            logger.info(f"⚡ Executando {pending_count} decisões pendentes...")

            executed_count = 0
            failed_count = 0

            # Executa cada decisão pendente
            for decision in self.pending_decisions.copy():
                try:
                    await self._execute_oracle_decision(decision)
                    executed_count += 1
                except Exception as e:
                    logger.error(f"❌ Erro executando decisão {decision.symbol}: {e}")
                    failed_count += 1

            # Limpa fila de decisões pendentes
            self.pending_decisions.clear()

            logger.info(
                f"✅ Execução completa: {executed_count} executadas, "
                f"{failed_count} falharam"
            )

            # Atualiza métricas
            self._update_metrics()

        except Exception as e:
            self.last_error = str(e)
            logger.error(f"❌ Erro executando decisões pendentes: {e}")

    def get_status(self) -> dict:
        """Retorna o status atual da interface de execução.

        Returns
        -------
        dict
            Dicionário com informações resumidas de execução.
        """

        status = {
            "mode": self.mode,
            "running": self.running,
            "initial_capital": self.initial_capital,
            "pending_decisions": len(self.pending_decisions),
            "active_positions": len(self.open_positions),
            "open_positions": len(self.open_positions),
            "last_error": self.last_error,
        }

        status.update(
            {
                "current_capital": self.current_capital,
                "available_cash": self.available_cash,
                "total_pnl": self.metrics.total_pnl,
                "total_pnl_pct": self.metrics.total_pnl_pct,
                "win_rate": self.metrics.win_rate,
                "current_drawdown": self.metrics.current_drawdown,
                "executed_decisions": self.metrics.total_trades,
            }
        )

        return status
