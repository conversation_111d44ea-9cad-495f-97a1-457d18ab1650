"""
Estratégia de Momentum Quântico Aprimorada

Esta estratégia combina análise técnica clássica com métricas quânticas avançadas
para gerar sinais de trading mais precisos e robustos.
"""

import logging
from ...utils.logger import get_logger
import numpy as np
import pandas as pd
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
from ...config.config_manager import ConfigManager
from numpy.typing import ArrayLike

from ...strategies.metrics_adapter import QuantumMetricsAdapter
from ...strategies.strategy_interface import (
    TradingStrategy,
    register_strategy,
    TradingContext,
)
from .indicators import (
    calculate_rsi,
    calculate_ema,
    calculate_macd,
    calculate_macd_from_emas,
)
from .risk import risk_management as apply_risk_management
from .signal_generation import generate_signals as eqm_generate_signals
from .sizing import position_sizing as eqm_position_sizing

logger = get_logger(__name__)


@register_strategy(
    name="EnhancedQuantumMomentumStrategy",
    category="quantum_enhanced",
    description="Estratégia de momentum quântico com filtros de volatilidade e adaptação de risco",
    version="2.0.0",
    tags=["quantum", "momentum", "adaptive"],
    legacy=True,
)
class EnhancedQuantumMomentumStrategy(TradingStrategy):
    # Garantir que o nome seja registrado corretamente com um alias mais
    # simples
    strategy_alias = "quantum_momentum_v2"
    """
    Versão aprimorada da estratégia de momentum quântico com:

    1. Filtros de volatilidade adaptativos
    2. Ajuste dinâmico de risk/reward baseado em métricas quânticas
    3. Filtros de tendência de longo prazo
    4. Detecção de divergências entre indicadores clássicos e quânticos
    5. Gestão de risco baseada em métricas de mercado em tempo real
    """

    def __init__(
        self,
        rsi_period: int = 14,
        rsi_overbought: float = 75.0,  # Mais restritivo
        rsi_oversold: float = 25.0,  # Mais restritivo
        ema_short: int = 8,  # Mais rápido
        ema_long: int = 21,  # Médio prazo
        trend_ema: int = 50,  # Confirmar tendência longo prazo
        qubits: int = 8,  # Mais qubits para maior precisão
        min_volatility: Any = 0.015,  # Pode ser float ou lista
        max_volatility: float = 0.05,  # Evita mercados excessivamente voláteis
        signal_threshold: float = 0.72,  # Mais restritivo
        quantum_weight: float = 0.55,  # Maior peso para componente quântico
        adaptive_risk: bool = True,  # Ajuste de risco dinâmico
        divergence_filter: bool = True,  # Detectar divergências
        data_lookback_period: int = 200,  # Período de lookback para dados históricos
        # Dias máximos para manter uma posição
        max_holding_period: Optional[int] = None,
        # Universo de ativos permitido
        asset_universe: Optional[List[str]] = None,
        take_profit_r_multiple: Any = 2.0,  # Pode ser float ou lista
        stop_loss_r_multiple: Any = 1.0,  # Pode ser float ou lista
        log_level: str = "INFO",
        # Novos parâmetros para passar contexto à classe base
        symbol: Optional[str] = None,
        timeframe: Optional[str] = None,
        config_manager: Optional[ConfigManager] = None,
    ):
        """
        Inicializa a estratégia com os parâmetros especificados.
        Os parâmetros min_volatility, take_profit_r_multiple e stop_loss_r_multiple podem ser float ou lista de floats (grid).
        Se fornecido como escalar, será convertido para lista internamente.
        O primeiro valor da lista será usado como padrão para execução normal.

        Args:
            rsi_period: Período para cálculo do RSI
            rsi_overbought: Limiar de sobrecompra no RSI
            rsi_oversold: Limiar de sobrevenda no RSI
            ema_short: Período para média móvel exponencial curta
            ema_long: Período para média móvel exponencial longa
            trend_ema: EMA de longo prazo para confirmar tendência
            qubits: Número de qubits para cálculos quânticos
            min_volatility: Volatilidade mínima para considerar sinais (float ou lista)
            max_volatility: Volatilidade máxima permitida
            signal_threshold: Limiar de confiança para sinais quânticos
            quantum_weight: Peso das métricas quânticas vs. clássicas
            adaptive_risk: Usar ajuste de risco adaptativo
            divergence_filter: Filtrar com base em divergências
            data_lookback_period: Período de lookback para dados históricos
            max_holding_period: Dias máximos para manter uma posição
            asset_universe: Universo de ativos permitido
            take_profit_r_multiple: Multiplicador de take profit em R (float ou lista)
            stop_loss_r_multiple: Multiplicador de stop loss em R (float ou lista)
            log_level: Nível de log. Valores permitidos: ``"DEBUG"``, ``"INFO"``,
                ``"WARNING"``, ``"ERROR"`` e ``"CRITICAL"``
            symbol: Símbolo do ativo
            timeframe: Faixa de tempo do ativo
        """
        # Inicializar a classe base com contexto incluindo symbol/timeframe
        super().__init__(
            context={"symbol": symbol, "timeframe": timeframe},
            config_manager=config_manager,
        )

        # Nome utilizado em logs e identificação
        self.name = "EnhancedQuantumMomentumStrategy"

        # Manter referência direta a symbol/timeframe para uso interno
        self.symbol = symbol
        self.timeframe = timeframe

        # Converter para lista se necessário
        self.min_volatility = (
            min_volatility if isinstance(min_volatility, list) else [min_volatility]
        )
        self.take_profit_r_multiple = (
            take_profit_r_multiple
            if isinstance(take_profit_r_multiple, list)
            else [take_profit_r_multiple]
        )
        self.stop_loss_r_multiple = (
            stop_loss_r_multiple
            if isinstance(stop_loss_r_multiple, list)
            else [stop_loss_r_multiple]
        )

        # Usar o primeiro valor como padrão para lógica existente
        self.rsi_period = rsi_period
        self.rsi_overbought = rsi_overbought
        self.rsi_oversold = rsi_oversold
        self.ema_short = ema_short
        self.ema_long = ema_long
        self.macd_signal = 9
        self.qubits = qubits
        self.signal_threshold = signal_threshold
        self.quantum_weight = quantum_weight
        self.trend_ema = trend_ema
        self.max_volatility = max_volatility
        self.adaptive_risk = adaptive_risk
        self.divergence_filter = divergence_filter
        self.data_lookback_period = data_lookback_period
        self.max_holding_period = max_holding_period
        self.asset_universe = asset_universe if asset_universe else []

        # Parâmetros de grid (usar o primeiro valor como padrão)
        self._min_volatility_value = self.min_volatility[0]
        self._take_profit_r_multiple_value = self.take_profit_r_multiple[0]
        self._stop_loss_r_multiple_value = self.stop_loss_r_multiple[0]

        _allowed_levels = {
            "DEBUG": logging.DEBUG,
            "INFO": logging.INFO,
            "WARNING": logging.WARNING,
            "ERROR": logging.ERROR,
            "CRITICAL": logging.CRITICAL,
        }

        level_name = log_level.upper()
        if level_name not in _allowed_levels:
            raise ValueError(
                f"Nível de log inválido: {log_level}. Opções válidas: "
                f"{', '.join(_allowed_levels)}"
            )

        logger.setLevel(_allowed_levels[level_name])

        # Adaptador para métricas quânticas
        q_weight = (
            quantum_weight[0] if isinstance(quantum_weight, list) else quantum_weight
        )
        self.quantum_metrics_adapter = QuantumMetricsAdapter(
            qubits=qubits, quantum_weight=q_weight
        )

        # Controle de estado
        self.initialized = False

        # Histórico para análise de tendências
        self.historical_signals: List[Dict[str, Any]] = []
        self.market_regime = (
            "neutral"  # neutro, tendência de alta ou tendência de baixa
        )

        # Métricas de desempenho
        self.performance_metrics = {
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0,
            "win_rate": 0.0,
            # Lucro médio por trade (considerando ganhos e perdas)
            "avg_profit_per_trade": 0.0,
            "avg_win": 0.0,  # Lucro médio apenas nos trades vencedores
            "avg_loss": 0.0,  # Perda média apenas nos trades perdedores
            "profit_factor": 0.0,  # Gross profit / Gross loss
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,  # Necessita de risk-free rate e retornos periódicos
            # Necessita de risk-free rate e retornos periódicos (downside
            # deviation)
            "sortino_ratio": 0.0,
            "profitable_pairs": set(),
        }
        logger.debug(
            f"EnhancedQuantumMomentumStrategy instanciada com alias: {self.strategy_alias} para {self.symbol or 'N/A'}@{self.timeframe or 'N/A'}"
        )

    def initialize(self, context: Optional[Dict[str, Any]] = None) -> None:
        """
        Inicializa a estratégia com recursos e configurações do contexto.
        O contexto pode incluir: capital_base, broker_interface, etc.

        Args:
            context: Dicionário com parâmetros e recursos
        """
        logger.info(f"Inicializando estratégia {self.strategy_alias}...")

        if context:
            # Atualizar parâmetros se fornecidos no contexto
            for param_name, value in context.items():
                if hasattr(self, param_name):
                    setattr(self, param_name, value)
                    logger.debug(
                        f"Parâmetro '{param_name}' atualizado para '{value}' via contexto."
                    )

            # Exemplo: Configurar interface do broker se fornecida
            # self.broker = context.get('broker_interface')
            # self.capital_base = context.get('capital_base', 100000) # Default
            # capital

        # Reinicializar adaptador com parâmetros potencialmente atualizados
        q_weight = (
            self.quantum_weight[0]
            if isinstance(self.quantum_weight, list)
            else self.quantum_weight
        )
        self.quantum_metrics_adapter = QuantumMetricsAdapter(
            qubits=self.qubits, quantum_weight=q_weight
        )

        self.initialized = True
        logger.info(f"Estratégia {self.strategy_alias} inicializada com sucesso.")
        logger.debug(f"Parâmetros finais: {self._get_parameters()}")

    def analyze_market(
        self,
        market_data: pd.DataFrame,
        common_indicators: Optional[Dict[str, Any]] = None,
        trading_context: TradingContext | None = None,
        *,
        context: TradingContext | None = None,
    ) -> Dict[str, Any]:
        """
        Analisa os dados de mercado com indicadores clássicos e quânticos aprimorados.

        Args:
            market_data: DataFrame com dados OHLCV
            common_indicators: Indicadores comuns pré-calculados (ema_short, ema_long, rsi)

        Returns:
            Dicionário com resultados da análise
        """
        trading_context = trading_context or context
        if not self.initialized:
            raise ValueError(
                "Estratégia não inicializada. Chame initialize() primeiro."
            )

        # Verificar dados mínimos
        if len(market_data) < 100:
            logger.warning(
                f"Dados insuficientes para análise: {len(market_data)} registros (min 100)"
            )
            return {}

        # Dados básicos
        closes = market_data["close"].to_numpy()
        highs = market_data["high"].to_numpy()
        lows = market_data["low"].to_numpy()
        volumes = market_data["volume"].to_numpy() if "volume" in market_data else None
        last_price = closes[-1]
        timestamp = market_data.index[-1]

        # 1. Calcular indicadores clássicos
        # Usar indicadores comuns se fornecidos, senão calcular
        rsi_val = (
            common_indicators.get("rsi")
            if common_indicators and "rsi" in common_indicators
            else self._calculate_rsi(closes, self.rsi_period)
        )
        ema_short_val = (
            common_indicators.get("ema_short")
            if common_indicators and "ema_short" in common_indicators
            else self._calculate_ema(closes, self.ema_short)
        )
        ema_long_val = (
            common_indicators.get("ema_long")
            if common_indicators and "ema_long" in common_indicators
            else self._calculate_ema(closes, self.ema_long)
        )
        # Tendência EMA não é comum, calcula sempre
        trend_ema_val = self._calculate_ema(closes, self.trend_ema)

        # MACD depende das EMAs, recalcula se EMAs não foram fornecidas
        if not (
            common_indicators
            and "ema_short" in common_indicators
            and "ema_long" in common_indicators
        ):
            macd_val, macd_signal_val, macd_hist_val = self._calculate_macd(
                closes, self.ema_short, self.ema_long
            )
        else:
            # Agora podemos reutilizar os valores de EMA já calculados
            macd_val, macd_signal_val, macd_hist_val = self._calculate_macd_from_emas(
                ema_short_val,
                ema_long_val,
                self.macd_signal,
            )

        # Calcular volatilidades
        vol_short = np.std(closes[-20:]) / np.mean(closes[-20:])
        vol_medium = np.std(closes[-50:]) / np.mean(closes[-50:])
        vol_long = np.std(closes[-100:]) / np.mean(closes[-100:])

        # Calcular métricas quânticas
        quantum_metrics = []

        # Momentum quântico
        q_momentum = self.quantum_metrics_adapter.calculate_quantum_momentum(
            closes[-50:], volumes[-50:] if volumes is not None else None, vol_short
        )

        # Regime de mercado
        q_regime = self.quantum_metrics_adapter.get_quantum_market_regime(
            closes[-50:], volumes[-50:] if volumes is not None else None
        )

        # Divergência quântica
        (
            has_divergence,
            div_strength,
        ) = self.quantum_metrics_adapter.calculate_quantum_divergence(
            closes[-30:], rsi_val[-30:], 0.5
        )

        quantum_metrics.append(
            {
                "momentum": q_momentum,
                "regime": q_regime,
                "divergence": has_divergence,
                "divergence_strength": div_strength,
                "entropy": q_regime.get("volatile", 0.0),
            }
        )

        # Detectar regime de mercado (tendência vs. lateralização)
        price_above_trend = closes[-1] > trend_ema_val[-1]
        price_below_trend = closes[-1] < trend_ema_val[-1]

        if price_above_trend and rsi_val[-1] > 50:
            market_regime = "uptrend"
        elif price_below_trend and rsi_val[-1] < 50:
            market_regime = "downtrend"
        else:
            market_regime = "range"

        # Calcular índice de força direcional (ADX-like) para medir força da
        # tendência
        directional_strength = self._calculate_directional_strength(market_data)

        # Obter entropia quântica para medir incerteza do mercado
        entropy = quantum_metrics[0].get("entropy", 0.5) if quantum_metrics else 0.5

        # Construir resultado de análise
        analysis_result = {
            "rsi": pd.Series(rsi_val, index=market_data.index[-len(rsi_val) :]),
            "ema_short": pd.Series(
                ema_short_val, index=market_data.index[-len(ema_short_val) :]
            ),
            "ema_long": pd.Series(
                ema_long_val, index=market_data.index[-len(ema_long_val) :]
            ),
            "trend_ema": pd.Series(
                trend_ema_val, index=market_data.index[-len(trend_ema_val) :]
            ),
            "macd": pd.Series(macd_val, index=market_data.index[-len(macd_val) :]),
            "macd_signal": pd.Series(
                macd_signal_val, index=market_data.index[-len(macd_signal_val) :]
            ),
            "macd_histogram": pd.Series(
                macd_hist_val, index=market_data.index[-len(macd_hist_val) :]
            ),
            "vol_short": vol_short,
            "vol_medium": vol_medium,
            "vol_long": vol_long,
            "market_regime": market_regime,
            "directional_strength": directional_strength,
            "quantum_entropy": entropy,
            "price_above_trend": price_above_trend,
            "price_below_trend": price_below_trend,
            "quantum_metrics": quantum_metrics,
            "last_price": last_price,
            "timestamp": timestamp,
        }

        return analysis_result

    def generate_signals(self, analysis_result: Dict[str, Any]) -> pd.DataFrame:
        """Delegates to :func:`generate_signals` submodule."""
        return eqm_generate_signals(self, analysis_result)

    def risk_management(
        self, signals: pd.DataFrame, market_data: pd.DataFrame
    ) -> pd.DataFrame:
        """Delegate to :func:`apply_risk_management`."""
        return apply_risk_management(self, signals, market_data)

    def _get_parameters(self) -> Dict[str, Any]:
        """
        Retorna todos os parâmetros configuráveis da estratégia.

        Returns:
            Dicionário com parâmetros e seus valores atuais
        """
        # Obter parâmetros da estratégia
        params = {
            "rsi_period": self.rsi_period,
            "rsi_overbought": self.rsi_overbought,
            "rsi_oversold": self.rsi_oversold,
            "ema_short": self.ema_short,
            "ema_long": self.ema_long,
            "trend_ema": self.trend_ema,
            "qubits": self.qubits,
            "min_volatility": self.min_volatility,
            "max_volatility": self.max_volatility,
            "signal_threshold": self.signal_threshold,
            "quantum_weight": self.quantum_weight,
            "adaptive_risk": self.adaptive_risk,
            "divergence_filter": self.divergence_filter,
            "data_lookback_period": self.data_lookback_period,
            "max_holding_period": self.max_holding_period,
            "asset_universe": self.asset_universe,
            "take_profit_r_multiple": self.take_profit_r_multiple,
            "stop_loss_r_multiple": self.stop_loss_r_multiple,
            "log_level": logging.getLevelName(logger.getEffectiveLevel()),
            "symbol": self.symbol,
            "timeframe": self.timeframe,
        }

        return params

    def get_parameters(self) -> Dict[str, Any]:
        """
        Compatibilidade: retorna os parâmetros da estratégia para controle QAST.
        """
        return self._get_parameters()

    def get_params(self) -> Dict[str, Any]:
        """Alias para compatibilidade com :class:`TradingStrategy`."""

        return self.get_parameters()

    def set_params(self, params: Dict[str, Any]) -> None:
        """Alias compatível para ``set_parameters``."""

        self.set_parameters(params)

    def _calculate_directional_strength(self, market_data: pd.DataFrame) -> float:
        """
        Calcula um índice de força direcional para medir tendência.

        Args:
            market_data: DataFrame com dados OHLCV

        Returns:
            Valor entre 0 e 1 indicando força da tendência
        """
        close = market_data["close"].to_numpy()

        # Se não houver dados suficientes
        if len(close) < 50:
            return 0.0

        # Calcular média móvel de diferentes períodos
        ma_short = np.mean(close[-20:])
        ma_medium = np.mean(close[-50:])

        # Calcular inclinação da tendência
        last_20_slope = (close[-1] - close[-20]) / 20
        last_10_slope = (close[-1] - close[-10]) / 10

        # Determinar se as inclinações estão alinhadas (mesmo sinal)
        slopes_aligned = last_20_slope * last_10_slope > 0

        # Calcular diferença entre médias móveis
        ma_diff = abs(ma_short - ma_medium) / ma_medium

        # Calcular desvio padrão normalizado (volatilidade)
        stddev = np.std(close[-50:]) / np.mean(close[-50:])

        # Quanto maior a diferença entre médias e menor a volatilidade, mais
        # direcional é o mercado
        directional_score = ma_diff / (stddev + 0.001)  # Evitar divisão por zero

        # Normalizar entre 0 e 1
        directional_score = min(1.0, max(0.0, directional_score))

        # Penalizar se inclinações não estiverem alinhadas
        if not slopes_aligned:
            directional_score *= 0.7

        return directional_score

    def _calculate_classic_score(
        self,
        rsi: pd.Series,
        macd: pd.Series,
        macd_signal: pd.Series,
        macd_histogram: pd.Series,
    ) -> float:
        """
        Calcula uma pontuação baseada em indicadores técnicos clássicos.

        Args:
            rsi: Série com valores RSI
            macd: Série com linha MACD
            macd_signal: Série com linha de sinal MACD
            macd_histogram: Série com valores do histograma MACD

        Returns:
            Pontuação entre -1 (venda) e 1 (compra)
        """
        if len(rsi) < 2 or len(macd) < 2:
            return 0.0  # Dados insuficientes

        # Extrair valores mais recentes
        current_rsi = rsi.iloc[-1]
        prev_rsi = rsi.iloc[-2]

        current_macd = macd.iloc[-1]
        current_signal = macd_signal.iloc[-1]
        current_hist = macd_histogram.iloc[-1]
        prev_hist = macd_histogram.iloc[-2]

        # 1. Sinal RSI (0 a 100 -> -1 a 1)
        # Quanto mais próximo de sobrecompra (>70), mais negativo
        # Quanto mais próximo de sobrevenda (<30), mais positivo
        rsi_signal = 0.0

        if current_rsi > self.rsi_overbought:
            rsi_signal = -1.0 + (100 - current_rsi) / (100 - self.rsi_overbought)
        elif current_rsi < self.rsi_oversold:
            rsi_signal = 1.0 - (current_rsi / self.rsi_oversold)
        else:
            # Escala linear na região neutra
            rsi_neutral = (current_rsi - self.rsi_oversold) / (
                self.rsi_overbought - self.rsi_oversold
            )
            rsi_signal = 1.0 - 2 * rsi_neutral  # Mapear [0,1] para [1,-1]

        # 2. Direção RSI (momentum)
        rsi_direction = 0.2 * np.sign(current_rsi - prev_rsi)

        # 3. Sinal MACD
        macd_signal_value = 0.0

        # Cruzamento de MACD e sinal
        if current_macd > current_signal:
            macd_signal_value = 0.5  # Sinal positivo (compra)
        else:
            macd_signal_value = -0.5  # Sinal negativo (venda)

        # Amplificar com base na distância
        macd_divergence = abs(current_macd - current_signal)
        macd_signal_value *= min(1.5, 1.0 + macd_divergence * 10)  # Amplificar até 50%

        # 4. Momentum do histograma
        hist_direction = np.sign(current_hist - prev_hist)
        hist_momentum = (
            0.3 * hist_direction * min(1.0, abs(current_hist - prev_hist) * 20)
        )

        # Combinar sinais com pesos
        combined_signal = (
            rsi_signal * 0.4
            + rsi_direction * 0.1
            + macd_signal_value * 0.3
            + hist_momentum * 0.2
        )

        # Garantir que esteja no intervalo [-1, 1]
        return max(-1.0, min(1.0, combined_signal))

    def _calculate_quantum_score(self, quantum_metrics: List[Dict[str, Any]]) -> float:
        """
        Calcula uma pontuação aprimorada baseada em métricas quânticas.

        Args:
            quantum_metrics: Lista de dicionários com métricas quânticas

        Returns:
            Pontuação entre -1 e 1
        """
        # Se não houver métricas quânticas
        if not quantum_metrics or len(quantum_metrics) == 0:
            return 0.0

        # Obter métricas das últimas 3 janelas
        recent_metrics = quantum_metrics[:3]

        # Extrair valores relevantes
        entropies = [m.get("entropy", 0.5) for m in recent_metrics]
        coherences = [m.get("coherence", 0.0) for m in recent_metrics]
        eigenstates = [m.get("ground_state_probability", 0.5) for m in recent_metrics]

        # Calcular tendências nas métricas
        entropy_trend = (
            entropies[0] - np.mean(entropies[1:]) if len(entropies) > 1 else 0
        )
        coherence_trend = (
            coherences[0] - np.mean(coherences[1:]) if len(coherences) > 1 else 0
        )

        # Métricas mais recentes têm maior peso
        recent_entropy = entropies[0] if entropies else 0.5
        recent_coherence = coherences[0] if coherences else 0.0
        recent_eigenstate = eigenstates[0] if eigenstates else 0.5

        # Ajuste específico do eigenstate para detectar "curvatura do espaço de
        # fase"
        # Converter para escala -1 a 1
        eigenstate_signal = 2 * (recent_eigenstate - 0.5)

        # Tendência de entropia para baixo (mais ordem) é sinal de compra
        # Converter para escala -1 a 1
        entropy_signal = -2 * (recent_entropy - 0.5)

        # Aumento de coerência é sinal de alinhamento de mercado
        coherence_signal = coherence_trend * 5  # Amplificar tendência

        # Combinar sinais com pesos
        combined_signal = (
            eigenstate_signal * 0.5 + entropy_signal * 0.3 + coherence_signal * 0.2
        )

        # Limitar entre -1 e 1
        return max(-1.0, min(1.0, combined_signal))

    def _calculate_rsi(self, prices: ArrayLike, period: int) -> np.ndarray:
        """Wrapper around :func:`calculate_rsi`."""
        return calculate_rsi(prices, period)

    def _calculate_ema(self, prices: ArrayLike, period: int) -> np.ndarray:
        """Wrapper around :func:`calculate_ema`."""
        return calculate_ema(prices, period)

    def _calculate_macd(
        self,
        prices: ArrayLike,
        fast_period: int = 12,
        slow_period: int = 26,
        signal_period: int = 9,
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Wrapper around :func:`calculate_macd`."""
        return calculate_macd(prices, fast_period, slow_period, signal_period)

    def _calculate_macd_from_emas(
        self,
        fast_ema: ArrayLike,
        slow_ema: ArrayLike,
        signal_period: int = 9,
    ) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Wrapper around :func:`calculate_macd_from_emas`."""
        return calculate_macd_from_emas(fast_ema, slow_ema, signal_period)

    def _score_classic_indicators(self, analysis_result: Dict[str, Any]) -> float:
        """Avalia indicadores técnicos clássicos."""
        return self._calculate_classic_score(
            analysis_result["rsi"],
            analysis_result["macd"],
            analysis_result["macd_signal"],
            analysis_result["macd_histogram"],
        )

    def _score_quantum_metrics(self, analysis_result: Dict[str, Any]) -> float:
        """Avalia métricas quânticas do mercado."""
        return self._calculate_quantum_score(analysis_result["quantum_metrics"])

    def _apply_volatility_filters(self, analysis_result: Dict[str, Any]) -> bool:
        """Verifica se a volatilidade está dentro dos limites aceitáveis."""
        vol_short = analysis_result["vol_short"]
        if vol_short < self._min_volatility_value or vol_short > self.max_volatility:
            logger.debug(
                f"Volatilidade {vol_short:.4f} fora dos limites ({self._min_volatility_value} - {self.max_volatility})"
            )
            return False
        return True

    def position_sizing(
        self, signals: pd.DataFrame, capital: float, risk_per_trade: float
    ) -> pd.DataFrame:
        """Delegates to :func:`position_sizing` submodule."""
        return eqm_position_sizing(self, signals, capital, risk_per_trade)

    def set_parameters(self, parameters: Dict[str, Any]) -> None:
        """
        Define novos parâmetros para a estratégia EnhancedQuantumMomentumStrategy.

        Args:
            parameters: Dicionário contendo os novos parâmetros a serem aplicados.
        """
        for key, value in parameters.items():
            if hasattr(self, key):
                setattr(self, key, value)
        # Re-inicializar componentes dependentes, se necessário
        if "qubits" in parameters or "quantum_weight" in parameters:
            q_weight = (
                self.quantum_weight[0]
                if isinstance(self.quantum_weight, list)
                else self.quantum_weight
            )
            self.quantum_metrics_adapter = QuantumMetricsAdapter(
                qubits=self.qubits, quantum_weight=q_weight
            )
        logger.debug(
            f"EnhancedQuantumMomentumStrategy: Parâmetros atualizados via set_parameters: {list(parameters.keys())}"
        )
