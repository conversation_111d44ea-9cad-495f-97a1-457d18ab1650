"""Utility helpers for reducing portfolio exposure.

This module provides :func:`reduce_positions` to compute a step-by-step plan for
closing a percentage of an open portfolio. The helper is intentionally generic
so the calling code can specify how to retrieve the current value and quantity
for each position object.
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Any, Callable, Generic, List, Sequence, Tuple, TypeVar


T = TypeVar("T")


@dataclass
class PlanEntry(Generic[T]):
    """Entry describing how to reduce a single position."""

    position: T
    quantity_to_close: float
    price: float


def reduce_positions(
    positions: Sequence[T],
    reduction_pct: float,
    value_fn: Callable[[T], float],
    qty_fn: Callable[[T], float],
) -> Tuple[List[PlanEntry[T]], float]:
    """Compute a plan to reduce exposure across ``positions``.

    Args:
        positions: Iterable with the open positions to inspect.
        reduction_pct: Desired exposure reduction percentage ``0-100``.
        value_fn: Callable returning the current monetary value of a position.
        qty_fn: Callable returning the quantity currently held.

    Returns:
        A tuple ``(plan, total_value)`` where ``plan`` is an ordered list of
        :class:`PlanEntry` objects describing how much of each position should be
        closed and ``total_value`` is the aggregated portfolio value used when
        computing the plan.
    """

    if reduction_pct <= 0 or not positions:
        return [], 0.0

    sorted_positions = sorted(positions, key=value_fn, reverse=True)
    total_value = sum(value_fn(p) for p in sorted_positions)
    if total_value <= 0:
        return [], 0.0

    target_value = total_value * reduction_pct / 100.0
    planned_value = 0.0
    plan: List[PlanEntry[T]] = []

    for pos in sorted_positions:
        if planned_value >= target_value:
            break

        value = value_fn(pos)
        qty = qty_fn(pos)
        price = value / qty if qty else 0.0
        needed = target_value - planned_value

        if price == 0 or value <= needed:
            qty_to_close = qty
            planned_value += value
        else:
            qty_to_close = needed / price
            planned_value += needed

        plan.append(
            PlanEntry(position=pos, quantity_to_close=qty_to_close, price=price)
        )

    return plan, total_value
