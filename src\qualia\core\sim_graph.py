"""Maintain a directed graph of symbolic-token transitions.

This lightweight helper uses *networkx* when available; otherwise it falls back
on a minimal in-memory representation so that the rest of QUALIA does not crash
if the dependency is missing. The graph can be queried as a JSON-serialisable
``{"nodes": [...], "links": [...]}`` structure that the UI understands.

Nodes represent individual symbolic tokens (e.g. ``S1A3S0F0``).
Edges represent an observed transition *token_i → token_{i+1}* inside the same
OperatorSIM modulation batch.

Keeping this module isolated avoids polluting core logic with optional
visualisation concerns.
"""
from __future__ import annotations

from typing import Dict, List, Tuple

try:
    import networkx as nx  # type: ignore
except ModuleNotFoundError:  # pragma: no cover – optional dependency
    nx = None  # type: ignore

__all__ = [
    "add_path",
    "get_snapshot",
]

if nx is not None:
    _G = nx.DiGraph()
else:
    # Very small substitute: dict of dicts with weights
    _edges: Dict[Tuple[str, str], int] = {}


def add_path(tokens: List[str]) -> None:
    """Add a sequence of *tokens* as a directed path in the graph."""
    if len(tokens) < 2:
        return

    if nx is not None:
        nx.add_path(_G, tokens)  # type: ignore[attr-defined]
        # Increment (or set) weight attribute
        for u, v in zip(tokens, tokens[1:]):
            w = _G[u][v].get("weight", 0) + 1  # type: ignore[index]
            _G[u][v]["weight"] = w  # type: ignore[index]
    else:  # fallback structure
        for u, v in zip(tokens, tokens[1:]):
            _edges[(u, v)] = _edges.get((u, v), 0) + 1


def _build_snapshot() -> Dict[str, List[Dict]]:
    """Return the graph as a ``{nodes, links}`` JSON-ready dict."""
    if nx is not None:
        nodes = [{"id": n, "weight": _G.degree(n)} for n in _G.nodes]  # type: ignore[arg-type]
        links = [
            {"source": u, "target": v, "weight": d.get("weight", 1)}
            for u, v, d in _G.edges(data=True)
        ]
    else:
        node_set = {n for edge in _edges for n in edge}
        nodes = [{"id": n, "weight": 1} for n in node_set]
        links = [
            {"source": u, "target": v, "weight": w}
            for (u, v), w in _edges.items()
        ]
    return {"nodes": nodes, "links": links}


def get_snapshot() -> Dict[str, List[Dict]]:  # noqa: D401 simple
    """Return a copy of the current graph for external consumption."""
    return _build_snapshot().copy()
