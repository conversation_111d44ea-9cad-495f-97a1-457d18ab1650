from dataclasses import dataclass

from ..config.settings import get_env


@dataclass
class TemporalPatternConfig:
    """Configuration options for :class:`TemporalPatternDetector`."""

    wavelet_depth: int = int(get_env("QUALIA_WAVELET_DEPTH", "3", warn=False))
    use_quantum_transform: bool = (
        get_env("QUALIA_USE_QUANTUM_TRANSFORM", "False", warn=False).lower() == "true"
    )
    min_confidence: float = float(
        get_env("QUALIA_MIN_PATTERN_CONFIDENCE", "0.65", warn=False)
    )
    quantum_wavelet_shots: int = int(
        get_env("QUALIA_QWAVELET_SHOTS", "2048", warn=False)
    )
    qft_shots: int = int(get_env("QUALIA_QFT_SHOTS", "4096", warn=False))


__all__ = ["TemporalPatternConfig"]
