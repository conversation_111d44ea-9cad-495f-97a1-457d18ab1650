"""Fallback risk profile configuration used by the trading system.

This module defines :class:`RiskProfileSettings` and a dictionary with
predefined profiles applied when external JSON configurations are
missing or invalid.
"""

from dataclasses import dataclass
from typing import Dict, Optional


@dataclass
class RiskProfileSettings:
    """Container for risk profile parameters."""

    max_drawdown_pct: Optional[float] = None
    risk_per_trade_pct: Optional[float] = None
    max_position_size_pct: Optional[float] = None
    mass_based_size_pct: Optional[float] = None
    max_daily_loss_pct: Optional[float] = None
    max_open_positions: Optional[int] = None
    cooling_period_minutes: Optional[int] = None
    stop_loss_adjustment: Optional[float] = None
    min_lot_size: Optional[float] = None

    # Novos parâmetros para configuração avançada
    position_sizing_mode: Optional[str] = None
    max_position_percentage: Optional[float] = None
    stop_loss_percentage: Optional[float] = None
    take_profit_percentage: Optional[float] = None
    enable_trailing_stop: Optional[bool] = None
    enable_dynamic_position_sizing: Optional[bool] = None
    quantum_sensitivity_boost: Optional[float] = None


# Fallback risk profile parameters used when JSON configurations are missing.
_FALLBACK_RISK_PROFILE_SETTINGS: Dict[str, RiskProfileSettings] = {
    "conservative": RiskProfileSettings(
        max_drawdown_pct=5.0,
        risk_per_trade_pct=0.5,
        max_position_size_pct=3.0,
        max_daily_loss_pct=2.0,
        max_open_positions=3,
        cooling_period_minutes=60,
        stop_loss_adjustment=0.8,
        min_lot_size=0.0001,
        # Novos parâmetros obrigatórios
        position_sizing_mode="fixed_percentage",
        max_position_percentage=0.02,
        stop_loss_percentage=0.015,
        take_profit_percentage=0.025,
        enable_trailing_stop=False,
        enable_dynamic_position_sizing=False,
        quantum_sensitivity_boost=0.8,
    ),
    "moderate": RiskProfileSettings(
        max_drawdown_pct=12.0,
        risk_per_trade_pct=1.0,
        max_position_size_pct=15.0,
        max_daily_loss_pct=6.0,
        max_open_positions=2,
        cooling_period_minutes=30,
        stop_loss_adjustment=0.8,
        min_lot_size=0.0001,
        # Novos parâmetros obrigatórios
        position_sizing_mode="volatility_adjusted",
        max_position_percentage=0.05,
        stop_loss_percentage=0.025,
        take_profit_percentage=0.04,
        enable_trailing_stop=True,
        enable_dynamic_position_sizing=True,
        quantum_sensitivity_boost=1.2,
    ),
    "aggressive": RiskProfileSettings(
        max_drawdown_pct=6.0,
        risk_per_trade_pct=3.0,
        max_position_size_pct=25.0,
        max_daily_loss_pct=4.2,
        max_open_positions=6,
        cooling_period_minutes=21,
        stop_loss_adjustment=1.2,
        min_lot_size=0.0001,
        # Novos parâmetros obrigatórios
        position_sizing_mode="kelly_criterion",
        max_position_percentage=0.08,
        stop_loss_percentage=0.035,
        take_profit_percentage=0.06,
        enable_trailing_stop=True,
        enable_dynamic_position_sizing=True,
        quantum_sensitivity_boost=1.5,
    ),
    "custom_base": RiskProfileSettings(
        max_drawdown_pct=15.0,
        risk_per_trade_pct=0.7,
        max_position_size_pct=20.0,
        max_daily_loss_pct=5.0,
        max_open_positions=5,
        cooling_period_minutes=30,
        stop_loss_adjustment=0.8,
        min_lot_size=0.0001,
        # Novos parâmetros obrigatórios
        position_sizing_mode="volatility_adjusted",
        max_position_percentage=0.06,
        stop_loss_percentage=0.03,
        take_profit_percentage=0.05,
        enable_trailing_stop=True,
        enable_dynamic_position_sizing=True,
        quantum_sensitivity_boost=1.1,
    ),
    "balanced": RiskProfileSettings(
        max_drawdown_pct=10.0,
        risk_per_trade_pct=1.0,
        max_position_size_pct=8.0,
        max_daily_loss_pct=3.0,
        max_open_positions=5,
        cooling_period_minutes=30,
        stop_loss_adjustment=0.8,
        min_lot_size=0.0001,
        # Novos parâmetros obrigatórios
        position_sizing_mode="volatility_adjusted",
        max_position_percentage=0.04,
        stop_loss_percentage=0.02,
        take_profit_percentage=0.035,
        enable_trailing_stop=True,
        enable_dynamic_position_sizing=True,
        quantum_sensitivity_boost=1.0,
    ),
}

__all__ = ["RiskProfileSettings", "_FALLBACK_RISK_PROFILE_SETTINGS"]
