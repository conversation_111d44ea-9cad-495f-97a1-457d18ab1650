from __future__ import annotations

import asyncio
import time
from collections.abc import Awaitable, Callable, Sequence
from typing import Dict, Any

import pandas as pd

from ..utils.logger import get_logger
from .base_integration import CryptoDataFetcher

logger = get_logger(__name__)


class TickerWorker:
    """Fetch ticker and OHLCV concurrently for multiple symbols.

    Parameters
    ----------
    fetcher
        Instance responsible for retrieving market data.
    symbols
        Trading pairs to monitor.
    on_data
        Callback executed when new data is available.
    window_ms
        Interval between fetch cycles in milliseconds. Capped at ``500``.
    """

    def __init__(
        self,
        fetcher: CryptoDataFetcher,
        symbols: Sequence[str],
        on_data: Callable[[str, Dict[str, Any], pd.DataFrame], Awaitable[None]],
        *,
        window_ms: int = 500,
    ) -> None:
        self.fetcher = fetcher
        self.symbols = list(symbols)
        self.on_data = on_data
        self.window_ms = min(window_ms, 500)
        self._running = False
        self._tg: asyncio.TaskGroup | None = None

    async def _fetch_and_process(self, symbol: str) -> None:
        try:
            ticker, ohlcv = await self.fetcher.fetch_ticker_and_ohlcv(symbol)
        except asyncio.CancelledError:
            raise
        except Exception:  # pragma: no cover - logging only
            logger.exception("Erro ao obter dados para %s", symbol)
            return

        asyncio.create_task(self.on_data(symbol, ticker, ohlcv))

    async def run_once(self) -> None:
        """Execute one fetch cycle using ``asyncio.TaskGroup``."""
        async with asyncio.TaskGroup() as tg:
            for sym in self.symbols:
                tg.create_task(self._fetch_and_process(sym))

    async def start(self) -> None:
        """Continuously fetch data respecting ``window_ms``."""
        self._running = True
        try:
            while self._running:
                start_time = time.perf_counter()
                async with asyncio.TaskGroup() as tg:
                    self._tg = tg
                    for sym in self.symbols:
                        tg.create_task(self._fetch_and_process(sym))
                elapsed_ms = (time.perf_counter() - start_time) * 1000
                delay = max(0.0, self.window_ms - elapsed_ms) / 1000
                await asyncio.sleep(delay)
        except asyncio.CancelledError:
            if self._tg:
                self._tg._abort()
            raise
        finally:
            self._running = False
            if self._tg:
                self._tg._abort()
                self._tg = None

    def stop(self) -> None:
        """Stop the worker loop and cancel pending tasks."""
        self._running = False
        if self._tg is not None:
            self._tg._abort()
