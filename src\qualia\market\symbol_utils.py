from __future__ import annotations

from typing import Any, Iterable, List, Optional

from ..utils.logger import get_logger

logger = get_logger(__name__)


__all__ = [
    "normalize_symbol",
    "normalize_symbol_async",
    "validate_and_normalize_symbols",
    "to_canonical_format",
]


def to_canonical_format(symbol: str) -> str:
    """
    Converte um símbolo de par de trading para o formato canônico 'BASE/QUOTE'.

    Esta função não valida se o símbolo existe na exchange, ela apenas
    padroniza o formato para uso interno consistente.

    Exemplos:
    - BTC-USDT -> BTC/USDT
    - BTCUSDT -> BTC/USDT
    - btc/usdt -> BTC/USDT

    Parameters
    ----------
    symbol
        O símbolo do par de trading em qualquer formato comum.

    Returns
    -------
    str
        O símbolo no formato 'BASE/QUOTE'.
    """
    if not isinstance(symbol, str) or not symbol:
        return ""

    # Remove delimitadores comuns e converte para maiúsculas
    cleaned_symbol = symbol.upper().replace("-", "").replace("_", "").replace(" ", "")

    # Lista de quotes comuns para ajudar na separação
    common_quotes = [
        "USDT", "USDC", "BUSD", "TUSD", "USD", "EUR", "GBP",
        "BTC", "ETH", "BNB", "XRP", "SOL", "ADA",
    ]

    for quote in common_quotes:
        if cleaned_symbol.endswith(quote):
            base = cleaned_symbol[: -len(quote)]
            if base:
                return f"{base}/{quote}"

    # Fallback para símbolos com 6 caracteres (e.g., BTCETH)
    if len(cleaned_symbol) == 6:
        base = cleaned_symbol[:3]
        quote = cleaned_symbol[3:]
        return f"{base}/{quote}"
    
    # Se já estiver no formato, retorna como está
    if "/" in cleaned_symbol:
        return cleaned_symbol

    logger.warning(
        "Não foi possível determinar o formato canônico para '%s'. Retornando original.", symbol
    )
    return symbol.upper()


def _get_markets(exchange: Any) -> Optional[dict]:
    """Return ``exchange.markets`` when available and valid."""

    markets = getattr(exchange, "markets", None)
    return markets if isinstance(markets, dict) and markets else None


def _load_markets(exchange: Any) -> dict:
    """Synchronously call ``exchange.load_markets`` and return markets."""

    try:
        exchange.load_markets()
    except Exception as exc:  # pragma: no cover - network errors
        logger.warning("Falha ao carregar mercados: %s", exc)
        raise ValueError("Mercados indisponíveis") from exc

    markets = _get_markets(exchange)
    if not markets:
        raise ValueError("Mercados indisponíveis")
    return markets


async def _load_markets_async(exchange: Any) -> dict:
    """Asynchronously call ``exchange.load_markets`` and return markets."""

    try:
        await exchange.load_markets()
    except Exception as exc:  # pragma: no cover - network errors
        logger.warning("Falha ao carregar mercados: %s", exc)
        raise ValueError("Mercados indisponíveis") from exc

    markets = _get_markets(exchange)
    if not markets:
        raise ValueError("Mercados indisponíveis")
    return markets


def normalize_symbol(symbol: str, exchange: Any) -> str:
    """Normalize symbol names to the format used by ``exchange``.

    The function attempts simple transformations such as changing case and
    delimiter characters. It loads markets if they are not already available
    and validates that the resulting symbol exists.

    For the Kraken exchange specifically, ``BTC/USDT`` is converted to
    ``XBT/USDT`` or ``BTC/USD`` depending on which pair is listed.

    Parameters
    ----------
    symbol : str
        Trading pair in any common representation or market id.
    exchange : Any
        ``ccxt`` exchange instance providing ``markets`` and ``load_markets``.

    Returns
    -------
    str
        Canonical symbol or market id recognized by the exchange.

    Raises
    ------
    ValueError
        If the symbol is not listed in the exchange markets.
    """

    markets = _get_markets(exchange)
    if not markets:
        markets = _load_markets(exchange)

    # Permitir que o símbolo seja um market['id'] válido
    for market in markets.values():
        if symbol == market.get("id"):
            logger.debug("Símbolo %s corresponde ao market id", symbol)
            return symbol

    # Mapeamento especial para Kraken: BTC/USDT → XBT/USDT ou BTC/USD
    exchange_id = getattr(exchange, "id", None)
    if exchange_id == "kraken":
        # Tente XBT/USDT
        if symbol.upper().replace("-", "/").replace(" ", "") == "BTC/USDT":
            if "XBT/USDT" in markets:
                logger.info(
                    "Símbolo %s mapeado para XBT/USDT (preferência Kraken)",
                    symbol,
                )
                return "XBT/USDT"
            if "XBT/USD" in markets:
                logger.info("Símbolo %s mapeado para XBT/USD (fallback Kraken)", symbol)
                return "XBT/USD"
            if "BTC/USD" in markets:
                logger.info("Símbolo %s mapeado para BTC/USD (fallback Kraken)", symbol)
                return "BTC/USD"

    if symbol in markets:
        logger.debug("Símbolo %s encontrado diretamente", symbol)
        return symbol

    normalized = symbol.upper().replace("-", "/")
    if normalized in markets:
        logger.info(
            "Símbolo %s normalizado para %s (case/delimiter)",
            symbol,
            normalized,
        )
        return normalized

    stripped = normalized.replace("/", "")
    for market_symbol in markets:
        if market_symbol.replace("/", "") == stripped:
            logger.info(
                "Símbolo %s mapeado para %s (ignore delimitadores)",
                symbol,
                market_symbol,
            )
            return market_symbol

    raise ValueError(
        f"Par de trading nao encontrado: {symbol}. Símbolos disponíveis: {list(markets.keys())[:20]} ..."
    )


async def normalize_symbol_async(symbol: str, exchange: Any) -> str:
    """Asynchronous wrapper around :func:`normalize_symbol`.

    This version awaits ``exchange.load_markets()`` when the markets are not
    yet loaded before delegating to :func:`normalize_symbol`.

    Parameters
    ----------
    symbol : str
        Trading pair in any common representation or market id.
    exchange : Any
        ``ccxt`` exchange instance providing async ``load_markets``.

    Returns
    -------
    str
        Canonical symbol recognized by the exchange.
    """

    markets = _get_markets(exchange)
    if not markets:
        markets = await _load_markets_async(exchange)

    return normalize_symbol(symbol, exchange)


async def validate_and_normalize_symbols(
    symbols: Iterable[str], exchange: Any
) -> List[str]:
    """Validate and normalize user supplied symbols.

    Each provided symbol is normalized using :func:`normalize_symbol_async`.
    When a symbol is not found, a ``USDT`` to ``USD`` fallback is attempted. If
    no direct alternative exists, a warning is emitted with suggestions based on
    the base currency and the symbol is removed from the list.

    Parameters
    ----------
    symbols : Iterable[str]
        Raw symbols provided by the user configuration.
    exchange : Any
        ``ccxt`` exchange instance already connected and with markets loaded.

    Returns
    -------
    List[str]
        List of valid symbols recognized by the exchange.
    """

    markets = _get_markets(exchange)
    if not markets:
        try:  # pragma: no cover - network errors
            markets = await _load_markets_async(exchange)
        except ValueError:
            markets = {}

    valid_symbols: List[str] = []
    for sym in symbols:
        try:
            normalized = await normalize_symbol_async(sym, exchange)
        except ValueError:
            normalized = None
            alt = sym.upper().replace("USDT", "USD")
            if alt != sym.upper():
                try:
                    normalized = await normalize_symbol_async(alt, exchange)
                    logger.warning(
                        "Símbolo %s não encontrado. Usando %s como alternativa.",
                        sym,
                        normalized,
                    )
                except ValueError:
                    pass

            if normalized is None:
                base = sym.split("/")[0].upper()
                suggestions = [m for m in markets if m.startswith(f"{base}/")][:3]
                if suggestions:
                    logger.warning(
                        "Símbolo %s não encontrado. Sugestões: %s. Removendo.",
                        sym,
                        ", ".join(suggestions),
                    )
                else:
                    logger.warning(
                        "Símbolo %s não encontrado na exchange. Removendo.", sym
                    )
                continue

        valid_symbols.append(normalized)

    return valid_symbols
