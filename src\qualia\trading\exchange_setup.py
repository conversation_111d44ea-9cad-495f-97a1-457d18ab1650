"""Rotinas de configuração de exchanges.

Responsável por ler variáveis de ambiente e instanciar integrações
com as exchanges suportadas.
"""

from __future__ import annotations

from typing import Optional, Tuple, Any, List

import pandas as pd
from .data_utils import is_data_empty

import ccxt
from ccxt import AuthenticationError

from ..config.settings import get_env, settings
from ..market.kraken_integration import KrakenIntegration
from ..market.kucoin_integration import KucoinIntegration
from ..exchanges.base_exchange import BaseExchange
from ..exchanges.kucoin_client import KuCoinClient
from ..common.specs import MarketSpec

from ..utils.logger import get_logger

logger = get_logger(__name__)


def credentials_from_env(name: str) -> Tuple[str, str, Optional[str]]:
    """Ler credenciais da exchange a partir de variáveis de ambiente."""

    if name.lower() == "kraken":
        api_key = get_env("KRAKEN_API_KEY")
        api_secret = get_env("KRAKEN_SECRET_KEY")
        return api_key, api_secret, None
    if name.lower() == "kucoin":
        api_key = get_env("KUCOIN_API_KEY")
        api_secret = get_env("KUCOIN_SECRET_KEY")
        passphrase = get_env("KUCOIN_PASSPHRASE")
        return api_key, api_secret, passphrase
    raise ValueError(f"Exchange desconhecida: {name}")


def connection_settings_from_env() -> Tuple[float, int]:
    """Obter ``conn_timeout`` e ``conn_retries`` de variáveis de ambiente."""

    try:
        conn_timeout = float(get_env("EXCHANGE_CONN_TIMEOUT", "30", warn=False))
    except ValueError:
        conn_timeout = 30.0

    try:
        conn_retries = int(get_env("EXCHANGE_CONN_RETRIES", "3", warn=False))
    except ValueError:
        conn_retries = 3

    return conn_timeout, conn_retries


def decrypt_credentials_from_config(
    name: str,
    secure_config: dict,
    security,
    passphrase: Optional[str] = None,
) -> Tuple[str, str, Optional[str]]:
    """Descriptografar credenciais armazenadas em ``secure_config``."""

    api_key, api_secret = security.decrypt_credentials(
        secure_config["encrypted_credentials"]
    )
    if name.lower() == "kucoin":
        return api_key, api_secret, passphrase
    return api_key, api_secret, None


def initialize_exchange(
    name: str,
    *,
    api_key: Optional[str] = None,
    api_secret: Optional[str] = None,
    password: Optional[str] = None,
    conn_timeout: Optional[float] = None,
    conn_retries: Optional[int] = None,
    ticker_timeout: Optional[float] = None,
    ticker_retries: Optional[int] = None,
    fail_threshold: Optional[int] = None,
    recovery_timeout: Optional[float] = None,
):
    """Instanciar integração específica da exchange."""

    logger.info("Inicializando exchange: %s", name)

    if conn_timeout is None or conn_retries is None:
        ct, cr = connection_settings_from_env()
        conn_timeout = conn_timeout if conn_timeout is not None else ct
        conn_retries = conn_retries if conn_retries is not None else cr

    if name.lower() == "kraken":
        return KrakenIntegration(
            api_key=api_key,
            api_secret=api_secret,
            conn_timeout=conn_timeout,
            conn_retries=conn_retries,
            ticker_timeout=ticker_timeout,
            ticker_retries=ticker_retries,
            fail_threshold=fail_threshold,
            recovery_timeout=recovery_timeout,
        )

    if name.lower() == "kucoin":
        return KucoinIntegration(
            api_key=api_key,
            api_secret=api_secret,
            password=password,
            conn_timeout=conn_timeout,
            conn_retries=conn_retries,
            ticker_timeout=ticker_timeout,
            ticker_retries=ticker_retries,
            fail_threshold=fail_threshold,
            recovery_timeout=recovery_timeout,
        )

    raise ValueError(f"Exchange desconhecida: {name}")


async def preflight_exchange_check(
    trader: Any, *, keep_connection: bool = False
) -> bool:
    """Executar verificações preliminares de acesso à exchange.

    Parameters
    ----------
    trader : Any
        Instância que provê métodos de integração com a exchange.
    keep_connection : bool, optional
        Quando ``True`` mantém a conexão aberta ao final das
        verificações. O padrão é ``False``.

    Returns
    -------
    bool
        ``True`` caso todas as chamadas à exchange sejam bem-sucedidas;
        ``False`` em caso de falhas ou dados ausentes.
    """

    try:
        await trader._initialize_exchange_connection()

        all_ok = True
        for symbol in trader.symbols:
            try:
                ticker = await trader.exchange.fetch_ticker(symbol)
                if not ticker:
                    raise ValueError("ticker vazio")
            except Exception as exc:
                logger.error(
                    "Pré-flight: falha ao obter ticker %s: %s",
                    symbol,
                    exc,
                )
                all_ok = False

            for tf in trader.timeframes:
                try:
                    data = await trader.exchange.fetch_ohlcv(symbol, tf, limit=1)
                    # CORREÇÃO YAA: Usar verificação tipo-segura
                    if is_data_empty(data):
                        raise ValueError("OHLCV vazio")
                except Exception as exc:
                    logger.error(
                        "Pré-flight: falha ao obter OHLCV %s %s: %s",
                        symbol,
                        tf,
                        exc,
                    )
                    all_ok = False

        return all_ok
    except (
        AuthenticationError,
        getattr(ccxt, "BaseError", Exception),
        OSError,
        RuntimeError,
    ) as exc:
        logger.error("Erro de pré-flight na exchange: %s", exc)
        cached_ok = False
        try:
            fetcher = getattr(trader, "exchange", None)
            if fetcher is not None:
                cached_ok = True
                for symbol in trader.symbols:
                    for tf in trader.timeframes:
                        cached_df = getattr(
                            fetcher, "_get_cached_ohlcv", lambda *a, **k: None
                        )(symbol, tf)

                        if is_data_empty(cached_df):
                            cached_ok = False
                            break
                    if not cached_ok:
                        break
        except Exception:  # pragma: no cover - log and continue
            logger.debug("Falha ao validar cache OHLCV", exc_info=True)
        if cached_ok:
            logger.info("Pré-flight bem-sucedido utilizando cache de OHLCV")
            return True
        return False
    finally:
        if not keep_connection:
            try:
                await trader.close_exchange()
            except Exception:  # pragma: no cover - log and continue
                logger.debug("Falha ao fechar exchange após pré-flight", exc_info=True)


async def _verify_exchange_connectivity(
    trader: QUALIATrader, symbols: List[str], timeframes: List[str]
) -> bool:
    """Verifica a conectividade e a disponibilidade de dados para todos os pares."""
    logger.info("Verificando conectividade e disponibilidade de dados...")
    all_ok = True
    for symbol in symbols:
        for tf in timeframes:
            try:
                # Tenta buscar 1 candle para verificar se o par existe
                data = await trader.exchange.fetch_ohlcv(
                    spec=MarketSpec(symbol=symbol, timeframe=tf), limit=1
                )
                if data is None or data.empty:
                    logger.error(
                        f"Falha na verificação de conectividade para {symbol}@{tf}: Sem dados."
                    )
                    all_ok = False
                else:
                    logger.debug(f"Verificação de conectividade OK para {symbol}@{tf}.")
            except Exception as e:
                logger.error(
                    f"Erro na verificação de conectividade para {symbol}@{tf}: {e}",
                    exc_info=True,
                )
                all_ok = False
    return all_ok
