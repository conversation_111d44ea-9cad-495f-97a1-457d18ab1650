"""Helpers for safely handling market data objects."""

from __future__ import annotations

import pandas as pd
import numpy as np

from ..utils.logger import get_logger

logger = get_logger(__name__)


def is_data_empty(data) -> bool:
    """Check whether ``data`` is empty in a type-safe manner.

    Parameters
    ----------
    data
        Dataset to verify. Supports pandas objects, numpy arrays, lists and tuples.

    Returns
    -------
    bool
        ``True`` if ``data`` contains no entries, ``False`` otherwise.
    """
    if data is None:
        return True

    if isinstance(data, pd.DataFrame):
        return data.empty
    if isinstance(data, np.ndarray):
        return data.size == 0
    if isinstance(data, (list, tuple)):
        return len(data) == 0
    if hasattr(data, "__len__"):
        try:
            return len(data) == 0
        except Exception:  # pragma: no cover - defensive
            return True
    if hasattr(data, "empty"):
        try:
            return data.empty
        except Exception:  # pragma: no cover - defensive
            return True
    return False


def safe_iloc(data, index: int = 0, column: str | int | None = None):
    """Safely access ``data`` by position.

    Parameters
    ----------
    data
        Dataset supporting index access.
    index
        Positional index to fetch. Default is ``0`` (first element).
    column
        Column name or index when ``data`` is a ``DataFrame``.

    Returns
    -------
    Any | None
        Value at the specified location, or ``None`` when out of bounds.
    """
    if is_data_empty(data):
        return None

    try:
        if isinstance(data, pd.DataFrame):
            if column is not None:
                return data[column].iloc[index]
            return data.iloc[index]
        if isinstance(data, pd.Series):
            return data.iloc[index]
        if isinstance(data, np.ndarray):
            if data.ndim == 1:
                return data[index]
            return data[index, column] if column is not None else data[index]
        if hasattr(data, "__getitem__"):
            return data[index]
    except (IndexError, KeyError, AttributeError):  # pragma: no cover - best effort
        return None
    return None


def safe_get_last_close(data) -> float | None:
    """Return the last close price from ``data`` when available.

    Parameters
    ----------
    data
        Market data structure such as ``DataFrame`` or numpy array.

    Returns
    -------
    float | None
        Latest close value if retrievable, otherwise ``None``.
    """
    if is_data_empty(data):
        return None

    try:
        if isinstance(data, pd.DataFrame):
            if "close" in data.columns:
                return float(data["close"].iloc[-1])
        elif isinstance(data, np.ndarray):
            if data.size > 0:
                if data.ndim == 1:
                    return float(data[-1])
                return float(data[-1, -1])
        elif isinstance(data, (list, tuple)) and len(data) > 0:
            return float(data[-1])
    except (
        IndexError,
        KeyError,
        ValueError,
        TypeError,
    ) as exc:  # pragma: no cover - log
        logger.debug("Erro ao extrair último close: %s", exc)
        return None
    return None
