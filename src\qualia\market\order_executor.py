from __future__ import annotations

from typing import Any, Dict, List, Optional, TYPE_CHECKING

if TYPE_CHECKING:
    from .base_integration import CryptoDataFetcher


class OrderExecutor:
    """Execute and query orders through a CryptoDataFetcher."""

    def __init__(self, fetcher: "CryptoDataFetcher") -> None:
        self._fetcher = fetcher

    async def create_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        params = params or {}
        return await self._fetcher.create_order(
            symbol, order_type, side, amount, price, params
        )

    async def cancel_order(
        self, order_id: str, symbol: Optional[str] = None
    ) -> Dict[str, Any]:
        return await self._fetcher.cancel_order(order_id, symbol)

    async def fetch_order(
        self, order_id: str, symbol: Optional[str] = None
    ) -> Dict[str, Any]:
        return await self._fetcher.fetch_order(order_id, symbol)

    async def fetch_open_orders(
        self, symbol: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        return await self._fetcher.fetch_open_orders(symbol)

    async def fetch_positions(
        self, symbols: Optional[List[str]] = None
    ) -> List[Dict[str, Any]]:
        return await self._fetcher.fetch_positions(symbols)
