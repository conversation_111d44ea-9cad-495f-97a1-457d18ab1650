"""
QUALIA Farsight Holographic Extension

Estende o FarsightEngine existente com capacidades holográficas,
integrando insights do arXiv com simulação de universo holográfico
para gerar sinais de trading avançados.
"""

from __future__ import annotations

import numpy as np
import time
import asyncio
from typing import Dict, List, Any, Optional
from dataclasses import asdict
import inspect

from qualia.analysis.farsight_engine import FarsightEngine
from ..consciousness.holographic_universe import (
    HolographicMarketUniverse,
    HolographicEvent,
    HolographicPattern,
    TradingSignal
)
from ..custom_types import CollectiveMindState
from ..utils.logger import get_logger

logger = get_logger(__name__)

class HolographicFarsightEngine(FarsightEngine):
    """
    Extensão do FarsightEngine com capacidades holográficas.
    
    Combina insights do arXiv com simulação de universo holográfico
    para detectar padrões emergentes e gerar sinais de trading.
    """
    
    def __init__(
        self,
        holographic_universe: Optional[HolographicMarketUniverse] = None,
        **kwargs
    ):
        # YAA-FIX-ROBUST: Filtrar dinamicamente os kwargs para passar apenas os
        # argumentos que o construtor de FarsightEngine realmente espera.
        # Isso evita TypeError para 'config', 'personas' ou qualquer outro
        # argumento inesperado adicionado no futuro.
        
        # 1. Obter a assinatura do construtor da classe pai
        parent_init_signature = inspect.signature(FarsightEngine.__init__)
        parent_params = parent_init_signature.parameters
        
        # 2. Criar um dicionário com apenas os kwargs válidos para o pai
        valid_parent_kwargs = {
            key: value for key, value in kwargs.items() if key in parent_params
        }
        
        logger.debug(
            f"HolographicFarsightEngine: Filtrando kwargs para FarsightEngine. "
            f"Recebidos: {list(kwargs.keys())}. "
            f"Válidos para o pai: {list(valid_parent_kwargs.keys())}."
        )
            
        # 3. Chamar super().__init__ com os argumentos filtrados
        super().__init__(**valid_parent_kwargs)
        
        # Inicializa universo holográfico
        self.holographic_universe = holographic_universe or HolographicMarketUniverse()
        
        # Estado da simulação
        self.simulation_time = 0.0
        self.simulation_running = False
        self.holographic_insights = []
        
        logger.info("HolographicFarsightEngine inicializado com universo holográfico")
    
    def run_holographic_analysis(self) -> List[Dict[str, Any]]:
        """
        Executa análise holográfica completa.
        
        Returns:
            Lista de insights combinados (Farsight + Holográfico)
        """
        # Step 1: Executa Farsight tradicional
        farsight_insights = super().run()
        logger.info(f"Farsight detectou {len(farsight_insights)} insights")
        
        # Step 2: Converte insights em eventos holográficos
        holographic_events = self._convert_insights_to_events(farsight_insights)
        logger.info(f"Convertidos {len(holographic_events)} eventos holográficos")
        
        # Step 3: Executa simulação holográfica
        holographic_patterns = asyncio.run(self._run_holographic_simulation(holographic_events))
        logger.info(f"Simulação detectou {len(holographic_patterns)} padrões")
        
        # Step 4: Gera sinais de trading
        trading_signals = self.holographic_universe.generate_trading_signals(holographic_patterns)
        logger.info(f"Gerados {len(trading_signals)} sinais de trading")
        
        # Step 5: Combina insights
        combined_insights = self._combine_insights(
            farsight_insights, holographic_patterns, trading_signals
        )
        
        # Step 6: Publica métricas
        self._emit_holographic_metrics(combined_insights)
        
        return combined_insights
    
    def _convert_insights_to_events(self, insights: List[Dict[str, Any]]) -> List[HolographicEvent]:
        """
        Converte insights do Farsight em eventos holográficos.
        
        Args:
            insights: Lista de insights do Farsight
            
        Returns:
            Lista de eventos holográficos
        """
        events = []
        current_time = time.time()
        
        for i, insight in enumerate(insights):
            # Mapeia insight para posição espacial
            position = self._map_insight_to_position(insight, i)
            
            # Calcula parâmetros do evento
            amplitude = self._calculate_event_amplitude(insight)
            spatial_sigma = self._calculate_spatial_spread(insight)
            temporal_sigma = self._calculate_temporal_spread(insight)
            
            event = HolographicEvent(
                position=position,
                time=current_time,
                amplitude=amplitude,
                spatial_sigma=spatial_sigma,
                temporal_sigma=temporal_sigma,
                event_type=f"farsight_{insight.get('topic', 'unknown')}",
                source_data=insight,
                confidence=min(insight.get('curvature', 0.5), 1.0)
            )
            
            events.append(event)
        
        return events
    
    def _map_insight_to_position(self, insight: Dict[str, Any], index: int) -> tuple[int, int]:
        """
        Mapeia insight para posição no campo holográfico.
        
        Args:
            insight: Insight do Farsight
            index: Índice do insight
            
        Returns:
            Posição (x, y) no campo
        """
        # Usa hash do tópico para posição determinística
        topic = insight.get('topic', f'insight_{index}')
        topic_hash = hash(topic)
        
        # Mapeia hash para coordenadas
        field_size = self.holographic_universe.field_size
        x = abs(topic_hash) % field_size[0]
        y = abs(topic_hash // field_size[0]) % field_size[1]
        
        # Adiciona variação baseada em curvatura e velocidade
        curvature = insight.get('curvature', 0.5)
        velocity = insight.get('velocity', 0.5)
        
        x_offset = int(curvature * 20 - 10)  # -10 a +10
        y_offset = int(velocity * 20 - 10)   # -10 a +10
        
        x = max(0, min(field_size[0] - 1, x + x_offset))
        y = max(0, min(field_size[1] - 1, y + y_offset))
        
        return (x, y)
    
    def _calculate_event_amplitude(self, insight: Dict[str, Any]) -> float:
        """
        Calcula amplitude do evento baseado nas métricas do insight.
        
        Args:
            insight: Insight do Farsight
            
        Returns:
            Amplitude do evento (0.0 a 2.0)
        """
        curvature = insight.get('curvature', 0.5)
        velocity = insight.get('velocity', 0.5)
        
        # Combina curvatura e velocidade
        # Alta curvatura = alta inovação
        # Baixa velocidade = nicho emergente
        amplitude = curvature * (1.0 + (1.0 - velocity))
        
        return min(amplitude, 2.0)
    
    def _calculate_spatial_spread(self, insight: Dict[str, Any]) -> float:
        """
        Calcula dispersão espacial baseado no impacto do insight.
        
        Args:
            insight: Insight do Farsight
            
        Returns:
            Sigma espacial (5.0 a 25.0)
        """
        velocity = insight.get('velocity', 0.5)
        
        # Maior velocidade = maior dispersão (mais mainstream)
        spatial_sigma = 5.0 + velocity * 20.0
        
        return spatial_sigma
    
    def _calculate_temporal_spread(self, insight: Dict[str, Any]) -> float:
        """
        Calcula dispersão temporal baseado na persistência do insight.
        
        Args:
            insight: Insight do Farsight
            
        Returns:
            Sigma temporal (2.0 a 10.0)
        """
        curvature = insight.get('curvature', 0.5)
        
        # Maior curvatura = maior persistência temporal
        temporal_sigma = 2.0 + curvature * 8.0
        
        return temporal_sigma
    
    async def _run_holographic_simulation(
        self, 
        events: List[HolographicEvent]
    ) -> List[HolographicPattern]:
        """
        Executa simulação holográfica com os eventos.
        
        Args:
            events: Lista de eventos para injetar
            
        Returns:
            Lista de padrões detectados
        """
        # Injeta todos os eventos
        for event in events:
            await self.holographic_universe.inject_holographic_event(event)
        
        # Executa simulação por 100 passos
        self.simulation_running = True
        patterns = []
        
        try:
            for step in range(100):
                current_time = time.time() + step * 0.1  # 0.1s por passo
                
                # Evolui universo
                await self.holographic_universe.step_evolution(current_time)
                
                # Analisa padrões a cada 10 passos
                if step % 10 == 0:
                    step_patterns = self.holographic_universe.analyze_holographic_patterns()
                    patterns.extend(step_patterns)
                
                # Pequena pausa para não sobrecarregar
                await asyncio.sleep(0.001)
            
        finally:
            self.simulation_running = False
        
        # Remove padrões duplicados
        unique_patterns = self._deduplicate_patterns(patterns)
        
        return unique_patterns
    
    def _deduplicate_patterns(self, patterns: List[HolographicPattern]) -> List[HolographicPattern]:
        """
        Remove padrões duplicados baseado em posição e tipo.
        
        Args:
            patterns: Lista de padrões
            
        Returns:
            Lista de padrões únicos
        """
        seen = set()
        unique_patterns = []
        
        for pattern in patterns:
            # Cria chave única baseada em posição e tipo
            key = (pattern.position, pattern.pattern_type)
            
            if key not in seen:
                seen.add(key)
                unique_patterns.append(pattern)
        
        return unique_patterns
    
    def _combine_insights(
        self,
        farsight_insights: List[Dict[str, Any]],
        holographic_patterns: List[HolographicPattern],
        trading_signals: List[TradingSignal]
    ) -> List[Dict[str, Any]]:
        """
        Combina insights do Farsight com padrões holográficos.
        
        Args:
            farsight_insights: Insights originais do Farsight
            holographic_patterns: Padrões detectados no universo holográfico
            trading_signals: Sinais de trading gerados
            
        Returns:
            Lista de insights combinados
        """
        combined_insights = []
        
        # Adiciona insights originais do Farsight com enriquecimento holográfico
        for insight in farsight_insights:
            enhanced_insight = insight.copy()
            enhanced_insight['holographic_analysis'] = {
                'field_summary': self.holographic_universe.get_field_summary(),
                'related_patterns': len(holographic_patterns),
                'trading_signals_generated': len(trading_signals)
            }
            combined_insights.append(enhanced_insight)
        
        # Adiciona insights puramente holográficos
        for pattern in holographic_patterns:
            if pattern.confidence > 0.8:  # Apenas padrões de alta confiança
                holographic_insight = {
                    'topic': f"Holographic {pattern.pattern_type}",
                    'curvature': pattern.strength,
                    'velocity': pattern.dominant_frequency,
                    'prediction_window': pattern.timeframe,
                    'sources': [{
                        'title': f"Holographic pattern at {pattern.position}",
                        'link': f"holographic://field/{pattern.position[0]},{pattern.position[1]}"
                    }],
                    'holographic_analysis': {
                        'pattern_type': pattern.pattern_type,
                        'position': pattern.position,
                        'confidence': pattern.confidence,
                        'timestamp': pattern.timestamp
                    }
                }
                combined_insights.append(holographic_insight)
        
        # Adiciona resumo de sinais de trading
        if trading_signals:
            trading_summary = {
                'topic': f"Holographic Trading Signals ({len(trading_signals)})",
                'curvature': np.mean([s.strength for s in trading_signals]),
                'velocity': np.mean([s.confidence for s in trading_signals]),
                'prediction_window': "Multi-timeframe",
                'sources': [{
                    'title': f"{signal.symbol}: {signal.action}",
                    'link': f"trading://{signal.symbol}/{signal.action}"
                } for signal in trading_signals[:5]],  # Top 5 signals
                'holographic_analysis': {
                    'trading_signals': [asdict(signal) for signal in trading_signals],
                    'signal_count': len(trading_signals),
                    'avg_confidence': np.mean([s.confidence for s in trading_signals])
                }
            }
            combined_insights.append(trading_summary)
        
        return combined_insights
    
    def _emit_holographic_metrics(self, combined_insights: List[Dict[str, Any]]) -> None:
        """
        Emite métricas holográficas para monitoramento.
        
        Args:
            combined_insights: Lista de insights combinados
        """
        # Métricas do universo holográfico
        field_summary = self.holographic_universe.get_field_summary()
        
        # Métricas de insights combinados
        holographic_insights = [
            i for i in combined_insights 
            if 'holographic_analysis' in i
        ]
        
        # Métricas de trading signals
        trading_insights = [
            i for i in combined_insights
            if 'trading_signals' in i.get('holographic_analysis', {})
        ]
        
        logger.debug("Métricas holográficas processadas")

    def generate_collective_mind_state(self) -> 'CollectiveMindState':
        """
        Executa uma análise focada em gerar o estado mental coletivo para o simulador de agentes.
        Este método é o ponto de entrada para o SEA e coexiste com `run_holographic_analysis`.

        Returns:
            Um objeto CollectiveMindState representando o estado mental inferido.
        """
        logger.info("Gerando CollectiveMindState para o Simulador de Ecossistemas de Agentes.")
        
        # Step 1: Executa Farsight tradicional para obter os dados brutos
        farsight_insights = super().run()
        logger.info(f"Farsight detectou {len(farsight_insights)} insights para análise de sentimento.")

        # Step 2: Determinar a narrativa dominante a partir dos insights
        # Placeholder: Lógica simples para encontrar o tópico mais frequente
        if not farsight_insights:
            dominant_narrative = "NEUTRAL"
        else:
            topics = [insight.get('topic', 'unknown') for insight in farsight_insights]
            dominant_narrative = max(set(topics), key=topics.count)

        # Step 3: Calcular o impacto da narrativa em cada persona
        persona_impact = self._calculate_persona_impact(dominant_narrative, farsight_insights)

        # Step 4: Construir e retornar o objeto CollectiveMindState
        mind_state = CollectiveMindState(
            timestamp=time.time(),
            dominant_narrative=dominant_narrative,
            persona_impact=persona_impact
        )

        logger.info(f"CollectiveMindState gerado com a narrativa dominante: {dominant_narrative}")
        return mind_state

    def _calculate_persona_impact(self, dominant_narrative: str, insights: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Calcula o impacto da narrativa dominante nas diferentes personas.
        Esta é uma implementação inicial (placeholder).

        Args:
            dominant_narrative: A narrativa dominante identificada.
            insights: A lista de insights brutos do Farsight.

        Returns:
            Um dicionário mapeando o nome da persona para seu impacto.
        """
        # Placeholder: Lógica de mapeamento simples
        impact = {
            "RetailCluster": {"confidence_boost": 0.0, "action_bias": "NEUTRAL"},
            "MomentumQuant": {"confidence_boost": 0.0, "action_bias": "NEUTRAL"}
        }

        # Exemplo: Se a narrativa for sobre 'AI', aumenta a confiança do varejo
        if 'AI' in dominant_narrative.upper():
            impact["RetailCluster"]["confidence_boost"] = 0.25
            impact["RetailCluster"]["action_bias"] = "BUY"
            logger.debug("Narrativa de IA detectada, impulsionando o RetailCluster.")

        # Exemplo: MomentumQuant é geralmente indiferente a narrativas
        # Nenhuma alteração necessária para MomentumQuant com base nesta lógica simples.

        return impact
    
    async def shutdown(self):
        """Shutdown do engine holográfico."""
        self.simulation_running = False
        await self.holographic_universe.shutdown()
        logger.info("HolographicFarsightEngine shutdown concluído")

# Factory function para criar engine holográfico
def create_holographic_farsight_engine(**kwargs) -> HolographicFarsightEngine:
    """
    Cria instância do HolographicFarsightEngine com configuração padrão.
    
    Args:
        **kwargs: Argumentos para FarsightEngine
        
    Returns:
        Instância configurada do HolographicFarsightEngine
    """
    return HolographicFarsightEngine(**kwargs)
 