"""Caches centralizados para simulações quânticas do QUALIA."""

from __future__ import annotations

from typing import Dict, Optional, Callable

from qiskit import QuantumCircuit
from qiskit.quantum_info import Statevector

from ..utils.logger import get_logger

logger = get_logger(__name__)

# Cache específico para circuitos e vetores de estado da QFT
_QFT_CIRCUIT_CACHE: Dict[int, QuantumCircuit] = {}
_QFT_STATE_CACHE: Dict[int, Statevector] = {}
# Estados usados como fallback quando a QFT não pode ser construída
_FALLBACK_STATE_CACHE: Dict[int, Statevector] = {}

# Cache genérico para circuitos identificados por chave
_GENERIC_CIRCUIT_CACHE: Dict[str, QuantumCircuit] = {}


def get_qft_circuit(
    n_qubits: int, builder: Callable[[int], QuantumCircuit]
) -> QuantumCircuit:
    """Retorna circuito QFT do cache, construindo se necessário."""
    circuit = _QFT_CIRCUIT_CACHE.get(n_qubits)
    if circuit is None:
        circuit = builder(n_qubits)
        _QFT_CIRCUIT_CACHE[n_qubits] = circuit
    return circuit


def get_qft_statevector(
    n_qubits: int, builder: Callable[[int], Statevector]
) -> Statevector:
    """Retorna statevector QFT do cache, construindo se necessário."""
    sv = _QFT_STATE_CACHE.get(n_qubits)
    if sv is None:
        sv = builder(n_qubits)
        _QFT_STATE_CACHE[n_qubits] = sv
    return sv


def get_fallback_state(n_qubits: int) -> Optional[Statevector]:
    """Obtém statevector usado como fallback."""
    return _FALLBACK_STATE_CACHE.get(n_qubits)


def cache_fallback_state(n_qubits: int, sv: Statevector) -> None:
    """Armazena um statevector de fallback."""
    _FALLBACK_STATE_CACHE[n_qubits] = sv


def cache_circuit(key: str, circuit: QuantumCircuit) -> None:
    """Armazena um circuito no cache genérico."""
    _GENERIC_CIRCUIT_CACHE[key] = circuit


def get_cached_circuit(key: str) -> Optional[QuantumCircuit]:
    """Obtém um circuito do cache genérico."""
    return _GENERIC_CIRCUIT_CACHE.get(key)


def invalidate_qft_cache(n_qubits: Optional[int] = None) -> None:
    """Remove entradas do cache de QFT."""
    if n_qubits is None:
        _QFT_CIRCUIT_CACHE.clear()
        _QFT_STATE_CACHE.clear()
        logger.info("Cache de QFT limpo para todos os tamanhos")
    else:
        _QFT_CIRCUIT_CACHE.pop(n_qubits, None)
        _QFT_STATE_CACHE.pop(n_qubits, None)
        logger.info("Cache de QFT invalidado para %s qubits", n_qubits)
