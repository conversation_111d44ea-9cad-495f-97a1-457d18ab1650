"""Helpers to load default parameters for dynamic risk management.

This module reads ``config/dynamic_risk_defaults.yaml`` unless the
``QUALIA_DYNAMIC_RISK_DEFAULTS`` environment variable specifies an
alternative file.
"""

from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("dynamic_risk_defaults.yaml")


def load_dynamic_risk_defaults() -> Dict[str, Any]:
    """Load dynamic risk default parameters from a YAML file.

    The path can be overridden via the ``QUALIA_DYNAMIC_RISK_DEFAULTS``
    environment variable.

    Returns
    -------
    Dict[str, Any]
        Mapping of parameter names to values. Returns an empty dict on error.
    """

    return load_yaml_config(
        "QUALIA_DYNAMIC_RISK_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_dynamic_risk_defaults"]
