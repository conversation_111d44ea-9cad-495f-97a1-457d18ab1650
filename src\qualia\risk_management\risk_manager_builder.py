from __future__ import annotations

"""Fábrica e registro de gerenciadores de risco.

Este módulo permite registrar e instanciar classes de risk manager de forma
flexível. Segue o mesmo padrão utilizado em ``config.encoder_registry`` para
facilitar a descoberta dinâmica de implementações. O acesso ao registro é
protegido por ``threading.Lock`` para evitar condições de corrida durante
operações concorrentes de registro ou criação de gerenciadores.
"""

from typing import Any, Dict, Type

from ..memory.event_bus import SimpleEventBus
import threading

from ..utils.logger import get_logger
from ..risk_management.risk_manager_base import QUALIARiskManagerBase
from ..risk_management.simple_risk_manager import SimpleRiskManager
from ..risk_management.advanced_risk_manager import AdvancedRiskManager

logger = get_logger(__name__)


_RISK_MANAGER_REGISTRY: Dict[str, Type[QUALIARiskManagerBase]] = {
    "simple": SimpleRiskManager,
    "advanced": AdvancedRiskManager,
}
"""Mapeia aliases para classes de risk manager registrados."""

_REGISTRY_LOCK = threading.Lock()


def register_risk_manager(alias: str, cls: Type[QUALIARiskManagerBase]) -> None:
    """Registra uma nova classe de risk manager.

    Parameters
    ----------
    alias : str
        Nome pelo qual o gerenciador será referenciado.
    cls : Type[QUALIARiskManagerBase]
        Classe a ser registrada.

    Raises
    ------
    TypeError
        Se ``cls`` não for subclasse de :class:`QUALIARiskManagerBase`.
    """

    if not issubclass(cls, QUALIARiskManagerBase):
        raise TypeError("cls deve ser subclasse de QUALIARiskManagerBase")
    with _REGISTRY_LOCK:
        if alias in _RISK_MANAGER_REGISTRY:
            logger.warning("Risk manager '%s' sobrescrito", alias)
        _RISK_MANAGER_REGISTRY[alias] = cls


def create_risk_manager(
    alias: str, *, event_bus: Optional[SimpleEventBus] = None, **kwargs: Any
) -> QUALIARiskManagerBase:
    """Instancia um risk manager pelo alias registrado."""
    with _REGISTRY_LOCK:
        if alias not in _RISK_MANAGER_REGISTRY:
            raise ValueError(f"Risk manager '{alias}' não registrado")
        cls = _RISK_MANAGER_REGISTRY[alias]
    return cls(event_bus=event_bus, **kwargs)


def get_risk_manager_class(alias: str) -> Type[QUALIARiskManagerBase]:
    """Retorna a classe registrada para ``alias``."""
    if alias not in _RISK_MANAGER_REGISTRY:
        raise ValueError(f"Risk manager '{alias}' não registrado")
    return _RISK_MANAGER_REGISTRY[alias]


def get_registered_risk_managers() -> Dict[str, Type[QUALIARiskManagerBase]]:
    """Devolve uma cópia do registro interno de risk managers."""
    return dict(_RISK_MANAGER_REGISTRY)


__all__ = [
    "register_risk_manager",
    "create_risk_manager",
    "get_risk_manager_class",
    "get_registered_risk_managers",
]
