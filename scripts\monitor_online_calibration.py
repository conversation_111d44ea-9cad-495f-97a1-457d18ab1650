#!/usr/bin/env python3
"""
Script de Monitoramento da Calibração Online QUALIA
Monitora performance e executa calibração automática
"""

import asyncio
import sys
import argparse
from pathlib import Path
from datetime import datetime

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.online_calibration_system import OnlineCalibrationSystem
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

def parse_arguments():
    """Parse argumentos da linha de comando"""
    parser = argparse.ArgumentParser(
        description="Monitor de Calibração Online QUALIA",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--show-performance', action='store_true',
                       help='Mostrar resumo de performance')
    parser.add_argument('--force-calibration', action='store_true',
                       help='Forçar calibração imediata')
    parser.add_argument('--days', type=int, default=7,
                       help='Dias para análise de performance (padrão: 7)')
    parser.add_argument('--continuous', action='store_true',
                       help='Modo contínuo de monitoramento')
    parser.add_argument('--interval', type=int, default=3600,
                       help='Intervalo em segundos para modo contínuo (padrão: 3600 = 1h)')
    
    return parser.parse_args()

async def main():
    """Função principal de monitoramento"""
    args = parse_arguments()
    
    print("🌌 QUALIA - Monitor de Calibração Online")
    print("=" * 50)
    
    try:
        # Inicializar sistema
        logger.info("🔧 Inicializando sistema QUALIA...")
        trading_system = QualiaBinanceCorrectedSystem()
        
        # Verificar se online_calibrator existe
        if not hasattr(trading_system, 'online_calibrator'):
            logger.error("❌ Sistema de calibração online não encontrado!")
            return
        
        calibrator = trading_system.online_calibrator
        
        if args.show_performance:
            # Mostrar performance atual
            performance_summary = calibrator.get_performance_summary()
            print(performance_summary)
            
            # Mostrar thresholds atuais
            print("\n🎯 THRESHOLDS ATUAIS:")
            print("-" * 30)
            for metric, value in trading_system.quantum_thresholds.items():
                print(f"   {metric}: {value:.3f}")
        
        if args.force_calibration:
            # Forçar calibração
            print("\n🔄 Executando calibração forçada...")
            await calibrator.execute_online_calibration()
            print("✅ Calibração concluída!")
        
        if args.continuous:
            # Modo contínuo
            print(f"\n🔄 Iniciando monitoramento contínuo (intervalo: {args.interval}s)")
            await continuous_monitoring(calibrator, args.interval)
    
    except Exception as e:
        logger.error(f"❌ Erro no monitoramento: {e}")
        raise

async def continuous_monitoring(calibrator: OnlineCalibrationSystem, interval: int):
    """Executa monitoramento contínuo"""
    logger.info("🔄 Monitoramento contínuo iniciado...")
    
    try:
        while True:
            # Verificar se precisa calibrar
            recent_trades = calibrator._get_recent_trades(days=1)
            
            if len(recent_trades) >= calibrator.min_trades_for_calibration:
                current_performance = calibrator._calculate_current_performance(recent_trades)
                
                if calibrator._should_recalibrate(current_performance):
                    logger.info("🎯 Trigger de calibração detectado!")
                    await calibrator.execute_online_calibration()
            
            # Mostrar status
            performance_summary = calibrator.get_performance_summary()
            logger.info(f"📊 Status: {len(recent_trades)} trades nas últimas 24h")
            
            # Aguardar próximo ciclo
            await asyncio.sleep(interval)
            
    except KeyboardInterrupt:
        logger.info("⚠️ Monitoramento interrompido pelo usuário")
    except Exception as e:
        logger.error(f"❌ Erro no monitoramento contínuo: {e}")

def run_monitor():
    """Wrapper para executar monitor"""
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Monitor interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    run_monitor()
