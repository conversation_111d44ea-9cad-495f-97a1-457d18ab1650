"""
QUALIA Quantum Metrics Calculator

Este módulo implementa o cálculo de métricas de trading usando o QUALIA Universe
e o ciclo QAST, aproveitando a arquitetura quântica avançada existente no sistema.

Ao utilizar o QUALIA Universe, este módulo consegue extrair insights de mercado
mais profundos através da dinâmica da consciência quântica do QUALIA.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timezone
from ..utils.logger import get_logger
from typing import Dict, List, Any, Optional, Tuple, Set
import time
import json
import hashlib
import logging

# YAA: Adicionar import para qasm2
try:
    from qiskit import qasm2  # type: ignore
except Exception:  # pragma: no cover - optional in older qiskit versions
    qasm2 = None

# YAA: Adicionar import para transpile
from qiskit.compiler import transpile
from qiskit import QuantumCircuit
from ..utils.quantum_utils import enforce_circuit_limits
from .symbol_utils import normalize_symbol

# Logging configuration available early for fallback imports
logger = get_logger(__name__)
circuits_logger = get_logger("qualia.circuits")

# Imports para a nova interface de encoding e encoders
try:
    from ..core.encoders import (
        QuantumEncodingInterface,
        PriceMomentumEncoder,
        VolatilityEncoder,
        VolumeRatioEncoder,  # P-3 CORREÇÃO: Adicionado import do VolumeRatioEncoder
        OBIEncoder,
        LiquidityVelocityEncoder,
        FundingRateDeviationEncoder,
        VolumeRatioAmplitudeEncoder,
        RSIPhaseEncoder,
        QuantumEncoder,
        MAX_CIRCUIT_DEPTH,
        MAX_CIRCUIT_OPERATIONS,
    )
except Exception as exc:  # pragma: no cover - degrade gracefully
    logger.warning("Failed to import encoders: %s", exc)

    class QuantumEncodingInterface:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class PriceMomentumEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class VolatilityEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class VolumeRatioEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class OBIEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class LiquidityVelocityEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class FundingRateDeviationEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class VolumeRatioAmplitudeEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class RSIPhaseEncoder:  # type: ignore
        def __init__(self, *a, **k):
            pass

    class QuantumEncoder:  # type: ignore
        pass

    MAX_CIRCUIT_DEPTH = 32
    MAX_CIRCUIT_OPERATIONS = 64

# Adicionado para type hinting se não estiver globalmente visível para
# Optional[QuantumCircuit]


def limit_depth(circuit: QuantumCircuit, max_depth: int) -> QuantumCircuit:
    """Return a new circuit truncated so its depth does not exceed ``max_depth``.

    The function removes operations from the end of ``circuit`` until the
    resulting depth is within the allowed limit.

    Parameters
    ----------
    circuit : QuantumCircuit
        Circuit to be trimmed.
    max_depth : int
        Maximum allowed circuit depth.

    Returns
    -------
    QuantumCircuit
        The trimmed circuit with depth less than or equal to ``max_depth``.
    """

    trimmed = circuit.copy()
    while trimmed.depth() > max_depth and trimmed.data:
        trimmed.data.pop()
    return trimmed


# Importar módulos da arquitetura QUALIA (com fallback)
try:
    # from ..core.unified.qualia_universe import
    # QUALIAQuantumUniverse # Caminho antigo para arquivo deletado
    from ..core.universe import QUALIAQuantumUniverse  # Caminho corrigido
    from ..core.consciousness import QUALIAConsciousness

    QUALIA_CORE_AVAILABLE = True
    logger.info("Módulos principais QUALIA carregados com sucesso")
except ImportError:
    QUALIA_CORE_AVAILABLE = False
    logger.warning("Módulos principais QUALIA não disponíveis, usando modo alternativo")


class QuantumMetricsCalculator:
    """
    Calculadora de métricas quânticas para trading usando o QUALIA Universe.

    Esta classe integra dados do mercado com o sistema QUALIA para calcular
    métricas quânticas avançadas que capturam dinâmicas de mercado complexas.
    """

    def __init__(
        self,
        qualia_universe: QUALIAQuantumUniverse,
        config: Optional[Dict[str, Any]] = None,
        qualia_consciousness_instance: Optional[QUALIAConsciousness] = None,
        exchange: Optional[Any] = None,
    ) -> None:
        """
        Inicializa a calculadora de métricas quânticas.

        Args:
            qualia_universe: Referência ao objeto QUALIAQuantumUniverse.
            config: Configurações adicionais, por exemplo, para encoders e mapeamento de qubits.
            qualia_consciousness_instance: Instância de QUALIAConsciousness associada ao QMC.
        """
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                f"QMC __init__: Configuração recebida (preview): {str(config)[:500]}..."
            )  # YAA: Log config recebida
        self.qualia_universe = qualia_universe
        self.consciousness = qualia_consciousness_instance
        self.thermal_coefficient = 0.1  # Pode ser sobrescrito por config
        self.last_market_state = None
        self.exchange = exchange
        self.config = config if config is not None else {}
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                f"QMC __init__: self.config atribuído (preview): {str(self.config)[:500]}..."
            )  # YAA: Log self.config

        scr_depth_cfg = self.config.get("scr_depth")
        ent_style_cfg = self.config.get("entanglement_style")
        if (scr_depth_cfg is not None) or (ent_style_cfg is not None):
            if hasattr(self.qualia_universe, "reconfigure"):
                self.qualia_universe.reconfigure(
                    **{
                        **(
                            {"scr_depth": scr_depth_cfg}
                            if scr_depth_cfg is not None
                            else {}
                        ),
                        **(
                            {"entanglement_style": ent_style_cfg}
                            if ent_style_cfg is not None
                            else {}
                        ),
                    }
                )
                logger.info(
                    "QMC: Universo reconfigurado via config inicial (scr_depth=%s, entanglement_style=%s)",
                    scr_depth_cfg,
                    ent_style_cfg,
                )

        # Atributos de configuração frequentemente utilizados em outros métodos
        # Alguns testes esperam que esses atributos existam mesmo quando ausentes
        # no dicionário de configuração fornecido.
        self.trading_symbols_raw = self.config.get("trading_symbols", [])
        self.symbol_normalization_map: Dict[str, str] = {}
        self.trading_symbols: List[str] = []
        if self.trading_symbols_raw and self.exchange:
            for sym in self.trading_symbols_raw:
                try:
                    norm = normalize_symbol(sym, self.exchange)
                    if norm != sym:
                        logger.warning("QMC: simbolo %s normalizado para %s", sym, norm)
                    self.symbol_normalization_map[sym] = norm
                    self.trading_symbols.append(norm)
                except ValueError as exc:
                    logger.warning("QMC: simbolo invalido %s: %s", sym, exc)
        else:
            self.trading_symbols = list(self.trading_symbols_raw)

        self.trading_primary_timeframe = self.config.get(
            "trading_primary_timeframe", ""
        )
        self.trading_timeframes = self.config.get("trading_timeframes", [])

        self._circuit_cache: Dict[str, QuantumCircuit] = {}
        self._transpiled_cache: Dict[
            str, Tuple[QuantumCircuit, Dict[str, List[int]]]
        ] = {}  # YAA: Cache para circuitos transpilados + mapeamento
        self._last_encoder_qubit_mapping: Dict[str, List[int]] = (
            {}
        )  # YAA: Para armazenar o mapeamento do último circuito de batch
        self.invalid_encoders: Dict[str, str] = (
            {}
        )  # Para registrar encoders inválidos e respectivas causas
        self.warned_timeframe_mismatch_encoders: Set[str] = set()

        self.shots = self.config.get("shots", 1024)
        self.qpu_steps = self.config.get(
            "qpu_steps", 10
        )  # Uso específico do QMC/Universo, não direto no backend.run
        self.thermal_noise_enabled = self.config.get(
            "thermal_noise", False
        )  # Bool para habilitar
        self.thermal_noise_temperature = self.config.get(
            "temperature", 0.0
        )  # Temperatura para o modelo de ruído
        self.measure_frequency = self.config.get(
            "measure_frequency", 1
        )  # Default: medir a cada passo/ciclo do universo

        self.convert_mismatched_timeframes = self.config.get(
            "convert_mismatched_timeframes", False
        )

        logger.info(
            f"QMC Qiskit execution params: shots={self.shots}, thermal_noise_enabled={self.thermal_noise_enabled}, thermal_noise_temperature={self.thermal_noise_temperature}, qpu_steps={self.qpu_steps}, measure_frequency={self.measure_frequency}"
        )

        # YAA: Obter a lista de configurações de encoders. Pode vir de 'encoders' ou 'encoder_config_list'.
        # Priorizar 'encoders' se existir, senão 'encoder_config_list'.
        encoders_config_list_from_config = self.config.get(
            "encoders", self.config.get("encoder_config_list", [])
        )

        if not encoders_config_list_from_config:
            logger.warning(
                f"QMC (ID: {id(self)}): Nenhuma configuração de encoder ('encoders' ou 'encoder_config_list') encontrada no config fornecido. "
                f"Gerando encoder default 'PriceMomentum_Primary' para o timeframe primário '{self.trading_primary_timeframe}' e símbolo(s) '{self.trading_symbols}'."
            )
            # Gerar configuração default para PriceMomentum_Primary
            # Esta estrutura deve ser compatível com o que _configure_encoders_from_list espera.
            # Cada símbolo terá seu próprio grupo de encoders default.
            default_encoders_config = []
            for symbol_name in self.trading_symbols:
                # O group_id pode ser o nome do símbolo para manter a estrutura esperada
                # ou um nome genérico se os defaults são globais para todos os símbolos.
                # Vamos criar um grupo por símbolo para consistência com a estrutura original.
                group_id_default = f"default_encoders_{symbol_name.replace('/', '_')}"

                # Criar o encoder default para o timeframe primário
                primary_tf_encoder_name = f"PM_{symbol_name.replace('/', '_')}_{self.trading_primary_timeframe}"
                default_primary_tf_encoder_entry = {
                    "name": primary_tf_encoder_name,
                    "class": "PriceMomentum_Primary",  # Usar o alias da classe como string
                    "params": {
                        "data_keys": [
                            f"{symbol_name}_{self.trading_primary_timeframe}_price_change"
                        ],
                        "n_qubits_feature_group": 1,  # Exemplo, PriceMomentum_Primary pode definir isso
                        "primary_symbol": symbol_name,
                        "primary_timeframe": self.trading_primary_timeframe,
                        # Adicionar quaisquer outros params default necessários para PriceMomentum_Primary
                    },
                    "timeframes": [
                        self.trading_primary_timeframe
                    ],  # Encoder para o timeframe primário
                }

                # Adicionar à lista de configuração de encoders
                # A estrutura esperada por _configure_encoders_from_list é uma lista de dicts,
                # onde cada dict tem 'group_id' e 'encoders_in_group'.
                default_encoders_config.append(
                    {
                        "group_id": group_id_default,
                        "encoders_in_group": [default_primary_tf_encoder_entry],
                    }
                )

            # Substituir a lista de configuração de encoders pela default gerada
            encoders_config_list = default_encoders_config
            # Log para informar que a configuração default será usada
            if logger.isEnabledFor(logging.INFO):
                logger.info(
                    "QMC (ID: %s): Utilizando configuração de encoder default gerada: %s",
                    id(self),
                    json.dumps(encoders_config_list, indent=2),
                )
        else:
            # Usar a configuração fornecida
            encoders_config_list = encoders_config_list_from_config
            logger.info(
                f"QMC (ID: {id(self)}): Encontrada configuração de encoders no arquivo. {len(encoders_config_list)} grupo(s) de encoders."
            )
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(
                    "QMC (ID: %s): Config de encoders fornecida: %s",
                    id(self),
                    json.dumps(encoders_config_list, indent=2),
                )

        # YAA: CORREÇÃO - Atribuir a variável local ao atributo da instância
        self.encoders_config_list = encoders_config_list

        # Garantir que os data_keys dos encoders estejam alinhados ao timeframe primário
        self._preprocess_encoder_timeframes()

        # Inicializar a interface de encoding quântico com o n_qubits do universo
        self.quantum_encoding_interface = QuantumEncodingInterface(
            # n_qubits=self.qualia_universe.n_qubits # Removido
        )
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                "QMC __init__: QuantumEncodingInterface instanciada."
            )  # YAA: Log instanciação QEI

        self._configure_encoders_from_list()  # YAA: Método para configurar encoders a partir da lista

        # Inicializar o universo QUALIA se disponível (o universo já é passado diretamente)
        self._initialize_qualia_components()
        if (
            self.qualia_universe
        ):  # Atualizar o universo com o thermal_coefficient daqui se necessário
            # Assumindo que QUALIAQuantumUniverse tem um atributo thermal
            self.qualia_universe.thermal = self.thermal_coefficient > 0
            # Se QUALIAQuantumUniverse.__init__ já lida com
            # thermal_coefficient, isso pode não ser necessário aqui.

        logger.info(
            f"QuantumMetricsCalculator inicializado, usará {self.qualia_universe.n_qubits} qubits do universo fornecido. Thermal Coeff: {self.thermal_coefficient}"
        )

    _encoder_class_map = {
        "PriceMomentumEncoder": PriceMomentumEncoder,
        "VolatilityEncoder": VolatilityEncoder,
        "VolumeRatioEncoder": VolumeRatioEncoder,  # P-3 CORREÇÃO: Adicionado VolumeRatioEncoder
        "OBIEncoder": OBIEncoder,
        "LiquidityVelocityEncoder": LiquidityVelocityEncoder,
        "FundingRateDeviationEncoder": FundingRateDeviationEncoder,
        "RSIPhaseEncoder": RSIPhaseEncoder,
        "VolumeRatioAmplitudeEncoder": VolumeRatioAmplitudeEncoder,
        # Adicione outros encoders aqui conforme são implementados e necessários
    }

    def _preprocess_encoder_timeframes(self) -> None:
        """Validate encoder data_keys against ``trading_primary_timeframe``.

        When ``convert_mismatched_timeframes`` is enabled, data_keys found with
        a different timeframe are rewritten to use the primary timeframe and a
        single warning is logged summarizing the affected encoders.
        """

        primary = self.trading_primary_timeframe
        if not primary or not isinstance(self.encoders_config_list, list):
            return

        mismatched: List[str] = []

        for group in self.encoders_config_list:
            if not isinstance(group, dict):
                continue
            encoders = group.get("encoders")
            if encoders is None:
                encoders = group.get("encoders_in_group", [])
            for enc in encoders:
                params = enc.get("params", {}) if isinstance(enc, dict) else {}
                data_keys = params.get("data_keys", [])
                if isinstance(data_keys, str):
                    data_keys = [data_keys]

                updated_keys = []
                mismatch_found = False
                for dk in data_keys:
                    parts = dk.split("_")
                    if len(parts) >= 3 and parts[1] != primary:
                        mismatch_found = True
                        if self.convert_mismatched_timeframes:
                            parts[1] = primary
                            dk = "_".join(parts)
                    updated_keys.append(dk)

                if mismatch_found:
                    mismatched.append(enc.get("name", "unknown"))
                    if self.convert_mismatched_timeframes:
                        params["data_keys"] = updated_keys

        if mismatched and self.convert_mismatched_timeframes:
            logger.warning(
                "QMC: convertendo timeframe de %d encoder(s) para '%s' ao carregar qmc_config",
                len(mismatched),
                primary,
            )

    def _configure_encoders_from_list(self):
        """
        Configura e instancia os encoders quânticos a partir da lista de configuração self.encoders_config_list.
        Os encoders são então registrados na QuantumEncodingInterface.
        """
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                f"QMC _configure_encoders_from_list: Iniciando. self.encoders_config_list (type: {type(self.encoders_config_list)}): {self.encoders_config_list}"
            )  # YAA: Log no início da função

        if (
            not hasattr(self, "quantum_encoding_interface")
            or self.quantum_encoding_interface is None
        ):
            logger.error(
                "QMC: QuantumEncodingInterface não está configurada internamente antes de _configure_encoders_from_list. Isso não deveria acontecer."
            )
            return

        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                f"QMC: (Ref) Iniciando _configure_encoders_from_list. Configuração de encoders recebida: {self.encoders_config_list}"
            )

        primary_timeframe = self.trading_primary_timeframe

        self.quantum_encoding_interface.clear_encoders()
        self.invalid_encoders.clear()
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug(
                "QMC _configure_encoders_from_list: quantum_encoding_interface.clear_encoders() chamado."
            )

        if not self.encoders_config_list:
            logger.warning(
                "QMC: _configure_encoders_from_list - Nenhuma configuração de encoder fornecida ou self.encoders_config_list não é uma lista ou está vazia."
            )
            return

        logger.info(
            f"QMC _configure_encoders_from_list: Processando {len(self.encoders_config_list)} grupo(s) de configuração de encoders."
        )

        for group_idx, config_group in enumerate(self.encoders_config_list):
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(
                    f"QMC _configure_encoders_from_list: Processando grupo {group_idx}"
                )
            if not isinstance(config_group, dict):
                logger.warning(
                    f"QMC: Item na lista de configuração de encoders (grupo {group_idx}) não é um dicionário: {config_group}. Pulando."
                )
                continue

            encoders_in_group = config_group.get("encoders")
            if encoders_in_group is None:
                encoders_in_group = config_group.get("encoders_in_group", [])
            group_id = config_group.get("id", f"default_group_{group_idx}")
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(
                    f"QMC: Processando grupo de configuração '{group_id}'. Encoders encontrados neste grupo: {len(encoders_in_group)}"
                )

            for entry_idx, encoder_config_entry in enumerate(encoders_in_group):
                if logger.isEnabledFor(logging.DEBUG):
                    logger.debug(
                        f"QMC _configure_encoders_from_list: Processando entrada {entry_idx} no grupo '{group_id}'"
                    )
                if not isinstance(encoder_config_entry, dict):
                    logger.warning(
                        f"QMC: Entrada de configuração de encoder (grupo '{group_id}', entrada {entry_idx}) não é um dicionário: {encoder_config_entry}. Pulando."
                    )
                    continue

                encoder_name_in_config = encoder_config_entry.get("name")
                encoder_class_name = encoder_config_entry.get("class")
                encoder_instance_id = (
                    f"{group_id}_{encoder_name_in_config or f'idx{entry_idx}'}"
                )

                # YAA: Adaptar data_keys ao timeframe primário, se configurado
                params_for_encoder_class = encoder_config_entry.get(
                    "params", {}
                ).copy()  # Usar .copy() para modificar
                original_data_keys = params_for_encoder_class.get("data_keys", [])

                symbol_map = getattr(self, "symbol_normalization_map", {})
                if symbol_map and original_data_keys:
                    updated_keys = []
                    for dk in (
                        original_data_keys
                        if isinstance(original_data_keys, list)
                        else [original_data_keys]
                    ):
                        new_key = dk
                        for raw, norm in symbol_map.items():
                            if raw in new_key:
                                new_key = new_key.replace(raw, norm)
                        updated_keys.append(new_key)
                    if isinstance(original_data_keys, list):
                        params_for_encoder_class["data_keys"] = updated_keys
                    else:
                        params_for_encoder_class["data_keys"] = updated_keys[0]

                if params_for_encoder_class.get(
                    "use_primary_timeframe_for_data_keys", False
                ):
                    primary_tf = self.config.get("trading_primary_timeframe")
                    original_tf_in_keys = params_for_encoder_class.get(
                        "original_timeframe_in_data_keys"
                    )

                    if primary_tf and original_tf_in_keys and original_data_keys:
                        rewritten_data_keys = []
                        for dk_template in original_data_keys:
                            if f"_{original_tf_in_keys}_" in dk_template:
                                rewritten_data_keys.append(
                                    dk_template.replace(
                                        f"_{original_tf_in_keys}_", f"_{primary_tf}_"
                                    )
                                )
                            else:
                                rewritten_data_keys.append(
                                    dk_template
                                )  # Manter original se o placeholder não for encontrado

                        if rewritten_data_keys != original_data_keys:
                            logger.info(
                                f"QMC: Encoder '{group_id}_{encoder_config_entry.get('name', '')}': Adaptando data_keys do original '{original_tf_in_keys}' para o primário '{primary_tf}'. "
                                f"Originais: {original_data_keys}, Reescritas: {rewritten_data_keys}"
                            )
                            params_for_encoder_class["data_keys"] = rewritten_data_keys
                        else:
                            logger.debug(
                                f"QMC: Encoder '{group_id}_{encoder_config_entry.get('name', '')}': use_primary_timeframe_for_data_keys=True, mas nenhuma substituição de timeframe foi feita para '{original_tf_in_keys}' -> '{primary_tf}'. Verifique data_keys e original_timeframe_in_data_keys."
                            )
                    elif (
                        original_data_keys
                    ):  # Logar apenas se havia data_keys para reescrever
                        logger.warning(
                            f"QMC: Encoder '{group_id}_{encoder_config_entry.get('name', '')}': use_primary_timeframe_for_data_keys=True, mas falta primary_tf ('{primary_tf}') ou original_timeframe_in_data_keys ('{original_tf_in_keys}'). Data_keys não foram adaptadas."
                        )
                # Fim da adaptação YAA

                data_keys_for_tf_check = params_for_encoder_class.get("data_keys", [])
                if isinstance(data_keys_for_tf_check, str):
                    data_keys_for_tf_check = [data_keys_for_tf_check]

                encoder_timeframes = []
                for dk in data_keys_for_tf_check:
                    parts = dk.split("_")
                    if len(parts) >= 3:
                        encoder_timeframes.append(parts[1])

                if (
                    primary_timeframe
                    and encoder_timeframes
                    and any(tf != primary_timeframe for tf in encoder_timeframes)
                ):
                    if (
                        encoder_instance_id
                        not in self.warned_timeframe_mismatch_encoders
                    ):
                        logger.warning(
                            "QMC: Encoder '%s' timeframe %s difere do trader '%s'.",
                            encoder_instance_id,
                            encoder_timeframes,
                            primary_timeframe,
                        )
                        self.warned_timeframe_mismatch_encoders.add(encoder_instance_id)

                    if self.convert_mismatched_timeframes:
                        converted_keys = []
                        for dk in data_keys_for_tf_check:
                            parts = dk.split("_")
                            if len(parts) >= 3:
                                parts[1] = primary_timeframe
                                converted_keys.append("_".join(parts))
                            else:
                                converted_keys.append(dk)
                        params_for_encoder_class["data_keys"] = converted_keys
                    else:
                        logger.info(
                            f"QMC: Pulando encoder '{encoder_instance_id}' por mismatch de timeframe {encoder_timeframes}"
                        )
                        continue

                # Filtrar encoders por timeframes solicitados
                allowed_timeframes = self.config.get("trading_timeframes", [])
                # data_keys agora vêm de params_for_encoder_class que pode ter sido modificado
                data_keys = params_for_encoder_class.get("data_keys", [])
                timeframes_in_keys = []
                for dk in data_keys:
                    parts = dk.split("_")
                    if len(parts) >= 3:
                        timeframes_in_keys.append(parts[1])
                if allowed_timeframes and any(
                    tf not in allowed_timeframes for tf in timeframes_in_keys
                ):
                    logger.info(
                        f"QMC: Encoder '{group_id}_{encoder_config_entry.get('name', '')}' contém timeframes {timeframes_in_keys} não solicitados {allowed_timeframes}. Pulando encoder."
                    )
                    continue

                logger.debug(
                    f"QMC _configure_encoders_from_list: Config lida - Nome: {encoder_name_in_config}, Classe: {encoder_class_name}, Params: {params_for_encoder_class}"
                )

                if not encoder_name_in_config or not encoder_class_name:
                    logger.warning(
                        f"QMC: Configuração de encoder inválida (sem nome ou classe): {encoder_config_entry} (grupo '{group_id}', entrada {entry_idx}). Pulando."
                    )
                    continue

                EncoderClass = self._encoder_class_map.get(encoder_class_name)
                if not EncoderClass:
                    logger.warning(
                        f"QMC: Classe de encoder desconhecida '{encoder_class_name}' para '{encoder_instance_id}'. Pulando."
                    )
                    self.invalid_encoders[encoder_instance_id] = "classe_desconhecida"
                    continue

                logger.debug(
                    f"QMC _configure_encoders_from_list: Classe Encoder '{EncoderClass.__name__}' encontrada para '{encoder_instance_id}'."
                )

                logger.debug(
                    f"QMC: Tentando instanciar encoder '{encoder_instance_id}' da classe '{encoder_class_name}' com params: {params_for_encoder_class}"
                )
                try:
                    current_params_for_encoder_class = {
                        **params_for_encoder_class,
                        "name": encoder_instance_id,
                    }
                    logger.debug(
                        f"QMC _configure_encoders_from_list: Parâmetros finais para instanciação: {current_params_for_encoder_class}"
                    )

                    encoder_instance = EncoderClass(**current_params_for_encoder_class)
                    logger.info(
                        f"QMC: Encoder '{encoder_instance_id}' (nome interno: {encoder_instance.name}) instanciado com sucesso."
                    )

                    logger.debug(
                        f"QMC _configure_encoders_from_list: Chamando quantum_encoding_interface.add_encoder() para '{encoder_instance.name}'"
                    )
                    self.quantum_encoding_interface.add_encoder(encoder_instance)
                    logger.info(
                        f"QMC: Encoder '{encoder_instance.name}' adicionado à QuantumEncodingInterface. Contagem atual na interface: {len(self.quantum_encoding_interface.get_all_encoders_list())}"
                    )

                    raw_target_qubits = getattr(encoder_instance, "target_qubits", None)
                    if raw_target_qubits is None:
                        raw_target_qubits = params_for_encoder_class.get(
                            "target_qubits"
                        )

                    processed_target_qubits = []
                    if isinstance(raw_target_qubits, list) and all(
                        isinstance(q, int) for q in raw_target_qubits
                    ):
                        processed_target_qubits = raw_target_qubits
                    elif isinstance(raw_target_qubits, int):
                        processed_target_qubits = [raw_target_qubits]
                    else:
                        logger.warning(
                            f"Encoder '{encoder_instance_id}': 'target_qubits' ({raw_target_qubits}) não é uma lista de inteiros ou um inteiro. Não é possível validar dimensionalidade."
                        )
                        self.invalid_encoders[encoder_instance_id] = (
                            "target_qubits_invalido"
                        )
                        self.quantum_encoding_interface.remove_encoder(
                            encoder_instance.name
                        )
                        continue

                    if not processed_target_qubits:
                        logger.warning(
                            f"Encoder '{encoder_instance_id}': 'target_qubits' resultou em lista vazia. Encoder não pode ser usado."
                        )
                        self.invalid_encoders[encoder_instance_id] = (
                            "target_qubits_vazio"
                        )
                        self.quantum_encoding_interface.remove_encoder(
                            encoder_instance.name
                        )
                        continue

                    max_qubit_index_needed_by_encoder = -1
                    valid_indices = True
                    for q_idx in processed_target_qubits:
                        if not isinstance(q_idx, int) or q_idx < 0:
                            logger.error(
                                f"Encoder '{encoder_instance_id}': target_qubits contém valor inválido ({q_idx}). Deve ser int >= 0."
                            )
                            valid_indices = False
                            break
                        if q_idx > max_qubit_index_needed_by_encoder:
                            max_qubit_index_needed_by_encoder = q_idx

                    if not valid_indices:
                        self.invalid_encoders[encoder_instance_id] = (
                            "target_qubits_com_valor_negativo_ou_nao_int"
                        )
                        self.quantum_encoding_interface.remove_encoder(
                            encoder_instance.name
                        )
                        continue

                    if (
                        self.qualia_universe
                        and self.qualia_universe.n_qubits is not None
                    ):
                        if (
                            max_qubit_index_needed_by_encoder
                            >= self.qualia_universe.n_qubits
                        ):
                            error_msg = (
                                f"Encoder '{encoder_instance_id}' (classe: {encoder_class_name}) requer qubit "
                                f"{max_qubit_index_needed_by_encoder}, mas o universo QUALIA tem apenas "
                                f"{self.qualia_universe.n_qubits} qubits disponíveis (0 a {self.qualia_universe.n_qubits - 1})."
                            )
                            logger.error(error_msg)
                            self.invalid_encoders[encoder_instance_id] = (
                                "dimensionalidade_excedida"
                            )
                            self.quantum_encoding_interface.remove_encoder(
                                encoder_instance.name
                            )
                            continue
                    else:
                        logger.warning(
                            f"QMC: Universo Qualia ou n_qubits não disponível durante a configuração do encoder '{encoder_instance_id}'. Não foi possível validar dimensionalidade."
                        )

                except Exception as e_instantiate:
                    logger.error(
                        f"QMC: Erro ao instanciar ou configurar encoder '{encoder_instance_id}': {e_instantiate}",
                        exc_info=True,
                    )
                    self.invalid_encoders[encoder_instance_id] = str(e_instantiate)
                    continue
            # Fim do loop for entry_idx
        # Fim do loop for group_idx

    def _initialize_qualia_components(self):
        """Inicializa os componentes do QUALIA necessários para cálculos quânticos."""
        if not QUALIA_CORE_AVAILABLE:
            logger.warning(
                "Impossível inicializar componentes QUALIA: módulos não disponíveis"
            )
            return False

        if self.consciousness is None:
            logger.warning(
                "QMC: Instância de QUALIAConsciousness não foi fornecida ao construtor. Alguns processamentos QUALIA podem não funcionar."
            )

        # Configurar QAST (se aplicável e se self.consciousness existir)
        if self.consciousness and hasattr(self.consciousness, "configure_qast"):
            self.consciousness.configure_qast()
            logger.info("QAST configurado com sucesso")

        logger.info("Componentes QUALIA inicializados com sucesso")
        return True

    def set_thermal_coefficient(self, coefficient: float):
        """
        Define o coeficiente térmico para os cálculos quânticos.

        Args:
            coefficient: Valor entre 0 e 1 representando o nível de "ruído térmico"
        """
        self.thermal_coefficient = coefficient
        # Atualizar o universo se já inicializado
        if self.qualia_universe:
            self.qualia_universe.thermal = self.thermal_coefficient > 0
            # Assumir que o universo tem como lidar com a mudança de
            # thermal_coefficient
        logger.info(f"QMC Thermal coefficient atualizado para: {coefficient}")

    def set_trading_primary_timeframe(self, new_timeframe: str) -> None:
        """Atualiza o timeframe primário usado pelos encoders.

        A mudança de timeframe influencia quais chaves de dados são
        buscadas na etapa de encoding. Reconfiguramos os encoders para que os
        módulos de métrica passem a receber informações no novo timeframe.

        Args:
            new_timeframe: Novo timeframe (por exemplo, ``"5m"``).
        """

        if not new_timeframe or new_timeframe == self.trading_primary_timeframe:
            return

        logger.info(
            f"QMC: Atualizando trading_primary_timeframe de {self.trading_primary_timeframe} para {new_timeframe}."
        )

        self.trading_primary_timeframe = new_timeframe
        self.config["trading_primary_timeframe"] = new_timeframe

        if new_timeframe not in self.trading_timeframes:
            self.trading_timeframes.append(new_timeframe)
            self.config["trading_timeframes"] = self.trading_timeframes

        # Reconstrói os encoders com o novo timeframe aplicado nos data_keys
        self._configure_encoders_from_list()

    def encode_market_data(
        self,
        market_data: Dict[str, Any],
        additional_features: Optional[
            Dict[str, Any]
        ] = None,  # YAA: Adicionado novo argumento
    ) -> Dict[str, Any]:
        """
        Codifica os dados de mercado brutos em um formato de percepção quântica.
        YAA: Agora aceita 'additional_features' para injetar features pré-calculadas.
        """
        perception_data: Dict[str, Any] = {
            "timestamp": datetime.now(timezone.utc).timestamp(),
            "metrics": {},  # Métricas por símbolo/timeframe
            "metrics_flat": {},  # Métricas achatadas para fácil acesso por encoders
            "price_changes": [],  # Lista de todas as mudanças de preço para análise de tendência
            "patterns": [],  # Padrões detectados
            "raw_market_data_refs": list(
                market_data.keys()
            ),  # Referência aos símbolos/timeframes originais
        }

        try:
            # YAA: Fundir additional_features em metrics_flat primeiro, se fornecidas
            if additional_features:
                for key, value in additional_features.items():
                    # Garantir que a chave já tenha o formato esperado pelos encoders (ex: SYMBOL_TIMEFRAME_feature)
                    perception_data["metrics_flat"][key] = value
                logger.debug(
                    f"QMC encode_market_data: Fused {len(additional_features)} additional_features into metrics_flat. Keys: {list(additional_features.keys())}"
                )

            # A lógica original de cálculo de features do QMC pode servir como fallback
            # ou ser removida se confiarmos inteiramente nas additional_features para as chaves relevantes.
            # Por agora, vamos manter a lógica original, mas as additional_features terão prioridade
            # se houver colisão de chaves, pois são adicionadas primeiro.
            # (Na verdade, o .update() abaixo sobrescreveria, então vamos colocar a lógica original primeiro)

            for symbol, tf_data in market_data.items():
                for timeframe, df in tf_data.items():
                    if df is not None and not df.empty:
                        close_prices = df["close"].dropna()
                        if len(close_prices) >= 2:
                            last_price = close_prices.iloc[-1]
                            prev_price = close_prices.iloc[-2]
                            price_change = (
                                (last_price - prev_price) / prev_price
                                if prev_price != 0
                                else 0
                            )

                            key_symbol_tf = f"{symbol}_{timeframe}"  # ex: BTC/USDT_1h

                            # Adicionar à estrutura 'metrics' (mais detalhada)
                            if key_symbol_tf not in perception_data["metrics"]:
                                perception_data["metrics"][key_symbol_tf] = {}

                            perception_data["metrics"][key_symbol_tf][
                                "last_price"
                            ] = last_price
                            perception_data["metrics"][key_symbol_tf][
                                "price_change"
                            ] = price_change

                            # Adicionar à estrutura 'metrics_flat' (para encoders)
                            # Só adiciona se não foi já fornecido por additional_features
                            # (ou seja, se additional_features é None ou não contém a chave)
                            # Esta lógica garante que additional_features têm prioridade se as chaves colidirem.
                            # No entanto, com a fusão no início, additional_features já estão lá.
                            # A lógica original abaixo pode ser mantida para features que *não* são parte das
                            # additional_features esperadas (price_change, volatility, volume_ratio).

                            # Feature: price_change (calculada internamente como fallback)
                            flat_key_price_change = f"{key_symbol_tf}_price_change"
                            if (
                                flat_key_price_change
                                not in perception_data["metrics_flat"]
                            ):
                                perception_data["metrics_flat"][
                                    flat_key_price_change
                                ] = price_change

                            # Feature: last_price (calculada internamente)
                            flat_key_last_price = f"{key_symbol_tf}_last_price"
                            if (
                                flat_key_last_price
                                not in perception_data["metrics_flat"]
                            ):
                                perception_data["metrics_flat"][
                                    flat_key_last_price
                                ] = last_price

                            perception_data["price_changes"].append(price_change)

                            # Feature: volatility (calculada internamente como fallback)
                            flat_key_volatility = f"{key_symbol_tf}_volatility"
                            if (
                                flat_key_volatility
                                not in perception_data["metrics_flat"]
                            ):
                                if (
                                    len(close_prices) >= 5
                                ):  # Lógica original de volatilidade do QMC
                                    sma = np.mean(close_prices[-5:])
                                    std_dev = np.std(close_prices[-5:])
                                    volatility = std_dev / sma if sma != 0 else 0
                                    perception_data["metrics"][key_symbol_tf][
                                        "volatility"
                                    ] = volatility
                                    perception_data["metrics_flat"][
                                        flat_key_volatility
                                    ] = volatility
                                elif (
                                    len(close_prices) > 1
                                ):  # Um fallback mais simples para volatilidade
                                    volatility = close_prices.pct_change().std()
                                    perception_data["metrics"][key_symbol_tf][
                                        "volatility"
                                    ] = (volatility if pd.notna(volatility) else 0.0)
                                    perception_data["metrics_flat"][
                                        flat_key_volatility
                                    ] = (volatility if pd.notna(volatility) else 0.0)

            # Detecção básica de padrões (apenas como demonstração)
            if len(perception_data["price_changes"]) > 0:
                avg_change = np.mean(perception_data["price_changes"])
                if avg_change > 0.02:
                    perception_data["patterns"].append("bullish_trend")
                elif avg_change < -0.02:
                    perception_data["patterns"].append("bearish_trend")
                else:
                    perception_data["patterns"].append("sideways_market")

            # Salvar estado de mercado para referência
            self.last_market_state = perception_data

        except Exception as e:
            logger.error(f"Erro ao codificar dados de mercado: {e}")

        return perception_data

    def _create_encoded_market_input(
        self, perception_data: Dict[str, Any]
    ) -> Optional[QuantumCircuit]:
        """
        Prepara o circuito quântico de entrada com base nos dados de percepção,
        utilizando a QuantumEncodingInterface para aplicar os encoders.

        Args:
            perception_data: Dicionário contendo os dados de percepção do mercado.

        Returns:
            Um QuantumCircuit pronto para ser transpilado e executado, ou None se ocorrer um erro.
        """
        # Registrar tempo de início para métricas de desempenho
        _start_time_encoding = time.perf_counter()

        # O número de qubits para o circuito de batch deve ser o número
        # de qubits do universo; mantido para documentação.
        _num_qubits_for_batch_circuit = self.qualia_universe.n_qubits

        # Recupera encoders ativos da interface
        active_encoders = self._get_active_encoders()
        if not active_encoders:
            logger.debug(
                "QMC _create_encoded_market_input: nenhum encoder ativo encontrado. Pulando geracao do circuito."
            )
            return None

        # Cache key generation
        active_encoder_names = sorted([enc.name for enc in active_encoders])

        # Apenas utilizar as features usadas pelos encoders ativos
        metrics_flat = perception_data.get("metrics_flat", {})
        relevant_features: Dict[str, Any] = {}
        for enc in active_encoders:
            data_keys = getattr(enc, "data_keys", [])
            if isinstance(data_keys, str):
                data_keys = [data_keys]
            for key in data_keys:
                if key in metrics_flat:
                    relevant_features[key] = metrics_flat[key]

        def make_hashable(data: Any) -> Any:
            if isinstance(data, dict):
                return frozenset((k, make_hashable(v)) for k, v in sorted(data.items()))
            if isinstance(data, list):
                return tuple(make_hashable(item) for item in data)
            return data

        hashable_features = make_hashable(relevant_features)
        cache_key_info = (
            hashable_features,
            tuple(active_encoder_names),
            self.qualia_universe.n_qubits,
            (
                self.qualia_universe.backend.name
                if self.qualia_universe and self.qualia_universe.backend
                else "default"
            ),
        )
        cache_key = hashlib.sha256(str(cache_key_info).encode()).hexdigest()

        # YAA: CORREÇÃO P-8 - Verificar cache de circuitos transpilados primeiro
        if cache_key in self._transpiled_cache:
            cached_transpiled_circuit, cached_encoder_mapping = self._transpiled_cache[
                cache_key
            ]
            logger.debug(
                f"QMC: Circuito transpilado encontrado no cache para key: {cache_key}"
            )
            self._last_encoder_qubit_mapping = cached_encoder_mapping
            return cached_transpiled_circuit

        # Se não está no cache ou o item do cache era inválido:
        # Cria o circuito de batch (não transpilado) e obtém o mapeamento de qubits
        (
            batch_circuit_to_transpile,
            encoder_qubit_mapping,
        ) = self.quantum_encoding_interface.encode_market_state(
            perception_data, self.qualia_universe.n_qubits
        )

        if not batch_circuit_to_transpile:
            logger.debug(
                "QMC: quantum_encoding_interface.encode_market_state não retornou um circuito."
            )
            return None

        # Transpilar o circuito de batch para o backend do universo
        if self.qualia_universe and self.qualia_universe.backend:
            try:
                start_time_transpile = time.time()
                logger.info(
                    f"QMC: Transpilando circuito de batch para o backend: {self.qualia_universe.backend.name}"
                )

                # Certificar que o backend está definido
                if (
                    not hasattr(self.qualia_universe, "backend")
                    or not self.qualia_universe.backend
                ):
                    logger.error(
                        "QMC: Backend do universo não está definido. Não é possível transpilar."
                    )
                    return None

                transpiled_qc = transpile(
                    batch_circuit_to_transpile, self.qualia_universe.backend
                )
                end_time_transpile = time.time()
                transpile_time = end_time_transpile - start_time_transpile
                logger.debug(
                    f"QMC: Circuito de batch transpilado com sucesso em {transpile_time:.4f}s."
                )
                # YAA: CORREÇÃO - Usar qiskit.qasm2.dumps() para obter a string QASM
                circuits_logger.debug(
                    "QMC: Circuito Transpilado: %s",
                    qasm2.dumps(transpiled_qc) if qasm2 else "<qasm unavailable>",
                )

                depth = transpiled_qc.depth()
                ops = transpiled_qc.size()
                if depth > MAX_CIRCUIT_DEPTH:
                    logger.info(
                        "Circuit depth %d excede o limite %d. Tentando nova transpilação com nível de otimização 1.",
                        depth,
                        MAX_CIRCUIT_DEPTH,
                    )
                    transpiled_qc = transpile(
                        batch_circuit_to_transpile,
                        self.qualia_universe.backend,
                        optimization_level=1,
                    )
                    transpiled_qc = limit_depth(transpiled_qc, MAX_CIRCUIT_DEPTH)
                    depth = transpiled_qc.depth()
                    ops = transpiled_qc.size()

                if depth > MAX_CIRCUIT_DEPTH or ops > MAX_CIRCUIT_OPERATIONS:
                    logger.warning(
                        "Circuito excede limites (depth=%d, ops=%d). Iniciando poda.",
                        depth,
                        ops,
                    )
                    transpiled_qc = self._prune_circuit(transpiled_qc)

                # Garantir limites homogêneos com o novo trim
                transpiled_qc = enforce_circuit_limits(transpiled_qc)

                # YAA: CORREÇÃO P-8 - Salvar no cache de transpilados com mapeamento
                self._transpiled_cache[cache_key] = (
                    transpiled_qc,
                    encoder_qubit_mapping,
                )
                self._last_encoder_qubit_mapping = encoder_qubit_mapping
                logger.debug(
                    f"QMC: Circuito transpilado salvo no cache para key: {cache_key}"
                )
                return transpiled_qc

            except Exception as e:
                logger.error(
                    f"QMC: Falha ao transpilar o circuito de batch: {e}", exc_info=True
                )
                # Loggar o circuito original que falhou ao transpilar pode ser útil
                try:
                    circuits_logger.debug(
                        "QMC: Circuito original (não transpilado) que causou erro: %s",
                        (
                            qasm2.dumps(batch_circuit_to_transpile)
                            if qasm2
                            else "<qasm unavailable>"
                        ),
                    )
                except Exception as dump_error:
                    logger.error(
                        f"QMC: Falha ao tentar converter para QASM o circuito original: {dump_error}"
                    )
                return None  # Falha na transpilação
        else:
            logger.error(
                "QMC: Universo Qualia ou backend do universo não disponível. Não é possível transpilar."
            )
            return None

    def _prune_circuit(self, circuit: QuantumCircuit) -> QuantumCircuit:
        """Reduce circuit depth and operation count within allowed limits."""

        pruned = circuit
        iterations = 0
        while (
            pruned.depth() > MAX_CIRCUIT_DEPTH or pruned.size() > MAX_CIRCUIT_OPERATIONS
        ) and iterations < 10:
            pruned = pruned.decompose()
            iterations += 1

        while (
            pruned.depth() > MAX_CIRCUIT_DEPTH or pruned.size() > MAX_CIRCUIT_OPERATIONS
        ):
            if not pruned.data:
                break
            pruned.data.pop()

        return pruned

    def _get_active_encoders(self) -> List[QuantumEncoder]:
        """
        Retorna uma lista de encoders ativos na interface de encoding quântico.

        Returns:
            Lista de encoders ativos
        """
        # YAA: Adicionando log detalhado aqui
        if not self.quantum_encoding_interface:
            logger.debug(
                "QMC _get_active_encoders: QuantumEncodingInterface não disponível (é None). Retornando lista vazia."
            )
            return []

        if not self.quantum_encoding_interface.has_encoders():
            logger.debug(
                "QMC _get_active_encoders: quantum_encoding_interface.has_encoders() retornou False. Retornando lista vazia."
            )
            return []

        active_encoders = self.quantum_encoding_interface.get_all_encoders_list()
        logger.debug(
            f"QMC _get_active_encoders: Retornando {len(active_encoders)} encoders da interface: {[enc.name for enc in active_encoders]}"
        )  # YAA: Log encoders retornados
        return active_encoders

    def process_with_qualia(self, perception_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Processa os dados de percepção usando o ciclo completo do QUALIA Universe e Consciousness.
        Modificado para injetar o circuito de mercado codificado.

        Args:
            perception_data: Dados de percepção do mercado já processados (inclui metrics_flat).

        Returns:
            Dicionário com métricas e resultados do processamento QUALIA.
        """
        if not self.qualia_universe or not self.consciousness:
            logger.warning(
                "QUALIA Universe ou Consciousness não estão disponíveis. Retornando métricas de fallback."
            )
            # Chamaria um _fallback_metrics(perception_data) aqui se existisse um método genérico de fallback
            # Por ora, retorna um dicionário indicando a falha mas com
            # estrutura esperada básica.
            return {
                "error": "QUALIA components not available",
                "quantum_entropy": 0.0,
                "coherence": 0.0,
                "qast_cycles": [],
                "final_metrics": {},
            }

        try:
            # 1. Criar o circuito inicial codificado com os dados de mercado
            initial_market_circuit = self._create_encoded_market_input(perception_data)
            if initial_market_circuit is None:
                if not self._get_active_encoders():
                    logger.info(
                        "QMC: nenhum encoder ativo - processamento QUALIA ignorado."
                    )
                    return {"warning": "no_active_encoders"}
                logger.error(
                    "Falha ao criar o circuito de mercado codificado. Abortando processamento QUALIA."
                )
                return {"error": "Failed to create encoded market circuit"}

            logger.info(
                f"Circuito de mercado inicial para o universo tem {initial_market_circuit.num_qubits} qubits, profundidade {initial_market_circuit.depth()}, {initial_market_circuit.size()} ops."
            )
            if (
                initial_market_circuit.num_qubits > 0
                and initial_market_circuit.depth() > 0
            ):
                circuits_logger.debug(
                    "QASM do circuito de mercado inicial:\n%s",
                    (
                        qasm2.dumps(initial_market_circuit)
                        if qasm2
                        else "<qasm unavailable>"
                    ),
                )

            # YAA: Ajuste dinâmico da frequência de medição do universo
            # Para corrigir QMC: setar measure_freq >= circuit depth
            if (
                initial_market_circuit.depth() > 0
            ):  # Considerar apenas se a profundidade é maior que 0
                circuit_depth = initial_market_circuit.depth()
                # Tenta obter a frequência de medição atual do universo.
                # Se o atributo não existir, assume um valor padrão (ex: 1) ou
                # loga um aviso.
                current_universe_measure_freq = getattr(
                    self.qualia_universe, "measure_frequency", 1
                )

                new_measure_freq = max(current_universe_measure_freq, circuit_depth)

                if new_measure_freq != current_universe_measure_freq:
                    logger.info(
                        f"Ajustando measure_frequency do universo de {current_universe_measure_freq} para {new_measure_freq} para acomodar profundidade do circuito ({circuit_depth})."
                    )
                    if hasattr(self.qualia_universe, "configure"):
                        self.qualia_universe.configure(
                            measure_frequency=new_measure_freq
                        )
                        logger.info(
                            f"Universe measure_frequency reconfigurado para {self.qualia_universe.measure_frequency} via configure()."
                        )
                    elif hasattr(self.qualia_universe, "measure_frequency"):
                        self.qualia_universe.measure_frequency = new_measure_freq
                        logger.info(
                            f"Universe measure_frequency atualizado diretamente para {self.qualia_universe.measure_frequency}."
                        )
                    else:
                        logger.warning(
                            "Não foi possível ajustar measure_frequency do universo: atributo/método não encontrado."
                        )

            # 2. Executar o universo QUALIA com o circuito de mercado inicial
            # O método run do universo deve internamente chamar build_circuit se necessário,
            # passando o initial_market_circuit.
            # Nota: A lógica exata de como o universo lida com `initial_market_circuit`
            # (se ele o combina, usa como está, etc.) está encapsulada no próprio universo.
            # O QUALIAQuantumUniverse.run foi projetado para aceitar initial_market_circuit.
            # YAA: Adicionado o argumento steps=self.universe.measure_frequency
            # Esta é a linha original que causa o TypeError:
            # universe_run_output = self.universe.run(initial_market_circuit=initial_market_circuit)
            # Correção:
            # YAA: Desempacotar a tupla retornada por universe.run()
            run_counts, run_metrics = self.qualia_universe.run(
                steps=self.config.get(
                    "qpu_steps", self.qualia_universe.measure_frequency
                ),
                initial_encoded_circuit=initial_market_circuit,
                shots=self.config.get("shots", 1024),
                thermal=self.config.get("thermal_noise", True),
                temperature=self.config.get("temperature", 0.01),
                retro_mode=self.config.get("retro_mode", "none"),
                measure_frequency=self.config.get("measure_frequency", 1),
                use_cache=False,
                max_execution_seconds=self.config.get("run_timeout", 30),
            )
            logger.info("Universo QUALIA executado com circuito de mercado inicial.")

            current_statevector_obj = self.qualia_universe.get_current_statevector()
            universe_metrics = run_metrics

            qast_results = self.consciousness.process_qast_cycle(
                statevector_from_universe=current_statevector_obj
            )
            logger.info("Ciclo QAST da Consciência QUALIA processado.")

            calculated_entropy = (
                self.consciousness.metrics.get("quantum_entropy", [])[-1]
                if self.consciousness.metrics.get("quantum_entropy")
                else None
            )
            calculated_coherence = (
                self.consciousness.metrics.get("coherence", [])[-1]
                if self.consciousness.metrics.get("coherence")
                else None
            )

            if calculated_entropy is not None and hasattr(
                self.qualia_universe, "add_external_metric"
            ):
                self.qualia_universe.add_external_metric(
                    "quantum_entropy", calculated_entropy
                )
                logger.info(
                    f"Quantum Entropy ({calculated_entropy:.4f}) da Consciência registrada no Universo."
                )

            if calculated_coherence is not None and hasattr(
                self.qualia_universe, "add_external_metric"
            ):
                self.qualia_universe.add_external_metric(
                    "quantum_coherence", calculated_coherence
                )
                logger.info(
                    f"Quantum Coherence ({calculated_coherence:.4f}) da Consciência registrada no Universo."
                )

            final_metrics_summary = {
                "universe_statevector_preview": (
                    str(current_statevector_obj.data)[:100] + "..."
                    if current_statevector_obj
                    and hasattr(current_statevector_obj, "data")
                    else None
                ),
                "qast_cycle_details": qast_results,
                "calculated_entropy_from_consciousness": calculated_entropy,
                "calculated_coherence_from_consciousness": calculated_coherence,
            }
            final_metrics_summary.update(universe_metrics)

            diversity_ratio = final_metrics_summary.get("counts_diversity_ratio")
            threshold = 0.05
            if hasattr(self.qualia_universe, "apply_random_scrambling") and (
                diversity_ratio is not None and diversity_ratio < threshold
            ):
                self.qualia_universe.apply_random_scrambling(depth=2)

            return {
                "status": "success",
                "qast_cycles": self.consciousness.state.get("qast_cycles", []),
                "final_metrics": final_metrics_summary,
                "raw_counts": run_counts,  # YAA: Adicionar run_counts
                "encoder_qubit_mapping": self._last_encoder_qubit_mapping,  # YAA: Adicionar mapeamento
            }

        except Exception as e:
            import traceback

            logger.error(
                f"Erro durante o processamento com QUALIA: {e}\n{traceback.format_exc()}"
            )
            return {
                "error": str(e),
                "traceback": traceback.format_exc(),
                "quantum_entropy": 0.0,  # Fallback values
                "coherence": 0.0,
                "qast_cycles": [],
                "final_metrics": {},
            }

    def calculate_metrics(
        self, market_data: Dict[str, Any], returns: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """
        Calcula métricas quânticas a partir dos dados de mercado usando QUALIA.

        Esta é a função principal que deve ser chamada para obter as métricas
        quânticas a partir dos dados do mercado.

        Args:
            market_data: Dicionário contendo dados do mercado
            returns: Array opcional de retornos para análise adicional (padrão: None)

        Returns:
            Dicionário com métricas quânticas calculadas
        """
        logger.info(
            "Iniciando cálculo de métricas quânticas com QUALIA (via process_with_qualia)"
        )  # YAA: Log ajustado
        start_time = time.time()

        results_header = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "n_qubits": self.qualia_universe.n_qubits,
            "thermal_coefficient": self.thermal_coefficient,
            "execution_time": 0,  # Será atualizado no final
        }

        try:
            if (
                not QUALIA_CORE_AVAILABLE
                or not self.qualia_universe
                or not self.consciousness
            ):
                logger.warning(
                    "QUALIA Core não disponível. Revertendo para modo alternativo."
                )
                # O _fallback_metrics já retorna um dicionário completo
                fallback_results = self._fallback_metrics(market_data)
                # Mescla com o cabeçalho
                results_header.update(fallback_results)
                results_header["qualia_mode"] = "fallback"
                results_header["execution_time"] = time.time() - start_time
                return results_header

            # 1. Processar dados de mercado para o formato de percepção
            perception_data = self.encode_market_data(market_data)

            # 2. Processar com QUALIA usando o método process_with_qualia
            qualia_processed_output = self.process_with_qualia(perception_data)

            # Mesclar o cabeçalho com a saída do processamento QUALIA
            results_header.update(qualia_processed_output)
            results_header["qualia_mode"] = (
                "active"
                if qualia_processed_output.get("status") == "success"
                else "error_in_qualia_processing"
            )
            results_header["execution_time"] = time.time() - start_time

            # YAA: Loggar a presença de raw_counts e encoder_qubit_mapping se existirem
            if "raw_counts" in qualia_processed_output:
                logger.debug(
                    f"QMC calculate_metrics: raw_counts (primeiras 10 chaves se >10): {list(qualia_processed_output['raw_counts'].keys())[:10] if isinstance(qualia_processed_output.get('raw_counts'), dict) else 'N/A ou não dict'}"
                )
            if "encoder_qubit_mapping" in qualia_processed_output:
                logger.debug(
                    f"QMC calculate_metrics: encoder_qubit_mapping: {qualia_processed_output['encoder_qubit_mapping']}"
                )

            # YAA: Extrair métricas por encoder a partir das contagens
            encoder_specific_metrics = {}
            if (
                isinstance(qualia_processed_output.get("raw_counts"), dict)
                and isinstance(
                    qualia_processed_output.get("encoder_qubit_mapping"), dict
                )
                and self.qualia_universe  # Garante que o universo e n_qubits estão disponíveis
                and self.qualia_universe.n_qubits is not None
            ):
                raw_counts_data = qualia_processed_output["raw_counts"]
                mapping_data = qualia_processed_output["encoder_qubit_mapping"]
                if raw_counts_data and mapping_data:  # Garantir que não estão vazios
                    encoder_specific_metrics = self._extract_encoder_metrics_from_counts(
                        raw_counts=raw_counts_data,
                        encoder_qubit_mapping=mapping_data,
                        num_total_qubits_in_circuit=self.qualia_universe.n_qubits,  # Passa n_qubits do universo
                    )
                    if encoder_specific_metrics:
                        results_header["per_encoder_metrics"] = encoder_specific_metrics
                        logger.info(
                            f"QMC calculate_metrics: Métricas por encoder extraídas: {list(encoder_specific_metrics.keys())}"
                        )
                        if logger.isEnabledFor(logging.DEBUG):
                            logger.debug(
                                "QMC calculate_metrics: Detalhes das métricas por encoder: %s",
                                json.dumps(encoder_specific_metrics, indent=2),
                            )

            logger.info(
                f"Métricas quânticas calculadas com QUALIA em {results_header['execution_time']:.2f}s"
            )
            return results_header

        except Exception as e:
            logger.error(f"Erro crítico ao calcular métricas quânticas com QUALIA: {e}")
            import traceback

            # Montar um dicionário de erro mais completo
            results_header["error"] = str(e)
            results_header["traceback"] = traceback.format_exc()
            results_header["qualia_mode"] = "critical_error_fallback"
            # Tentar modo alternativo mesmo em erro crítico
            try:
                fallback_results = self._fallback_metrics(market_data)
                # Mescla com o cabeçalho, pode sobrescrever 'error'
                results_header.update(fallback_results)
            except Exception as fe:
                logger.error(f"Erro também ao calcular métricas de fallback: {fe}")
                results_header["fallback_error"] = str(fe)

            results_header["execution_time"] = time.time() - start_time
            return results_header

    def calculate_market_metrics(
        self, market_data: Dict[str, Any], returns: Optional[np.ndarray] = None
    ) -> Dict[str, Any]:
        """
        Método de compatibilidade para chamar calculate_metrics.
        Alguns componentes legados chamam este método em vez de calculate_metrics.

        Args:
            market_data: Dicionário contendo dados do mercado
            returns: Array opcional de retornos para análise adicional (padrão: None)

        Returns:
            Dicionário com métricas quânticas calculadas
        """
        return self.calculate_metrics(market_data, returns=returns)

    def _fallback_metrics(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calcula métricas básicas quando o QUALIA não está disponível.

        Args:
            market_data: Dicionário contendo dados do mercado

        Returns:
            Dicionário com métricas básicas calculadas
        """
        logger.info(
            "Usando métricas de fallback devido à ausência de componentes QUALIA."
        )
        return {"fallback_metric": np.random.rand()}

    # YAA: Novo método para extrair métricas de encoders das contagens
    def _extract_encoder_metrics_from_counts(
        self,
        raw_counts: Dict[str, int],
        encoder_qubit_mapping: Dict[str, List[int]],
        num_total_qubits_in_circuit: int,
    ) -> Dict[str, Dict[str, float]]:
        """
        Extrai métricas para cada encoder (probabilidade de |1>, valor esperado)
        a partir das contagens brutas de um circuito de batch.

        Args:
            raw_counts: Dicionário de contagens (estado_binario -> contagem).
            encoder_qubit_mapping: Dicionário (nome_encoder -> lista_de_qubits_absolutos_usados).
            num_total_qubits_in_circuit: O número total de qubits no circuito cujas contagens foram fornecidas.

        Returns:
            Dicionário com métricas por encoder. Ex:
            {'EncoderA': {'p1': 0.3, 'p0': 0.7, 'expected_value': 0.4}, ...}
        """
        if (
            not raw_counts
            or not encoder_qubit_mapping
            or num_total_qubits_in_circuit <= 0
        ):
            logger.warning(
                "_extract_encoder_metrics_from_counts: Dados de entrada inválidos (contagens vazias, mapeamento vazio ou n_qubits <= 0). Retornando vazio."
            )
            return {}

        per_encoder_metrics: Dict[str, Dict[str, float]] = {}
        total_shots = sum(raw_counts.values())

        if total_shots == 0:
            logger.warning(
                "_extract_encoder_metrics_from_counts: Total de shots é 0. Não é possível calcular probabilidades. Retornando vazio."
            )
            return {}

        for encoder_name, mapped_qubit_indices in encoder_qubit_mapping.items():
            if not mapped_qubit_indices:
                logger.debug(
                    f"Encoder '{encoder_name}' não tem qubits mapeados. Pulando extração de métricas."
                )
                continue

            # Simplificação: Processar apenas o primeiro qubit mapeado para cada encoder.
            # Isso é adequado para encoders de 1 qubit, que é o caso comum para 'ry' com target_qubits=[0] relativo.
            # Para encoders multi-qubit, esta lógica precisaria ser expandida.
            if len(mapped_qubit_indices) > 1:
                logger.warning(
                    f"Encoder '{encoder_name}' mapeado para múltiplos qubits ({mapped_qubit_indices}). "
                    f"A extração de métricas atual só processa o primeiro qubit ({mapped_qubit_indices[0]})."
                )

            # O índice do qubit no circuito de batch (0 a N-1)
            q_idx_in_circuit = mapped_qubit_indices[0]

            if not (0 <= q_idx_in_circuit < num_total_qubits_in_circuit):
                logger.error(
                    f"Encoder '{encoder_name}': Índice de qubit mapeado ({q_idx_in_circuit}) "
                    f"está fora do intervalo do circuito (0-{num_total_qubits_in_circuit-1}). Pulando."
                )
                continue

            count_for_1 = 0
            for state_str, count_val in raw_counts.items():
                if len(state_str) != num_total_qubits_in_circuit:
                    logger.warning(
                        f"_extract_encoder_metrics_from_counts: Estado '{state_str}' tem comprimento {len(state_str)}, "
                        f"esperado {num_total_qubits_in_circuit}. Pulando este estado."
                    )
                    continue

                # Qiskit ordena os bits da direita para a esquerda (qubit 0 é o mais à direita).
                # Ex: '101' para 3 qubits. Qubit 0 é state_str[2], Qubit 1 é state_str[1], Qubit 2 é state_str[0].
                # O bit correspondente a q_idx_in_circuit é state_str[ (N-1) - q_idx_in_circuit ]
                bit_index_in_str = (num_total_qubits_in_circuit - 1) - q_idx_in_circuit

                if state_str[bit_index_in_str] == "1":
                    count_for_1 += count_val

            p1 = count_for_1 / total_shots
            p0 = 1.0 - p1
            # Valor esperado: |0> -> +1, |1> -> -1. Logo, E = (+1)*p0 + (-1)*p1 = p0 - p1
            expected_value = p0 - p1

            per_encoder_metrics[encoder_name] = {
                "p0": round(p0, 6),
                "p1": round(p1, 6),
                "expected_value": round(expected_value, 6),
            }
            logger.debug(
                f"Métricas para '{encoder_name}' (qubit {q_idx_in_circuit}): p0={p0:.4f}, p1={p1:.4f}, E={expected_value:.4f}"
            )

        return per_encoder_metrics

    # YAA: Métodos de Hot-Swap de Parâmetros
    def update_shots(self, shots: int):
        """Atualiza o número de shots para as medições quânticas."""
        if shots > 0:
            self.shots = shots
            logger.info(f"QMC: Número de shots atualizado para {self.shots}")
            if hasattr(self.qualia_universe, "reconfigure"):
                self.qualia_universe.reconfigure(shots=self.shots)
                logger.info(
                    f"QMC: qualia_universe reconfigurado com shots={self.shots}"
                )
            else:
                logger.info(
                    "QMC: qualia_universe não possui método 'reconfigure' para shots."
                )
        else:
            logger.warning(
                f"QMC: Tentativa de atualizar shots com valor inválido: {shots}. Mantendo valor anterior: {self.shots}"
            )

    def update_measure_frequency(self, freq: int):
        """Atualiza a frequência de medição no universo QUALIA."""
        if freq > 0:
            self.measure_frequency = freq
            logger.info(
                f"QMC: Frequência de medição atualizada para {self.measure_frequency}"
            )
            if hasattr(self.qualia_universe, "reconfigure"):
                self.qualia_universe.reconfigure(
                    measure_frequency=self.measure_frequency
                )
                logger.info(
                    f"QMC: qualia_universe reconfigurado com measure_frequency={self.measure_frequency}"
                )
            else:
                logger.info(
                    "QMC: qualia_universe não possui método 'reconfigure' para measure_frequency."
                )
        else:
            logger.warning(
                f"QMC: Tentativa de atualizar frequência de medição com valor inválido: {freq}. Mantendo valor anterior: {self.measure_frequency}"
            )

    def update_temperature(self, temp: float):
        """Atualiza a temperatura para simulação de ruído térmico."""
        if temp >= 0:
            self.thermal_noise_temperature = temp
            logger.info(
                f"QMC: Temperatura de ruído térmico atualizada para {self.thermal_noise_temperature}"
            )
            if hasattr(self.qualia_universe, "reconfigure"):
                self.qualia_universe.reconfigure(
                    thermal_noise_temperature=self.thermal_noise_temperature
                )
                logger.info(
                    f"QMC: qualia_universe reconfigurado com thermal_noise_temperature={self.thermal_noise_temperature}"
                )
            else:
                logger.info(
                    "QMC: qualia_universe não possui método 'reconfigure' para temperature/thermal_noise_temperature."
                )
        else:
            logger.warning(
                f"QMC: Tentativa de atualizar temperatura com valor inválido: {temp}. Mantendo valor anterior: {self.thermal_noise_temperature}"
            )

    def update_scr_depth(self, scr_depth: int) -> None:
        """Atualiza a profundidade de scrambling do universo."""
        if scr_depth > 0:
            if hasattr(self.qualia_universe, "reconfigure"):
                self.qualia_universe.reconfigure(scr_depth=scr_depth)
                logger.info(
                    f"QMC: qualia_universe reconfigurado com scr_depth={scr_depth}"
                )
            else:
                logger.info(
                    "QMC: qualia_universe não possui método 'reconfigure' para scr_depth."
                )
        else:
            logger.warning(
                f"QMC: Tentativa de atualizar scr_depth com valor inválido: {scr_depth}."
            )

    def update_entanglement_style(self, style: str) -> None:
        """Atualiza o estilo de entrelaçamento do universo."""
        if style:
            if hasattr(self.qualia_universe, "reconfigure"):
                self.qualia_universe.reconfigure(entanglement_style=style)
                logger.info(
                    f"QMC: qualia_universe reconfigurado com entanglement_style={style}"
                )
            else:
                logger.info(
                    "QMC: qualia_universe não possui método 'reconfigure' para entanglement_style."
                )
        else:
            logger.warning("QMC: Estilo de entrelaçamento vazio fornecido.")

    # Fim dos Métodos de Hot-Swap

    # Exemplo de uso (apenas para ilustração, não parte da classe QMC)
    # def integrate_qmc_hot_swap_in_metacognition(qmc_instance, metacognitive_directives):
    #     if not isinstance(qmc_instance, QuantumMetricsCalculator):
    #         logger.error("Instância QMC inválida fornecida para hot-swap.")
    #         return
    #
    #     qmc_updates = metacognitive_directives.get("qmc_params", {})
    #     for param, value in qmc_updates.items():
    #         update_method_name = f"update_{param}"
    #         if hasattr(qmc_instance, update_method_name):
    #             try:
    #                 getattr(qmc_instance, update_method_name)(value)
    #                 logger.info(f"Metacognição: QMC.{update_method_name} chamado com {value}")
    #             except Exception as e:
    #                 logger.error(f"Metacognição: Erro ao chamar QMC.{update_method_name} com {value}: {e}")
    #         else:
    #             logger.warning(f"Metacognição: Parâmetro QMC desconhecido para hot-swap: {param}")

    # --- Testes Ad-Hoc (se este arquivo for executado diretamente) ---
    # (O código de teste foi removido para brevidade na resposta, mas existia no arquivo original)
