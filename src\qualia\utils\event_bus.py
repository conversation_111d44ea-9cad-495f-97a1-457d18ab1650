from __future__ import annotations

"""Infraestrutura de barramento de eventos assíncrono do QUALIA.

Este módulo fornece o :class:`EventBus`, um mecanismo simples para
publicação e assinatura de mensagens entre componentes. A implementação
prioriza o desacoplamento e inclui tolerância a falhas para evitar
propagação de erros entre handlers.
"""

import asyncio
import logging
from dataclasses import dataclass
from typing import Any, Awaitable, Callable, Dict, List, Set, Type
import pandas as pd
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from ..consciousness.holographic_trading_adapter import HolographicSignal
    from ..core.qast_oracle_decision_engine import OracleDecision
    from ..consciousness.enhanced_data_collector import EnhancedMarketData
else:
    # Runtime fallbacks to avoid circular imports
    HolographicSignal = Any
    OracleDecision = Any
    EnhancedMarketData = Any

# ============================================================================
# DEFINIÇÕES DE EVENTOS GENÉRICOS
# ============================================================================


@dataclass
class MarketDataUpdated:
    """Evento publicado quando novos dados de mercado (OHLCV, etc.) estão disponíveis."""

    market_data: List[EnhancedMarketData]
    timestamp: float


@dataclass
class HolographicSignalsUpdated:
    """Evento publicado quando novos sinais do universo holográfico são gerados."""

    signals: Dict[str, HolographicSignal]
    timestamp: float


@dataclass
class OracleDecisionsUpdated:
    """Evento publicado quando o QAST Oracle gera novas decisões."""

    decisions: List[OracleDecision]
    timestamp: float


@dataclass
class SystemShutdown:
    """Evento para sinalizar um shutdown gracioso do sistema."""

    reason: str


# ============================================================================
# IMPLEMENTAÇÃO DO EVENT BUS
# ============================================================================


class EventBus:
    """
    Barramento de eventos assíncrono para comunicação desacoplada entre os
    componentes do sistema QUALIA.
    """

    def __init__(self) -> None:
        self._subscribers: Dict[Type, List[Callable[[Any], Awaitable[None]]]] = {}
        self._active_tasks: Set[asyncio.Task] = set()
        self._logger = logging.getLogger(__name__)

    def _handle_task_done(self, task: asyncio.Task) -> None:
        """Callback para limpar tarefas e registrar exceções."""

        self._active_tasks.discard(task)
        if task.cancelled():
            return
        exc = task.exception()
        if exc is not None:
            self._logger.error(
                "Exceção não tratada em handler do EventBus: %s",
                exc,
                exc_info=True,
            )

    def unsubscribe(self, event_type: Type, handler: Callable[[Any], Awaitable[None]]):
        """Remove ``handler`` from the subscriber list of ``event_type``.

        If ``handler`` is not registered for ``event_type``, this call is a no-op.
        """
        handlers = self._subscribers.get(event_type)
        if handlers and handler in handlers:
            handlers.remove(handler)
            if not handlers:
                del self._subscribers[event_type]

    def subscribe(self, event_type: Type, handler: Callable[[Any], Awaitable[None]]):
        """
        Registra um handler (corrotina) para um tipo de evento específico.

        Args:
            event_type: A classe do evento a ser escutada (ex: MarketDataUpdated).
            handler: A função assíncrona que será chamada quando o evento for publicado.
        """
        if event_type not in self._subscribers:
            self._subscribers[event_type] = []
        self._subscribers[event_type].append(handler)

    def publish(self, event: Any, payload: Any | None = None) -> None:
        """Publica ``event`` no barramento.

        Para retrocompatibilidade, aceita ``event`` como ``str`` e ``payload``
        separado, replicando a interface de ``AsyncEventBus``.
        """

        if payload is not None:
            handlers = self._subscribers.get(event, [])
            for handler in handlers:
                try:
                    loop = asyncio.get_running_loop()
                    loop_running = True
                except RuntimeError:  # pragma: no cover - no loop
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop_running = False
                if asyncio.iscoroutinefunction(handler):
                    coro = handler(payload)
                    if loop_running:
                        task = loop.create_task(coro)
                        self._active_tasks.add(task)
                        task.add_done_callback(self._handle_task_done)
                    else:
                        try:
                            loop.run_until_complete(coro)
                        except Exception as exc:  # pragma: no cover - defensive
                            self._logger.error(
                                "Erro ao processar evento %s: %s",
                                event,
                                exc,
                                exc_info=True,
                            )
                else:
                    if loop_running:
                        handler(payload)
                    else:
                        try:
                            loop.run_until_complete(asyncio.to_thread(handler, payload))
                        except Exception as exc:  # pragma: no cover - defensive
                            self._logger.error(
                                "Erro ao processar evento %s: %s",
                                event,
                                exc,
                                exc_info=True,
                            )
            return

        event_type = type(event)
        if event_type in self._subscribers:
            for handler in self._subscribers[event_type]:
                try:
                    loop = asyncio.get_running_loop()
                    loop_running = True
                except RuntimeError:  # pragma: no cover - no loop
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop_running = False
                if asyncio.iscoroutinefunction(handler):
                    coro = handler(event)
                    if loop_running:
                        task = loop.create_task(coro)
                        self._active_tasks.add(task)
                        task.add_done_callback(self._handle_task_done)
                    else:
                        try:
                            loop.run_until_complete(coro)
                        except Exception as exc:  # pragma: no cover - defensive
                            self._logger.error(
                                "Erro ao processar evento %s: %s",
                                event_type.__name__,
                                exc,
                                exc_info=True,
                            )
                else:
                    if loop_running:
                        handler(event)
                    else:
                        try:
                            loop.run_until_complete(asyncio.to_thread(handler, event))
                        except Exception as exc:  # pragma: no cover - defensive
                            self._logger.error(
                                "Erro ao processar evento %s: %s",
                                event_type.__name__,
                                exc,
                                exc_info=True,
                            )

    async def shutdown(self):
        """Cancela todas as tarefas ativas e aguarda a finalização."""
        if not self._active_tasks:
            return

        for task in list(self._active_tasks):
            if not task.done():
                task.cancel()

        await asyncio.gather(*self._active_tasks, return_exceptions=True)
        self._active_tasks.clear()
