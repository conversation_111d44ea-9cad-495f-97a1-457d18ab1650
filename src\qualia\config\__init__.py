"""QUALIA configuration entry point with lazy imports."""

from __future__ import annotations

import importlib
from typing import Optional, Any
from types import SimpleNamespace

__all__ = [
    "config",
    "RiskProfileSettings",
    "_FALLBACK_RISK_PROFILE_SETTINGS",
    "get_global_config_loader",
    "get_global_config_manager",
    "register_encoder",
    "unregister_encoder",
    "get_encoder_class",
    "create_encoder",
    "get_registered_encoders",
    "load_quantum_risk_defaults",
    "load_dynamic_risk_defaults",
    "load_signal_arbiter_defaults",
    "load_utils_defaults",
    "load_trading_defaults",
    "load_market_defaults",
    "VectorType",
    "feature_toggle",
    "get_min_counts_diversity_ratio",
    "logging",
]

_MODULE_MAP = {
    "config": ("qualia.config.settings", "settings"),
    "RiskProfileSettings": "qualia.config.risk_profiles",
    "_FALLBACK_RISK_PROFILE_SETTINGS": "qualia.config.risk_profiles",
    "VectorType": "qualia.config.vector_types",
    "ConfigLoader": "qualia.config.config_loader",
    "ConfigManager": "qualia.config.config_manager",
    "register_encoder": "qualia.config.encoder_registry",
    "unregister_encoder": "qualia.config.encoder_registry",
    "get_encoder_class": "qualia.config.encoder_registry",
    "create_encoder": "qualia.config.encoder_registry",
    "get_registered_encoders": "qualia.config.encoder_registry",
    "load_quantum_risk_defaults": "qualia.config.quantum_risk_defaults",
    "load_dynamic_risk_defaults": "qualia.config.dynamic_risk_defaults",
    "load_signal_arbiter_defaults": "qualia.config.signal_arbiter_defaults",
    "load_utils_defaults": "qualia.config.utils_defaults",
    "load_trading_defaults": "qualia.config.trading_defaults",
    "load_market_defaults": "qualia.config.market_defaults",
    "feature_toggle": "qualia.config.feature_flags",
    "get_min_counts_diversity_ratio": "qualia.config.constants",
}

_global_loader: Optional[Any] = None
_global_manager: Optional[Any] = None


def get_global_config_loader() -> ConfigLoader:
    """Return a process-wide :class:`ConfigLoader` instance."""

    global _global_loader
    if _global_loader is None:
        ConfigLoader = __getattr__("ConfigLoader")
        _global_loader = ConfigLoader()
    return _global_loader


def get_global_config_manager() -> ConfigManager:
    """Return a process-wide :class:`ConfigManager` instance."""

    global _global_manager
    if _global_manager is None:
        ConfigManager = __getattr__("ConfigManager")
        _global_manager = ConfigManager()
    return _global_manager


from .constants import get_min_counts_diversity_ratio

# Namespace com parametros de logging padrao
logging = SimpleNamespace(log_dir=importlib.import_module("qualia.config.settings").log_dir)


def __getattr__(name: str) -> Any:
    entry = _MODULE_MAP.get(name)
    if entry is None:
        raise AttributeError(f"module 'qualia.config' has no attribute {name}")
    if isinstance(entry, tuple):
        module_path, attr = entry
    else:
        module_path, attr = entry, name
    module = importlib.import_module(module_path)
    return getattr(module, attr)
