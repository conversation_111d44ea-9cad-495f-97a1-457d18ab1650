from __future__ import annotations

"""Online Genetic Algorithm to evolve threshold parameters in production."""

import random
import time
from collections import deque
from typing import Dict, List, Tuple

import numpy as np

from qualia.memory.event_bus import SimpleEventBus
from qualia.utils.logger import get_logger

logger = get_logger(__name__)

# Public globals for API/dashboard access

LATEST_GEN_INFO: Dict[str, float] | None = None  # updated every generation
GLOBAL_EVENT_BUS: SimpleEventBus | None = None   # set on first Evolver init

_GEN_BOUNDS = {"alpha": (0.02, 0.5), "offset": (0.01, 0.2)}


class _Individual:
    def __init__(self, alpha: float, offset: float):
        self.alpha = alpha
        self.offset = offset
        self.alerts = 0
        self.tp = 0
        self.fp = 0
        self.fn = 0

    @property
    def genes(self) -> Tuple[float, float]:
        return self.alpha, self.offset

    def as_dict(self) -> Dict[str, float]:
        return {"alpha": self.alpha, "offset": self.offset}

    def reset_metrics(self):
        self.alerts = self.tp = self.fp = self.fn = 0

    def fitness(self) -> float:
        precision = self.tp / self.alerts if self.alerts else 0.0
        recall = self.tp / (self.tp + self.fn) if (self.tp + self.fn) else 0.0
        if precision + recall == 0:
            return 0.0
        return 2 * precision * recall / (precision + recall)


class GAThresholdEvolver:
    def __init__(
        self,
        event_bus: SimpleEventBus,
        population_size: int = 6,
        offspring_size: int = 12,
        window_events: int = 500,
        mutation_sigma: float = 0.02,
    ) -> None:
        global GLOBAL_EVENT_BUS
        self.bus = event_bus
        if GLOBAL_EVENT_BUS is None:
            GLOBAL_EVENT_BUS = event_bus
        self.mu = population_size
        self.lam = offspring_size
        self.window = window_events
        self.sigma = mutation_sigma
        self.buffer: deque[Dict] = deque(maxlen=self.window)
        self.population: List[_Individual] = [self._random_individual() for _ in range(self.mu)]
        self.bus.subscribe("nexus.cross_modal_coherence", self._on_event)
        self.bus.subscribe("nexus.ga.pause", lambda _p: setattr(self, "paused", True))
        self.bus.subscribe("nexus.ga.resume", lambda _p: setattr(self, "paused", False))

        self.paused = False
        self.gen = 0
        logger.info("GAThresholdEvolver iniciado: mu=%d lambda=%d", self.mu, self.lam)

    # ----------------------------- GA -------------------------

    def _random_individual(self) -> _Individual:
        a = random.uniform(*_GEN_BOUNDS["alpha"])
        off = random.uniform(*_GEN_BOUNDS["offset"])
        return _Individual(a, off)

    def _mutate(self, ind: _Individual) -> _Individual:
        a = np.clip(np.random.normal(ind.alpha, self.sigma), *_GEN_BOUNDS["alpha"])
        off = np.clip(np.random.normal(ind.offset, self.sigma), *_GEN_BOUNDS["offset"])
        return _Individual(float(a), float(off))

    def _crossover(self, p1: _Individual, p2: _Individual) -> _Individual:
        return _Individual((p1.alpha + p2.alpha) / 2, (p1.offset + p2.offset) / 2)

    # ----------------------------- Event handling ------------

    def _on_event(self, payload):  # type: ignore[override]
        if self.paused:
            return
        coherence = float(payload.get("coherence", 0.0))
        self.buffer.append(coherence)
        # simplistic label: treat top 1% coherence as ground-truth event
        threshold_gt = np.percentile(self.buffer, 99) if len(self.buffer) >= 50 else 1.0
        is_event = coherence >= threshold_gt
        for ind in self.population:
            alert = coherence >= (ind.offset)  # simplified: actual calibrator logic omitted
            if alert:
                ind.alerts += 1
                if is_event:
                    ind.tp += 1
                else:
                    ind.fp += 1
            elif is_event:
                ind.fn += 1
        if len(self.buffer) == self.window:
            self._evolve()
            self.buffer.clear()

    # ----------------------------- Evolution cycle -----------

    def _evolve(self):
        ranked = sorted(self.population, key=lambda i: i.fitness(), reverse=True)
        best = ranked[0]
        self.gen += 1
        logger.info("GA geração concluída – best F1 %.3f alpha %.3f offset %.3f", best.fitness(), best.alpha, best.offset)
        # publish override
        from qualia.events import ThresholdOverrideEvent, GAGenerationEvent

        self.bus.publish(
            "nexus.threshold.override",
            ThresholdOverrideEvent(alpha=best.alpha, offset=best.offset, f1=best.fitness()),
        )

        global LATEST_GEN_INFO
        LATEST_GEN_INFO = {
            "gen": self.gen,
            "alpha": best.alpha,
            "offset": best.offset,
            "f1": best.fitness(),
        }
        self.bus.publish(
            "nexus.ga.generation",
            GAGenerationEvent(
                gen=self.gen,
                alpha=best.alpha,
                offset=best.offset,
                f1=best.fitness(),
            ),
        )

        # create offspring
        new_pop = ranked[: self.mu]
        while len(new_pop) < self.mu + self.lam:
            if random.random() < 0.6:
                p1, p2 = random.sample(ranked[: self.mu], 2)
                child = self._crossover(p1, p2)
            else:
                p = random.choice(ranked[: self.mu])
                child = self._mutate(p)
            new_pop.append(child)
        # reset metrics
        for ind in new_pop:
            ind.reset_metrics()
        self.population = new_pop[: self.mu] 