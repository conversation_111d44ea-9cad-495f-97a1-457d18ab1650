"""Utilities for PID control and metrics updates."""

from __future__ import annotations

from typing import Any, Dict, Sequence

from . import metrics_helpers


class PIDController:
    """Simple proportional-integral controller used in tests."""

    def __init__(self, Kp: float, Ki: float, i_max: float = 1.0) -> None:
        self.Kp = Kp
        self.Ki = Ki
        self.i_max = i_max
        self.integral = 0.0

    def update(self, error: float) -> float:
        """Return the PID control value for the provided error."""
        self.integral = max(min(self.integral + error, self.i_max), -self.i_max)
        return self.Kp * error + self.Ki * self.integral


async def update_metrics(
    metrics: Any,
    retro_operator: Any,
    quantum_universe: Any,
    qualia_state: Any,
    signals: Sequence[Any],
    risk_assessment: Any,
    timestamp: float,
) -> None:
    """Update trading metrics using ``metrics_helpers``."""
    try:
        retro_active = (
            retro_operator.is_retrocausally_active()
            if hasattr(retro_operator, "is_retrocausally_active")
            else False
        )
        prediction_confidence = (
            float(getattr(retro_operator.current_state, "prediction_confidence", 0.5))
            if getattr(retro_operator, "current_state", None) is not None
            else 0.5
        )

        metrics_payload: Dict[str, Any] = {
            "coherence_level": qualia_state.coherence_level,
            "quantum_entanglement": qualia_state.quantum_entanglement,
            "total_signals": len(signals) if signals else 0,
            "approved_signals": len(getattr(risk_assessment, "approved_signals", [])),
            "risk_score": getattr(risk_assessment, "overall_risk_score", 0.0),
            "retrocausality_active": float(retro_active),
            "prediction_confidence": prediction_confidence,
            "timestamp": timestamp,
        }

        tokens = [getattr(s, "action", str(s)) for s in signals] if signals else []
        entropy_snapshot: Dict[str, Any] = {}
        if hasattr(quantum_universe, "get_latest_metrics"):
            entropy_snapshot = quantum_universe.get_latest_metrics(
                get_last_value_from_list=True
            )
        current_entropy = entropy_snapshot.get("quantum_entropy")
        if current_entropy is not None:
            metrics_payload["symbolic_coherence"] = (
                metrics_helpers.compute_symbolic_coherence(
                    quantum_universe, float(current_entropy), tokens
                )
            )
        else:
            metrics_payload["symbolic_coherence"] = 0.0

        await metrics.record_metrics(metrics_payload)
        await metrics.record_retro_comparative_metrics(
            retro_active, prediction_confidence
        )
    except Exception as exc:  # pragma: no cover - best effort
        import logging

        logging.getLogger(__name__).warning("Failed to update metrics: %s", exc)
