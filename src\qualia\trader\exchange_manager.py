from __future__ import annotations

import asyncio
import time
from collections import defaultdict
from typing import Any, Dict, List, Optional

from ccxt import AuthenticationError

from ..market.symbol_utils import validate_and_normalize_symbols
from ..exchange_setup import (
    connection_settings_from_env,
    credentials_from_env,
    decrypt_credentials_from_config,
    initialize_exchange,
)
from ..utils.logger import get_logger

logger = get_logger(__name__)


class ExchangeManager:
    """Singleton responsável por gerenciar a conexão com a exchange."""

    _instance: Optional["ExchangeManager"] = None

    def __new__(cls) -> "ExchangeManager":
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def initialize_connection(self, trader: Any) -> None:
        await initialize_exchange_connection_impl(trader)

    async def is_connected(self, trader: Any) -> bool:
        return await is_exchange_connected_impl(trader)

    async def close(self, trader: Any) -> None:
        await close_exchange_impl(trader)

    async def health_check(self, trader: Any) -> None:
        if trader.mode not in ["live", "paper_trading"]:
            return

        if getattr(trader, "shutdown_event", None) and trader.shutdown_event.is_set():
            return

        if getattr(trader, "_prevent_reconnect", False):
            return

        if await self.is_connected(trader):
            logger.debug("Conexão com a exchange já está ativa; reutilizando")
            return

        await self.initialize_connection(trader)


async def initialize_exchange_connection_impl(trader: Any) -> None:
    """Initialize the exchange connection for ``trader``."""

    if getattr(trader, "shutdown_event", None) and trader.shutdown_event.is_set():
        logger.info("Ignorando reconexão: shutdown em andamento")
        return

    if getattr(trader, "_prevent_reconnect", False):
        logger.info("Reconexão bloqueada por flag interna")
        return

    if hasattr(trader, "exchange") and trader.exchange:
        checker = getattr(trader.exchange, "is_connected", None)
        connected = True
        if checker:
            connected = (
                await checker() if asyncio.iscoroutinefunction(checker) else checker()
            )

        if connected:
            logger.info("Conexão existente está saudável; reutilizando conexão atual")
            return

        if getattr(trader.exchange, "_rest_client", None) is None:
            session = getattr(trader.exchange.exchange, "session", None)
            if session and not getattr(session, "closed", True):
                logger.info(
                    "Sessão da exchange ativa sem REST client; mantendo conexão existente"
                )
                return

        logger.info("Conexão considerada inativa. Encerrando antes de reinicializar...")
        try:
            await close_exchange(trader)
        except Exception as e:  # pragma: no cover - log only
            logger.warning(
                "Erro ao fechar a conexão preexistente com a exchange: %s",
                e,
                exc_info=True,
            )

    logger.info(
        "Modo '%s' requer conexão com exchange. Tentando inicializar...",
        trader.mode,
    )
    try:
        if trader._secure_config:
            api_key, secret_key, passphrase = decrypt_credentials_from_config(
                trader.data_source,
                trader._secure_config,
                trader.security,
                passphrase=trader.kucoin_passphrase,
            )
        else:
            api_key, secret_key, passphrase = credentials_from_env(trader.data_source)

        if trader.data_source == "kraken" and (not api_key or not secret_key):
            raise ValueError(
                "Credenciais da Kraken (API Key e Secret Key) não encontradas."
            )
        if trader.data_source == "kucoin" and (
            not api_key or not secret_key or not passphrase
        ):
            raise EnvironmentError(
                "Credenciais da Kucoin (API Key, Secret Key e Passphrase) não encontradas."
            )

        conn_timeout, conn_retries = connection_settings_from_env()

        config_timeout = (
            trader.config.get("exchanges", {}).get("timeout")
            if isinstance(trader.config, dict)
            else None
        )
        if config_timeout is not None:
            try:
                conn_timeout = float(config_timeout)
            except (TypeError, ValueError):
                logger.warning(
                    "Valor invalido para 'exchanges.timeout': %s",
                    config_timeout,
                )

        trader.exchange = initialize_exchange(
            trader.data_source,
            api_key=api_key,
            api_secret=secret_key,
            password=passphrase,
            conn_timeout=conn_timeout,
            conn_retries=conn_retries,
            ticker_timeout=trader.ticker_fetch_timeout,
            ticker_retries=trader.config.get(
                "ticker_retry_attempts", trader.TICKER_RETRY_ATTEMPTS
            ),
            fail_threshold=trader.api_fail_threshold,
            recovery_timeout=trader.api_recovery_timeout,
        )

        logger.info("Inicializando conexão com a exchange...")
        await trader.exchange.initialize_connection()

        if trader.data_source == "kraken":
            try:
                pre_ticker = await trader.exchange.watch_ticker("XBT/USDT")
                if pre_ticker:
                    trader.exchange.ticker_cache["XBT/USDT"] = (
                        pre_ticker,
                        time.time(),
                    )
                    logger.info("Ticker XBT/USDT pré-carregado via WebSocket")
            except Exception as pre_exc:
                logger.debug("Falha ao pré-buscar ticker XBT/USDT: %s", pre_exc)
        # elif trader.data_source == "kucoin":
        #     try:
        #         pre_ticker = await trader.exchange.watch_ticker("BTC/USDT")
        #         if pre_ticker:
        #             trader.exchange.ticker_cache["BTC/USDT"] = (
        #                 pre_ticker,
        #                 time.time(),
        #             )
        #             logger.info("Ticker BTC/USDT pré-carregado via WebSocket")
        #     except Exception as pre_exc:
        #         logger.debug("Falha ao pré-buscar ticker BTC/USDT: %s", pre_exc)

        async def _normalize_symbols() -> List[str]:
            normalized_syms = await validate_and_normalize_symbols(
                trader.symbols_raw, trader.exchange
            )
            if not normalized_syms:
                raise ValueError("Nenhum símbolo válido encontrado na exchange.")

            if normalized_syms != trader.symbols:
                mapping = dict(zip(trader.symbols, normalized_syms))
                trader.symbols = normalized_syms
                trader.open_positions = {
                    mapping.get(k, k): v for k, v in trader.open_positions.items()
                }

                for attr in [
                    "market_data",
                    "current_tickers",
                    "stale_tickers",
                    "position_meta",
                    "last_data_update_time",
                    "strategies_additional_tf",
                ]:
                    data = getattr(trader, attr, {})
                    setattr(
                        trader,
                        attr,
                        {mapping.get(k, k): v for k, v in data.items()},
                    )

                trader.last_trade_close_time = defaultdict(
                    float,
                    {
                        mapping.get(k, k): v
                        for k, v in trader.last_trade_close_time.items()
                    },
                )

            trader.symbols_normalized = True
            trader._normalized_symbols_cache = normalized_syms
            trader._symbols_raw_cache = list(trader.symbols_raw)
            return normalized_syms

        if not getattr(trader, "symbols_normalized", False):
            normalized = await _normalize_symbols()
        else:
            cached_raw = getattr(trader, "_symbols_raw_cache", None)
            cached_norm = getattr(trader, "_normalized_symbols_cache", None)
            if cached_norm is not None and list(trader.symbols_raw) == cached_raw:
                normalized = cached_norm
            else:
                normalized = await _normalize_symbols()

        logger.info("Símbolos normalizados após conexão: %s", normalized)
        logger.info("Sincronizando posições abertas com a exchange...")
        await trader._sync_open_positions()
        trader._connection_failure_count = 0
    except AuthenticationError as e:
        logger.error(
            "Falha de autenticação na exchange %s: %s",
            trader.data_source,
            e,
            exc_info=True,
        )
        if getattr(trader, "exchange", None):
            try:
                await close_exchange(trader)
            except Exception as close_exc:  # pragma: no cover - log only
                logger.warning(
                    "Erro ao fechar conexão da exchange após falha: %s",
                    close_exc,
                    exc_info=True,
                )
        trader.exchange = None
        raise
    except Exception as e:
        logger.error(
            "Falha ao inicializar a conexão com a exchange: %s",
            e,
            exc_info=True,
        )
        if getattr(trader, "exchange", None):
            try:
                await close_exchange(trader)
            except Exception as close_exc:  # pragma: no cover - log only
                logger.warning(
                    "Erro ao fechar conexão da exchange após falha: %s",
                    close_exc,
                    exc_info=True,
                )
        trader.exchange = None
        raise


async def is_exchange_connected_impl(trader: Any) -> bool:
    """Check whether the trader's exchange connection is active."""

    wait_short = float(trader.connection_retry_wait)
    wait_long = float(trader.connection_failure_cooldown)

    if not trader.exchange:
        trader._connection_failure_count = (
            getattr(trader, "_connection_failure_count", 0) + 1
        )
        logger.debug(
            "[EXCHANGE] Nenhuma instância ativa. Falha consecutiva %s",
            trader._connection_failure_count,
        )
        backoff = min(
            wait_short * (2 ** (trader._connection_failure_count - 1)),
            32.0,
        )
        logger.debug(
            "[EXCHANGE] Back-off %.2fs por falha #%s",
            backoff,
            trader._connection_failure_count,
        )
        await asyncio.sleep(backoff)
        if trader._connection_failure_count >= trader.connection_failure_limit:
            logger.warning(
                "Limite de falhas de conexão atingido (%s). Aguardando %.1fs",
                trader.connection_failure_limit,
                wait_long,
            )
            await asyncio.sleep(wait_long)
        if trader.statsd:
            trader.statsd.gauge("exchange.connected", 0)
        return False

    try:
        checker = getattr(trader.exchange, "is_connected", None)
        if checker:
            if asyncio.iscoroutinefunction(checker):
                connected = await checker()
            else:
                connected = checker()
            if not connected:
                logger.debug(
                    "[EXCHANGE] Checagem inicial indicou desconexão, aguardando %.2fs",
                    wait_short,
                )
                backoff = min(0.5 * (2 ** (trader._connection_failure_count - 1)), 32.0)
                logger.debug(
                    "[EXCHANGE] Back-off %.2fs por falha #%s",
                    backoff,
                    trader._connection_failure_count,
                )
                await asyncio.sleep(backoff)
                connected = (
                    await checker()
                    if asyncio.iscoroutinefunction(checker)
                    else checker()
                )
        else:
            connected = trader.exchange is not None

        if connected:
            if trader._connection_failure_count:
                logger.debug(
                    "[EXCHANGE] Conexão restabelecida após %s falhas",
                    trader._connection_failure_count,
                )
            trader._connection_failure_count = 0
            if trader.statsd:
                trader.statsd.gauge("exchange.connected", 1)
            return True

        trader._connection_failure_count = (
            getattr(trader, "_connection_failure_count", 0) + 1
        )
        logger.debug(
            "[EXCHANGE] Exchange desconectada. Contagem de falhas: %s",
            trader._connection_failure_count,
        )
        backoff = min(
            wait_short * (2 ** (trader._connection_failure_count - 1)),
            32.0,
        )
        logger.debug(
            "[EXCHANGE] Back-off %.2fs por falha #%s",
            backoff,
            trader._connection_failure_count,
        )
        await asyncio.sleep(backoff)
        if trader._connection_failure_count >= trader.connection_failure_limit:
            logger.warning(
                "Limite de falhas de conexão atingido (%s). Aguardando %.1fs",
                trader.connection_failure_limit,
                wait_long,
            )
            await asyncio.sleep(wait_long)
        if trader.statsd:
            trader.statsd.gauge("exchange.connected", 0)
        return False
    except Exception as exc:
        trader._connection_failure_count = (
            getattr(trader, "_connection_failure_count", 0) + 1
        )
        logger.debug(
            "[EXCHANGE] Erro ao verificar conexão: %s (falha %s)",
            exc,
            trader._connection_failure_count,
            exc_info=True,
        )
        backoff = min(
            wait_short * (2 ** (trader._connection_failure_count - 1)),
            32.0,
        )
        logger.debug(
            "[EXCHANGE] Back-off %.2fs por falha #%s",
            backoff,
            trader._connection_failure_count,
        )
        await asyncio.sleep(backoff)
        if trader._connection_failure_count >= trader.connection_failure_limit:
            logger.warning(
                "Limite de falhas de conexão atingido (%s). Aguardando %.1fs",
                trader.connection_failure_limit,
                wait_long,
            )
            await asyncio.sleep(wait_long)
        if trader.statsd:
            trader.statsd.gauge("exchange.connected", 0)
        return False


async def close_exchange_impl(trader: Any) -> None:
    """Close the trader's current exchange connection."""

    if not trader.exchange:
        return

    try:
        logger.info("Fechando conexão com a exchange...")
        close_ws = getattr(trader.exchange, "close_websocket", None)
        if close_ws and asyncio.iscoroutinefunction(close_ws):
            try:
                await close_ws()
            except Exception as exc:  # pragma: no cover - log and continue
                logger.error(
                    "Erro ao fechar websocket da exchange: %s",
                    exc,
                    exc_info=True,
                )

        await trader.exchange.close()
        logger.info("Conexão com a exchange fechada com sucesso.")
    except Exception as exc:
        logger.error("Erro ao fechar conexão com a exchange: %s", exc, exc_info=True)
    finally:
        trader.exchange = None


_manager = ExchangeManager()


async def initialize_exchange_connection(trader: Any) -> None:
    await _manager.initialize_connection(trader)


async def is_exchange_connected(trader: Any) -> bool:
    return await _manager.is_connected(trader)


async def close_exchange(trader: Any) -> None:
    await _manager.close(trader)


async def health_check(trader: Any) -> None:
    await _manager.health_check(trader)
