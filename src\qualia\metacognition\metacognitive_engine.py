"""
Metacognitive engine for QUALIA trading system
"""

from typing import Any, Awaitable, Callable, Dict, List, Optional
import inspect
import asyncio

from ..utils.logger import get_logger
from ..risk_management.advanced_risk_manager import AdvancedRiskManager
from ..memory.event_bus import SimpleEventBus
from .meta_strategy_rl_agent import MetaStrategyRLAgent
import time

logger = get_logger(__name__)


class MetacognitiveEngine:
    """Metacognitive engine for self-awareness and adaptation"""

    def __init__(
        self,
        config: Dict[str, Any],
        risk_manager: Optional[AdvancedRiskManager] = None,
        meta_strategy_agent: Optional[MetaStrategyRLAgent] = None,
        event_bus: Optional["SimpleEventBus"] = None,
    ):
        self.config = config
        self.risk_manager = risk_manager
        self.meta_strategy_agent = meta_strategy_agent
        self.event_bus = event_bus
        self.feedback_history: List[Dict[str, Any]] = []
        self.z_operator_enabled: bool = bool(
            config.get("learning", {}).get("z_operator_enabled", False)
        )
        self._z_operator_hooks: List[Callable[[bool], Awaitable[None]]] = []
        self._original_risk_per_trade_pct: Optional[float] = None
        self._risk_restore_task: Optional[asyncio.Task] = None

    def register_z_operator_hook(self, hook: Callable[[bool], Awaitable[None]]) -> None:
        """Register a hook triggered when the Z operator is toggled.

        Parameters
        ----------
        hook : Callable[[bool], Awaitable[None]]
            Async callback receiving the enabled state.
        """

        self._z_operator_hooks.append(hook)

    def unregister_z_operator_hook(
        self, hook: Callable[[bool], Awaitable[None]]
    ) -> None:
        """Remove a previously registered Z operator hook.

        Parameters
        ----------
        hook : Callable[[bool], Awaitable[None]]
            The hook to remove.
        """

        if hook in self._z_operator_hooks:
            self._z_operator_hooks.remove(hook)

    async def _trigger_z_operator_hooks(self) -> None:
        """Invoke hooks with the current Z operator state."""

        for hook in list(self._z_operator_hooks):
            try:
                await hook(self.z_operator_enabled)
            except Exception as exc:  # pragma: no cover - defensive
                logger.error("Z operator hook error: %s", exc)

    async def _restore_risk_after_delay(self, delay: float = 3600.0) -> None:
        """Restore ``risk_per_trade_pct`` to its original value after ``delay`` seconds."""

        try:
            await asyncio.sleep(delay)
        except asyncio.CancelledError:  # pragma: no cover - defensive
            return
        risk_cfg = self.config.get("risk")
        if (
            risk_cfg
            and self._original_risk_per_trade_pct is not None
            and "risk_per_trade_pct" in risk_cfg
        ):
            risk_cfg["risk_per_trade_pct"] = self._original_risk_per_trade_pct
            self._original_risk_per_trade_pct = None
            if self.event_bus:
                from ..events import RiskUpdateEvent

                self.event_bus.publish(
                    "risk.update",
                    RiskUpdateEvent(params={"risk_per_trade_pct": risk_cfg["risk_per_trade_pct"]}),
                )

    async def apply_learning_directives(self, directives: Dict[str, Any]) -> None:
        """Apply learning directives to adjust internal parameters."""

        if "z_operator_enabled" in directives:
            new_state = bool(directives["z_operator_enabled"])
            changed = new_state != self.z_operator_enabled
            self.z_operator_enabled = new_state
            status = "enabled" if self.z_operator_enabled else "disabled"
            logger.info("Metacognition: Z operator %s by directive", status)
            if changed:
                await self._trigger_z_operator_hooks()

    async def initialize(self):
        """Initialize the metacognitive engine"""
        logger.info("Metacognitive engine initialized")

    async def process_feedback(
        self,
        qualia_state,
        signals,
        risk_assessment,
        timestamp,
        learning_directives: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Process feedback from trading operations"""
        try:
            await self.apply_learning_directives(learning_directives or {})
            feedback = {
                "timestamp": timestamp,
                "coherence_level": qualia_state.coherence_level,
                "signal_count": len(signals) if signals else 0,
                "risk_score": (
                    risk_assessment.overall_risk_score
                    if hasattr(risk_assessment, "overall_risk_score")
                    else 0.5
                ),
            }

            self.feedback_history.append(feedback)

            # Limit history size
            if len(self.feedback_history) > 1000:
                self.feedback_history.pop(0)

            # Perform metacognitive analysis
            await self._analyze_performance_trends()

        except Exception as e:
            logger.error(f"Error processing metacognitive feedback: {e}")

    async def _analyze_performance_trends(self):
        """Analyze performance trends for self-adaptation"""
        try:
            if len(self.feedback_history) < 10:
                return

            # Simple trend analysis
            recent_feedback = self.feedback_history[-10:]
            avg_coherence = sum(f["coherence_level"] for f in recent_feedback) / len(
                recent_feedback
            )
            avg_risk = sum(f["risk_score"] for f in recent_feedback) / len(
                recent_feedback
            )

            logger.debug(
                f"Metacognitive analysis - Avg coherence: {avg_coherence:.3f}, Avg risk: {avg_risk:.3f}"
            )

        except Exception as e:
            logger.warning(f"Error in performance trend analysis: {e}")

    async def resolve_paradox(self, paradox_payload: Dict[str, Any]) -> None:
        """
        Resolve um evento de paradoxo, conforme a Fase 5 do roadmap.

        Quando um paradoxo é detectado (conflito entre a realidade externa e interna),
        este método toma ações drásticas para forçar o sistema a reavaliar seu
        modelo de mundo.

        Args:
            paradox_payload: Dicionário contendo os detalhes do paradoxo.
        """
        symbol = paradox_payload.get("symbol", "unknown")
        coherence = paradox_payload.get("coherence", -1)

        logger.critical(
            f"🚨 RESOLVENDO PARADOXO para {symbol} (Coerência: {coherence:.3f}) 🚨"
        )

        # Ação 1: Forçar o MetaStrategyRLAgent a um modo de exploração agressiva
        if self.meta_strategy_agent:
            logger.info(
                "Metacognição: Forçando modo de exploração no MetaStrategyRLAgent."
            )
            try:
                await self.meta_strategy_agent.force_exploration(
                    symbol, duration_hours=1
                )
            except Exception as e:
                logger.error(f"Erro ao forçar exploração: {e}")

        # Ação 2: Reduzir drasticamente os limites de risco globais
        if self.risk_manager:
            logger.info(
                "Metacognição: Ativando protocolo de emergência no RiskManager."
            )
            try:
                self.risk_manager.activate_emergency_protocol(reduction_factor=0.5)
            except Exception as e:
                logger.error(f"Erro ao ativar protocolo de emergência: {e}")
        else:
            risk_cfg = self.config.get("risk")
            if risk_cfg and "risk_per_trade_pct" in risk_cfg:
                if self._original_risk_per_trade_pct is None:
                    self._original_risk_per_trade_pct = risk_cfg["risk_per_trade_pct"]
                risk_cfg["risk_per_trade_pct"] *= 0.5
                if self.event_bus:
                    from ..events import RiskUpdateEvent

                    self.event_bus.publish(
                        "risk.update",
                        RiskUpdateEvent(params={"risk_per_trade_pct": risk_cfg["risk_per_trade_pct"]}),
                    )
                if self._risk_restore_task:
                    self._risk_restore_task.cancel()
                self._risk_restore_task = asyncio.create_task(self._restore_risk_after_delay())

        reset_fn = self.config.get("reset_strategies")
        if reset_fn is not None:
            try:
                if inspect.iscoroutinefunction(reset_fn):
                    await reset_fn()
                else:
                    reset_fn()
            except Exception as exc:  # pragma: no cover - defensive
                logger.error("Erro ao executar reset_strategies: %s", exc)

        # Ação 3: Ativar o z_operator para reavaliar o modelo de mundo
        if not self.z_operator_enabled:
            self.z_operator_enabled = True
            logger.info("Metacognição: Ativando z_operator devido a paradoxo.")
            await self._trigger_z_operator_hooks()  # Notifica outros componentes

        # Registrar o evento de paradoxo para análise futura
        self.feedback_history.append(
            {
                "timestamp": time.time(),
                "event_type": "paradox_resolution",
                "symbol": symbol,
                "coherence": coherence,
                "payload": paradox_payload,
            }
        )

        if self.event_bus:
            from ..events import ParadoxEvent

            self.event_bus.publish(
                "metacognition.paradox",
                ParadoxEvent(
                    symbol=symbol, coherence=coherence, details=paradox_payload
                ),
            )

    async def shutdown(self):
        """Shutdown the metacognitive engine"""
        logger.info("Metacognitive engine shutdown")
