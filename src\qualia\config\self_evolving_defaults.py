"""Load defaults for ``SelfEvolvingTradingSystem`` behaviour.

The file ``config/self_evolving_defaults.yaml`` can be overridden by the
``QUALIA_SELF_EVOLVING_DEFAULTS`` environment variable.
"""

from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

logger = get_logger(__name__)

_DEFAULT_PATH = get_config_file_path("self_evolving_defaults.yaml")


def load_self_evolving_defaults() -> Dict[str, Any]:
    """Load ``SelfEvolvingTradingSystem`` defaults from YAML.

    The ``QUALIA_SELF_EVOLVING_DEFAULTS`` environment variable can override the
    bundled file.

    Returns
    -------
    Dict[str, Any]
        Mapping of configuration keys to values. Returns an empty mapping on
        error.
    """

    return load_yaml_config(
        "QUALIA_SELF_EVOLVING_DEFAULTS",
        _DEFAULT_PATH,
        logger=logger,
    )


__all__ = ["load_self_evolving_defaults"]
