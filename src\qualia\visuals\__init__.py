"""Componentes visuais do QUALIA."""

from __future__ import annotations

from typing import Any

__all__ = ["DynamicLogoEngine", "QualiaHUD"]


def __getattr__(name: str) -> Any:
    """Lazily import visual components to reduce startup time."""

    if name == "DynamicLogoEngine":
        from .dynamic_logo import DynamicLogoEngine as _DynamicLogoEngine

        return _DynamicLogoEngine
    if name == "QualiaHUD":
        from .hud import QualiaHUD as _QualiaHUD

        return _QualiaHUD
    raise AttributeError(f"module 'qualia.visuals' has no attribute {name}")
