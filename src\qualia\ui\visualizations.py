"""
Visualization functions for the QUALIA Quantum Dashboard

Este módulo contém funções para visualizar resultados de simulações quânticas,
incluindo gráficos de vetores de estado, distribuições de probabilidade,
e evolução de métricas.

Utiliza Plotly para gerar visualizações compatíveis com a interface Flask.
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
import json
from typing import Dict, List, Any, Optional, Union, Tuple


def plot_statevector(statevector: np.ndarray) -> go.Figure:
    """
    Plot statevector as a bar chart with magnitude and phase

    Args:
        statevector: The statevector from simulation results

    Returns:
        Plotly figure with statevector visualization
    """
    # Convert to numpy array if not already
    if not isinstance(statevector, np.ndarray):
        statevector = np.array(statevector)

    # Get number of qubits from statevector size
    n_qubits = int(np.log2(len(statevector)))

    # Prepare data for plotting
    states = [format(i, f"0{n_qubits}b") for i in range(len(statevector))]
    probabilities = np.abs(statevector) ** 2
    phases = np.angle(statevector) * 180 / np.pi  # Convert to degrees

    # Create a dataframe for plotting
    df = pd.DataFrame({"State": states, "Probability": probabilities, "Phase": phases})

    # Create figure with two y-axes
    fig = go.Figure()

    # Add bars for probability amplitude
    fig.add_trace(
        go.Bar(
            x=df["State"],
            y=df["Probability"],
            name="Probability",
            marker_color="royalblue",
        )
    )

    # Create a secondary y-axis for phase
    fig.add_trace(
        go.Scatter(
            x=df["State"],
            y=df["Phase"],
            name="Phase (degrees)",
            mode="markers",
            marker=dict(size=10, color="red", symbol="circle"),
            yaxis="y2",
        )
    )

    # Update layout
    fig.update_layout(
        title="Statevector Visualization",
        xaxis_title="Quantum State",
        yaxis=dict(title="Probability", side="left", range=[0, 1]),
        yaxis2=dict(
            title="Phase (degrees)",
            side="right",
            range=[-180, 180],
            overlaying="y",
            tickmode="array",
            tickvals=[-180, -90, 0, 90, 180],
            gridcolor="lightgray",
        ),
        legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)"),
        barmode="group",
    )

    return fig


def plot_probability_distribution(counts: Dict[str, int]) -> go.Figure:
    """
    Plot measurement counts as a probability distribution

    Args:
        counts: Dictionary of measurement counts

    Returns:
        Plotly figure with probability distribution
    """
    # Sort states by binary representation for clearer visualization
    states = sorted(list(counts.keys()))
    values = [counts[state] for state in states]

    # Calculate total counts for normalization
    total_counts = sum(values)
    probabilities = [count / total_counts for count in values]

    # Create a dataframe for plotting
    df = pd.DataFrame({"State": states, "Probability": probabilities, "Counts": values})

    # Create figure
    fig = go.Figure()

    # Add bars
    fig.add_trace(
        go.Bar(
            x=df["State"],
            y=df["Probability"],
            text=df["Counts"],
            textposition="auto",
            name="Probability",
            marker_color="lightseagreen",
        )
    )

    # Update layout
    fig.update_layout(
        title="Measurement Probability Distribution",
        xaxis_title="Quantum State",
        yaxis_title="Probability",
        yaxis=dict(range=[0, 1.05]),
        bargap=0.2,
    )

    return fig


def plot_entropy_evolution(metrics: Dict[str, List[Any]]) -> go.Figure:
    """
    Plot the evolution of entropy metrics over simulation steps

    Args:
        metrics: Dictionary of metric values

    Returns:
        Plotly figure with entropy evolution
    """
    # Check if entropy metrics are available
    if "quantum_entropy" not in metrics or not metrics["quantum_entropy"]:
        # Create an empty figure with a message
        fig = go.Figure()
        fig.update_layout(
            title="Entropy Evolution (No Data Available)",
            xaxis_title="Simulation Step",
            yaxis_title="Entropy Value",
        )
        return fig

    # Clean metrics data (remove None values)
    def clean_metrics_data(data):
        return [v if v is not None else float("nan") for v in data]

    # Prepare data for plotting
    steps = list(range(len(metrics["quantum_entropy"])))
    entropy_data = clean_metrics_data(metrics["quantum_entropy"])

    # Create figure
    fig = go.Figure()

    # Add entropy trace
    fig.add_trace(
        go.Scatter(
            x=steps,
            y=entropy_data,
            mode="lines+markers",
            name="Quantum Entropy",
            line=dict(color="blue", width=2),
        )
    )

    # Add other entropy-related metrics if available
    if "von_neumann_entropy" in metrics and metrics["von_neumann_entropy"]:
        vn_entropy_data = clean_metrics_data(metrics["von_neumann_entropy"])
        fig.add_trace(
            go.Scatter(
                x=steps,
                y=vn_entropy_data,
                mode="lines",
                name="Von Neumann Entropy",
                line=dict(color="green", width=2, dash="dash"),
            )
        )

    if "renyi_entropy" in metrics and metrics["renyi_entropy"]:
        renyi_entropy_data = clean_metrics_data(metrics["renyi_entropy"])
        fig.add_trace(
            go.Scatter(
                x=steps,
                y=renyi_entropy_data,
                mode="lines",
                name="Rényi Entropy",
                line=dict(color="purple", width=2, dash="dot"),
            )
        )

    # Update layout
    fig.update_layout(
        title="Entropy Evolution Over Simulation Steps",
        xaxis_title="Simulation Step",
        yaxis_title="Entropy Value",
        legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)"),
    )

    return fig


def plot_metrics_comparison(
    metrics: Dict[str, List[Any]], available_metrics: List[str]
) -> go.Figure:
    """
    Plot comparison of multiple metrics on the same chart

    Args:
        metrics: Dictionary of metric values
        available_metrics: List of available metric names

    Returns:
        Plotly figure with metrics comparison
    """

    # Clean metrics data (remove None values)
    def clean_metrics_data(data):
        return [v if v is not None else float("nan") for v in data]

    # Create figure
    fig = go.Figure()

    # Define a color palette
    colors = px.colors.qualitative.Plotly

    # Add traces for each selected metric
    for i, metric_name in enumerate(available_metrics):
        if metric_name in metrics and metrics[metric_name]:
            data = clean_metrics_data(metrics[metric_name])
            steps = list(range(len(data)))

            # Display name with proper formatting
            display_name = metric_name.replace("_", " ").title()

            fig.add_trace(
                go.Scatter(
                    x=steps,
                    y=data,
                    mode="lines",
                    name=display_name,
                    line=dict(color=colors[i % len(colors)], width=2),
                )
            )

    # Update layout
    fig.update_layout(
        title="Quantum Metrics Comparison",
        xaxis_title="Simulation Step",
        yaxis_title="Metric Value",
        legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)"),
    )

    return fig


def plot_correlation_matrix(
    metrics: Dict[str, List[Any]], available_metrics: List[str]
) -> go.Figure:
    """
    Plot correlation matrix between different metrics

    Args:
        metrics: Dictionary of metric values
        available_metrics: List of available metric names

    Returns:
        Plotly figure with correlation matrix
    """
    # Clean metrics data and build a DataFrame
    metric_data = {}

    for metric_name in available_metrics:
        if metric_name in metrics and metrics[metric_name]:
            # Clean data
            data = [v if v is not None else float("nan") for v in metrics[metric_name]]
            if len(data) > 0:
                metric_data[metric_name.replace("_", " ").title()] = data

    # If we have enough data, create a correlation matrix
    if len(metric_data) > 1:
        df = pd.DataFrame(metric_data)
        corr_matrix = df.corr(method="pearson")

        # Create heatmap
        fig = go.Figure(
            data=go.Heatmap(
                z=corr_matrix.values,
                x=corr_matrix.columns,
                y=corr_matrix.index,
                colorscale="RdBu",
                zmin=-1,
                zmax=1,
                text=np.round(corr_matrix.values, 2),
                texttemplate="%{text}",
                colorbar=dict(title="Correlation", titleside="right"),
            )
        )

        # Update layout
        fig.update_layout(
            title="Metrics Correlation Matrix",
            xaxis=dict(ticks="", tickangle=45),
            yaxis=dict(ticks=""),
        )

        return fig
    else:
        # Create an empty figure with a message
        fig = go.Figure()
        fig.update_layout(
            title="Metrics Correlation Matrix (Insufficient Data)",
            xaxis_title="",
            yaxis_title="",
        )

        return fig


def plot_qast_symbolic_evolution(metrics: Dict[str, List[Any]]) -> Dict[str, Any]:
    """
    Processa os dados de evolução simbólica do QAST para visualização.

    Args:
        metrics: Dicionário com dados métricos

    Returns:
        Dicionário com figuras e informações para visualização
    """
    result = {
        "has_data": False,
        "message": "Dados de evolução simbólica QAST não disponíveis. Execute uma simulação com QAST Feedback ativado.",
        "figures": {},
        "data": {},
    }

    # Verifica se temos dados de QAST
    if "qualia_dynamics" not in metrics or not metrics["qualia_dynamics"]:
        return result

    # Extrair dados
    dynamics = metrics["qualia_dynamics"]

    # Separar dados para plotagem
    cycles = []
    entropies = []
    expressions = []

    for entry in dynamics:
        if isinstance(entry, dict):
            cycles.append(entry.get("cycle", 0))
            entropies.append(entry.get("entropy", 0))
            expressions.append(entry.get("expr", "N/A"))

    # Criar dataframe
    if cycles:
        df = pd.DataFrame(
            {"Cycle": cycles, "Symbolic Entropy": entropies, "Expression": expressions}
        )

        # Criar gráfico de evolução de entropia simbólica
        fig = go.Figure()

        fig.add_trace(
            go.Scatter(
                x=df["Cycle"],
                y=df["Symbolic Entropy"],
                mode="lines+markers",
                name="Symbolic Entropy",
                line=dict(color="orange", width=2),
            )
        )

        # Update layout
        fig.update_layout(
            title="QAST Symbolic Entropy Evolution",
            xaxis_title="QAST Cycle",
            yaxis_title="Symbolic Entropy",
        )

        # Convertemos a figura para JSON
        result["figures"]["entropy_evolution"] = fig.to_json()
        result["has_data"] = True
        result["message"] = ""
        result["data"]["cycles"] = cycles
        result["data"]["entropies"] = entropies
        result["data"]["expressions"] = expressions
        result["data"]["df_json"] = df.to_json(orient="records")

        # Processar dados de coeficientes, se disponíveis
        if "coefficient_evolution" in metrics and metrics["coefficient_evolution"]:
            coef_data = metrics["coefficient_evolution"]
            if isinstance(coef_data, list) and len(coef_data) > 0:
                # Extrair os ciclos e coeficientes
                coef_cycles = list(range(len(coef_data)))

                # Detectar o número de coeficientes
                if isinstance(coef_data[0], list):
                    n_coefs = len(coef_data[0])

                    # Criar gráfico para cada coeficiente
                    coeffs_fig = go.Figure()

                    for i in range(n_coefs):
                        values = [
                            entry[i] if i < len(entry) else None for entry in coef_data
                        ]
                        coeffs_fig.add_trace(
                            go.Scatter(
                                x=coef_cycles,
                                y=values,
                                mode="lines",
                                name=f"Coefficient {i+1}",
                            )
                        )

                    # Update layout
                    coeffs_fig.update_layout(
                        title="QAST Coefficients Evolution",
                        xaxis_title="QAST Cycle",
                        yaxis_title="Coefficient Value",
                    )

                    # Adicionar ao resultado
                    result["figures"]["coefficient_evolution"] = coeffs_fig.to_json()
                    result["data"]["coef_data"] = coef_data
    else:
        result["message"] = "Sem dados suficientes de ciclos QAST para visualização."

    return result


def display_qast_logs_table(metrics: Dict[str, Any]) -> Dict[str, Any]:
    """
    Processa logs QAST (qualia_dynamics) para exibição em tabela.

    Args:
        metrics: Dicionário com dados métricos

    Returns:
        Dicionário com dados processados para exibição
    """
    result = {
        "has_data": False,
        "message": "Logs QAST não disponíveis.",
        "logs_data": [],
    }

    if "qualia_dynamics" not in metrics or not metrics["qualia_dynamics"]:
        return result

    dynamics = metrics["qualia_dynamics"]

    # Criar dataframe para exibição
    logs_data = []

    for entry in dynamics:
        if isinstance(entry, dict):
            # Extrair dados básicos
            cycle = entry.get("cycle", "N/A")
            expr = entry.get("expr", "N/A")
            entropy = entry.get("entropy", "N/A")

            # Extrair coeficientes
            coeffs = entry.get("coefficients", [])
            if coeffs:
                coeffs_str = ", ".join([f"{c:.4f}" for c in coeffs])
            else:
                coeffs_str = "N/A"

            logs_data.append(
                {
                    "Cycle": cycle,
                    "Expression": expr,
                    "Entropy": (
                        f"{entropy:.6f}"
                        if isinstance(entropy, (int, float))
                        else entropy
                    ),
                    "Coefficients": coeffs_str,
                }
            )

    if logs_data:
        result["has_data"] = True
        result["message"] = ""
        result["logs_data"] = logs_data

        # Criar dataframe e convertê-lo para JSON para facilitar o uso no
        # frontend
        df = pd.DataFrame(logs_data)
        result["logs_data_json"] = df.to_json(orient="records")
    else:
        result["message"] = "Sem dados de logs QAST para exibição."

    return result


def plot_hypothesis_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """
    Visualiza os resultados das hipóteses H1-H4 em uma interface amigável.

    Args:
        results: Dicionário contendo os resultados das hipóteses H1-H4

    Returns:
        Dicionário com informações de visualização para o frontend
    """
    result_data = {
        "has_data": False,
        "message": "Execute uma simulação para validar as hipóteses H1-H4.",
        "figures": {},
        "statistics": {},
        "metrics": {},
    }

    if not results:
        return result_data

    result_data["has_data"] = True
    result_data["message"] = ""

    # Extrair dados para cada hipótese
    if "h1_results" in results and results["h1_results"]:
        h1_data = results["h1_results"]

        # Extrair séries temporais
        entropy_series = h1_data.get("entropy_series", [])
        baseline_series = h1_data.get("baseline_series", [])
        transition_points = h1_data.get("transition_points", [])

        # Criar gráfico para H1
        if entropy_series and len(entropy_series) > 0:
            fig = go.Figure()

            # Adicionar série de entropia
            steps = list(range(len(entropy_series)))
            fig.add_trace(
                go.Scatter(
                    x=steps,
                    y=entropy_series,
                    mode="lines",
                    name="Quantum Entropy",
                    line=dict(color="blue", width=2),
                )
            )

            # Adicionar linha de base
            if baseline_series and len(baseline_series) == len(entropy_series):
                fig.add_trace(
                    go.Scatter(
                        x=steps,
                        y=baseline_series,
                        mode="lines",
                        name="Baseline",
                        line=dict(color="gray", width=1, dash="dash"),
                    )
                )

            # Marcar pontos de transição
            if transition_points:
                # Extrair coordenadas x e y dos pontos de transição
                trans_x = [tp for tp in transition_points if tp < len(entropy_series)]
                trans_y = [entropy_series[tp] for tp in trans_x]

                fig.add_trace(
                    go.Scatter(
                        x=trans_x,
                        y=trans_y,
                        mode="markers",
                        name="Transition Points",
                        marker=dict(color="red", size=10, symbol="circle"),
                    )
                )

            # Atualizar layout
            fig.update_layout(
                title="Quantum Entropy Evolution and Transition Points",
                xaxis_title="Simulation Step",
                yaxis_title="Entropy Value",
                legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)"),
            )

            # Adicionar figura ao resultado
            result_data["figures"]["h1_entropy_evolution"] = fig.to_json()

            # Adicionar estatísticas se disponíveis
            if "statistics" in h1_data and h1_data["statistics"]:
                stats = h1_data["statistics"]
                result_data["statistics"]["h1"] = stats

                result_data["metrics"]["h1"] = [
                    {
                        "name": "Number of Transitions",
                        "value": stats.get("num_transitions", "N/A"),
                    },
                    {
                        "name": "Average Entropy",
                        "value": f"{stats.get('avg_entropy', 'N/A'):.4f}",
                    },
                    {
                        "name": "Adaptive Coefficient",
                        "value": f"{stats.get('adaptive_coefficient', 'N/A'):.4f}",
                    },
                    {
                        "name": "Entropy Range",
                        "value": f"{stats.get('entropy_range', 'N/A'):.4f}",
                    },
                ]
        else:
            result_data["figures"][
                "h1_message"
            ] = "Dados insuficientes para visualização da Hipótese H1."

    if "h2_results" in results and results["h2_results"]:
        h2_data = results["h2_results"]

        # Extrair séries temporais
        coherence_series = h2_data.get("coherence_series", [])
        modulation_series = h2_data.get("modulation_series", [])

        # Criar gráfico para H2
        if coherence_series and len(coherence_series) > 0:
            fig = go.Figure()

            # Adicionar série de coerência
            steps = list(range(len(coherence_series)))
            fig.add_trace(
                go.Scatter(
                    x=steps,
                    y=coherence_series,
                    mode="lines",
                    name="Quantum Coherence",
                    line=dict(color="green", width=2),
                )
            )

            # Adicionar série de modulação
            if modulation_series and len(modulation_series) == len(coherence_series):
                # Criar eixo secundário para a modulação
                fig.add_trace(
                    go.Scatter(
                        x=steps,
                        y=modulation_series,
                        mode="lines",
                        name="Modulation",
                        line=dict(color="purple", width=2, dash="dot"),
                        yaxis="y2",
                    )
                )

                # Configurar layout com dois eixos y
                fig.update_layout(
                    yaxis2=dict(title="Modulation Value", side="right", overlaying="y")
                )

            # Atualizar layout
            fig.update_layout(
                title="Quantum Coherence under Modulation",
                xaxis_title="Simulation Step",
                yaxis_title="Coherence Value",
                legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)"),
            )

            # Adicionar figura ao resultado
            result_data["figures"]["h2_coherence_evolution"] = fig.to_json()

            # Adicionar estatísticas se disponíveis
            if "statistics" in h2_data and h2_data["statistics"]:
                stats = h2_data["statistics"]
                result_data["statistics"]["h2"] = stats

                result_data["metrics"]["h2"] = [
                    {
                        "name": "Correlation Coefficient",
                        "value": f"{stats.get('correlation', 'N/A'):.4f}",
                    },
                    {
                        "name": "Average Coherence",
                        "value": f"{stats.get('avg_coherence', 'N/A'):.4f}",
                    },
                    {
                        "name": "Phase Relationship",
                        "value": stats.get("phase_relation", "N/A"),
                    },
                    {
                        "name": "Modulation Sensitivity",
                        "value": f"{stats.get('modulation_sensitivity', 'N/A'):.4f}",
                    },
                ]
        else:
            result_data["figures"][
                "h2_message"
            ] = "Dados insuficientes para visualização da Hipótese H2."

    if "h3_results" in results and results["h3_results"]:
        h3_data = results["h3_results"]

        # Extrair séries temporais
        otoc_series = h3_data.get("otoc_series", [])
        pattern_points = h3_data.get("pattern_points", [])

        # Criar gráfico para H3
        if otoc_series and len(otoc_series) > 0:
            fig = go.Figure()

            # Adicionar série OTOC
            steps = list(range(len(otoc_series)))
            fig.add_trace(
                go.Scatter(
                    x=steps,
                    y=otoc_series,
                    mode="lines",
                    name="OTOC Values",
                    line=dict(color="orange", width=2),
                )
            )

            # Marcar pontos de padrão
            if pattern_points:
                # Extrair coordenadas x e y dos pontos de padrão
                pattern_x = [pp for pp in pattern_points if pp < len(otoc_series)]
                pattern_y = [otoc_series[pp] for pp in pattern_x]

                fig.add_trace(
                    go.Scatter(
                        x=pattern_x,
                        y=pattern_y,
                        mode="markers",
                        name="Pattern Points",
                        marker=dict(color="blue", size=10, symbol="circle"),
                    )
                )

            # Atualizar layout
            fig.update_layout(
                title="OTOC Evolution and Pattern Points",
                xaxis_title="Simulation Step",
                yaxis_title="OTOC Value",
                legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)"),
            )

            # Adicionar figura ao resultado
            result_data["figures"]["h3_otoc_evolution"] = fig.to_json()

            # Adicionar estatísticas se disponíveis
            if "statistics" in h3_data and h3_data["statistics"]:
                stats = h3_data["statistics"]
                result_data["statistics"]["h3"] = stats

                result_data["metrics"]["h3"] = [
                    {
                        "name": "Number of Patterns",
                        "value": stats.get("num_patterns", "N/A"),
                    },
                    {
                        "name": "Average OTOC",
                        "value": f"{stats.get('avg_otoc', 'N/A'):.4f}",
                    },
                    {
                        "name": "Pattern Complexity",
                        "value": f"{stats.get('pattern_complexity', 'N/A'):.2f}",
                    },
                    {
                        "name": "Emergent Period",
                        "value": f"{stats.get('emergent_period', 'N/A')}",
                    },
                ]
        else:
            result_data["figures"][
                "h3_message"
            ] = "Dados insuficientes para visualização da Hipótese H3."

    if "h4_results" in results and results["h4_results"]:
        h4_data = results["h4_results"]

        # Extrair séries temporais
        stability_series = h4_data.get("stability_series", [])
        metastable_regions = h4_data.get("metastable_regions", [])

        # Criar gráfico para H4
        if stability_series and len(stability_series) > 0:
            fig = go.Figure()

            # Adicionar série de estabilidade
            steps = list(range(len(stability_series)))
            fig.add_trace(
                go.Scatter(
                    x=steps,
                    y=stability_series,
                    mode="lines",
                    name="Temporal Stability",
                    line=dict(color="teal", width=2),
                )
            )

            # Adicionar regiões meta-estáveis
            if metastable_regions:
                for i, region in enumerate(metastable_regions):
                    start, end = region
                    if start < len(stability_series) and end <= len(stability_series):
                        # Criar região sombreada
                        fig.add_trace(
                            go.Scatter(
                                x=list(range(start, end)),
                                y=stability_series[start:end],
                                mode="lines",
                                name=f"Metastable Region {i+1}",
                                line=dict(color="rgba(255, 0, 0, 0.5)", width=5),
                                fill="tozeroy",
                                fillcolor="rgba(255, 0, 0, 0.1)",
                            )
                        )

            # Atualizar layout
            fig.update_layout(
                title="Temporal Stability and Metastable Regions",
                xaxis_title="Simulation Step",
                yaxis_title="Stability Value",
                legend=dict(x=0.01, y=0.99, bgcolor="rgba(255, 255, 255, 0.5)"),
            )

            # Adicionar figura ao resultado
            result_data["figures"]["h4_stability_evolution"] = fig.to_json()

            # Adicionar estatísticas se disponíveis
            if "statistics" in h4_data and h4_data["statistics"]:
                stats = h4_data["statistics"]
                result_data["statistics"]["h4"] = stats

                result_data["metrics"]["h4"] = [
                    {
                        "name": "Number of Metastable Regions",
                        "value": stats.get("num_metastable_regions", "N/A"),
                    },
                    {
                        "name": "Average Stability",
                        "value": f"{stats.get('avg_stability', 'N/A'):.4f}",
                    },
                    {
                        "name": "Max Metastable Duration",
                        "value": f"{stats.get('max_metastable_duration', 'N/A')}",
                    },
                    {
                        "name": "Temporal Coherence",
                        "value": f"{stats.get('temporal_coherence', 'N/A'):.4f}",
                    },
                ]
        else:
            result_data["figures"][
                "h4_message"
            ] = "Dados insuficientes para visualização da Hipótese H4."

    if not any(
        k in results for k in ["h1_results", "h2_results", "h3_results", "h4_results"]
    ):
        result_data["message"] = (
            "Nenhum resultado de hipótese disponível. Execute uma simulação com validação de hipóteses."
        )

    return result_data
