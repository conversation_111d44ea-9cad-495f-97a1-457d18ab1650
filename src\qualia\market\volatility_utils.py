"""Volatility calculation helpers for QUALIA."""

from __future__ import annotations

from typing import List, Union, Any

import numpy as np
import pandas as pd

try:  # pragma: no cover - optional dependency
    import cupy as cp  # type: ignore
except Exception:  # pragma: no cover - fallback when CuPy missing
    cp = None  # type: ignore

from ..utils.hardware_acceleration import gpu_available

from ..utils.logger import get_logger

logger = get_logger(__name__)


def calculate_implied_volatility(
    price_data: Union[List[float], np.ndarray, pd.Series],
    method: str = "historical",
    *,
    use_gpu: bool | None = None,
) -> float:
    """Calculate annualized implied volatility using various methods.

    Parameters
    ----------
    price_data:
        Sequence of historical closing prices.
    method:
        Method used for calculation. Supported values are ``"historical"``,
        ``"garch"`` and ``"exponential"``.
    use_gpu:
        Force GPU computation when ``True``. When ``None`` the function
        uses :func:`gpu_available` to decide automatically.

    Returns
    -------
    float
        Annualized implied volatility clipped to ``[0.05, 2.0]``.
    """
    try:
        if isinstance(price_data, list):
            prices = np.array(price_data)
        elif isinstance(price_data, pd.Series):
            prices = price_data.values
        else:
            prices = price_data

        if use_gpu is None:
            use_gpu = gpu_available()

        xp: Any = np
        if use_gpu and cp is not None:
            xp = cp  # type: ignore

        if len(prices) < 2:
            return 0.2

        arr = xp.asarray(prices, dtype=float)
        returns = xp.diff(xp.log(arr))

        if method == "historical":
            vol = xp.std(returns) * xp.sqrt(252)
        elif method == "exponential":
            alpha = 0.1
            squared_returns = returns**2
            n = len(squared_returns)
            weights = alpha * (1 - alpha) ** xp.arange(n - 1, -1, -1)
            ewma_var = xp.sum(weights * squared_returns)
            vol = xp.sqrt(ewma_var * 252)
        elif method == "garch":
            long_term_var = xp.var(returns)
            vol = xp.sqrt(long_term_var * 252)
        else:
            vol = xp.std(returns) * xp.sqrt(252)
        vol = xp.clip(vol, 0.05, 2.0)
        if use_gpu and cp is not None and hasattr(vol, "get"):
            vol = float(vol.get())  # type: ignore[attr-defined]
        return float(vol)
    except Exception as exc:  # pragma: no cover - unexpected errors
        logger.error("Erro ao calcular volatilidade impl\u00edcita: %s", exc)
        return 0.2
