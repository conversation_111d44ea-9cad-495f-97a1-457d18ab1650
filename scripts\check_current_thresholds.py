#!/usr/bin/env python3
"""
Script para verificar os thresholds atuais no sistema QUALIA
Mostra valores do arquivo de configuração e do sistema em memória
"""

import sys
import yaml
from pathlib import Path
from datetime import datetime

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

def check_config_file_thresholds():
    """Verifica thresholds no arquivo de configuração"""
    config_path = Path('config/qualia_config.yaml')
    
    print("THRESHOLDS NO ARQUIVO DE CONFIGURACAO")
    print("=" * 60)
    
    if not config_path.exists():
        print(f"ERRO: Arquivo nao encontrado: {config_path}")
        return None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        quantum_thresholds = config.get('quantum_thresholds', {})
        
        if not quantum_thresholds:
            print("ERRO: Secao 'quantum_thresholds' nao encontrada")
            return None
        
        # Mostrar thresholds principais
        main_metrics = ['consciousness', 'coherence', 'confidence']
        volume_momentum = ['volume_surge_min', 'momentum_min']
        geometric = ['spectral_phi_alignment_min', 'golden_symmetry_min']
        
        print("METRICAS PRINCIPAIS:")
        for metric in main_metrics:
            value = quantum_thresholds.get(metric, 'N/A')
            print(f"   {metric}: {value}")
        
        print("\nVOLUME & MOMENTUM:")
        for metric in volume_momentum:
            value = quantum_thresholds.get(metric, 'N/A')
            print(f"   {metric}: {value}")
        
        print("\nMETRICAS GEOMETRICAS:")
        for metric in geometric:
            value = quantum_thresholds.get(metric, 'N/A')
            print(f"   {metric}: {value}")
        
        # Mostrar metadados de calibração se existirem
        metadata = quantum_thresholds.get('_calibration_metadata')
        if metadata:
            print("\nMETADADOS DE CALIBRACAO:")
            for key, value in metadata.items():
                print(f"   {key}: {value}")
        else:
            print("\nNenhum metadado de calibracao encontrado")
        
        return quantum_thresholds
        
    except Exception as e:
        print(f"ERRO lendo arquivo de configuracao: {e}")
        return None

def check_system_thresholds():
    """Verifica thresholds no sistema em memória (se possível)"""
    print("\n" + "=" * 60)
    print("THRESHOLDS NO SISTEMA EM MEMORIA")
    print("=" * 60)
    
    try:
        from qualia.binance_system import QualiaBinanceCorrectedSystem
        
        # Criar instância do sistema
        trading_system = QualiaBinanceCorrectedSystem()
        
        if hasattr(trading_system, 'quantum_thresholds'):
            thresholds = trading_system.quantum_thresholds
            
            print("QUANTUM THRESHOLDS:")
            for metric, value in thresholds.items():
                print(f"   {metric}: {value}")
        else:
            print("Sistema nao tem quantum_thresholds inicializados")
        
        # Verificar adaptive_manager se disponível
        if hasattr(trading_system, 'adaptive_manager') and trading_system.adaptive_manager:
            current_thresholds = trading_system.adaptive_manager.current_thresholds
            
            print("\nADAPTIVE MANAGER THRESHOLDS:")
            for field in ['consciousness', 'coherence', 'confidence', 'volume_surge_min', 
                         'momentum_min', 'spectral_phi_alignment_min', 'golden_symmetry_min']:
                if hasattr(current_thresholds, field):
                    value = getattr(current_thresholds, field)
                    print(f"   {field}: {value}")
        else:
            print("\nAdaptive manager nao disponivel")
            
    except Exception as e:
        print(f"ERRO verificando sistema em memoria: {e}")

def compare_thresholds():
    """Compara thresholds entre arquivo e valores padrão"""
    print("\n" + "=" * 60)
    print("COMPARACAO COM VALORES PADRAO")
    print("=" * 60)
    
    # Valores padrão originais
    default_thresholds = {
        'consciousness': 0.60,
        'coherence': 0.50,
        'confidence': 0.55,
        'momentum_min': 0.003,
        'volume_surge_min': 1.2,
        'spectral_phi_alignment_min': 0.5,
        'golden_symmetry_min': 0.5
    }
    
    config_thresholds = check_config_file_thresholds()
    
    if config_thresholds:
        print("\nCOMPARACAO (Atual vs Padrao):")
        for metric, default_value in default_thresholds.items():
            current_value = config_thresholds.get(metric, 'N/A')
            
            if current_value != 'N/A' and isinstance(current_value, (int, float)):
                change = current_value - default_value
                change_pct = (change / default_value) * 100 if default_value != 0 else 0
                status = "CALIBRADO" if abs(change) > 0.001 else "PADRAO"
                
                print(f"   {metric}:")
                print(f"      Atual: {current_value:.3f}")
                print(f"      Padrao: {default_value:.3f}")
                print(f"      Mudanca: {change:+.3f} ({change_pct:+.1f}%) [{status}]")
            else:
                print(f"   {metric}: {current_value} vs {default_value} [N/A]")

def main():
    """Função principal"""
    print("VERIFICACAO DE THRESHOLDS QUALIA")
    print("=" * 60)
    print(f"Data/Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # Verificar arquivo de configuração
    config_thresholds = check_config_file_thresholds()
    
    # Verificar sistema em memória
    check_system_thresholds()
    
    # Comparar com valores padrão
    compare_thresholds()
    
    # Verificar se há backup
    backup_path = Path('config/qualia_config.yaml.backup')
    if backup_path.exists():
        print(f"\nBACKUP DISPONIVEL: {backup_path}")
        print(f"Criado em: {datetime.fromtimestamp(backup_path.stat().st_mtime)}")
    else:
        print("\nNenhum backup encontrado")

if __name__ == "__main__":
    main()
