"""Kraken exchange client wrapper."""

from __future__ import annotations

from typing import Any, Dict, Optional

from .ccxt_client import CCXTExchangeClient
from ..market.kraken_integration import KrakenIntegration


class KrakenClient(CCXTExchangeClient):
    """Concrete client for the Kraken exchange."""

    integration_cls = KrakenIntegration

    def __init__(self, config: Dict[str, Any]) -> None:
        super().__init__(config)

    async def get_order_book(
        self, symbol: str, limit: int = 100
    ) -> Optional[Dict[str, Any]]:
        """Delegates to :class:`CCXTExchangeClient`."""

        return await super().get_order_book(symbol, limit)
