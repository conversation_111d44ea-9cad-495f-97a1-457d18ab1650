"""
QUALIA Core Package
Sistema quântico-computacional altamente avançado e auto-evolutivo

Módulos principais:
- binance_system: Sistema principal de trading
- config_manager: Gerenciador de configuração centralizado
- adaptive_threshold_system: Sistema adaptativo de thresholds
"""

from .binance_system import QualiaBinanceCorrectedSystem
from .config_manager import QualiaConfigManager, get_config_manager, ConfigurationError
from .adaptive_threshold_system import AdaptiveThresholdManager, MetricAnalysis, TradingMode
from .quantum_memory_bank import QuantumMemoryBank

__all__ = [
    'QualiaBinanceCorrectedSystem',
    'QualiaConfigManager', 
    'get_config_manager',
    'ConfigurationError',
    'AdaptiveThresholdManager',
    'MetricAnalysis', 
    'TradingMode',
    'QuantumMemoryBank'
]
