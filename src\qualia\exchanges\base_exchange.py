"""Base exchange interface for the QUALIA trading system."""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import logging

logger = logging.getLogger(__name__)


class BaseExchange(ABC):
    """Base class for all exchange integrations."""

    def __init__(self, config: Dict[str, Any]):
        """Create the exchange wrapper.

        Parameters
        ----------
        config : dict[str, Any]
            Configuration parameters for the exchange connection.
        """
        self.config = config
        self.connected = False

    @abstractmethod
    async def initialize(self):
        """Initialize the exchange connection."""
        pass

    @abstractmethod
    async def get_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Return ticker information for ``symbol``.

        Parameters
        ----------
        symbol : str
            Market symbol to query.

        Returns
        -------
        Optional[dict[str, Any]]
            Ticker data if available, otherwise ``None``.
        """
        pass

    @abstractmethod
    async def place_order(
        self,
        symbol: str,
        order_type: str,
        side: str,
        amount: float,
        price: Optional[float] = None,
    ) -> Optional[Dict[str, Any]]:
        """Place an order on the exchange.

        Parameters
        ----------
        symbol : str
            Market symbol for the order.
        order_type : str
            Order type supported by the exchange.
        side : str
            ``"buy"`` or ``"sell"``.
        amount : float
            Quantity to trade.
        price : float, optional
            Limit price for limit orders.

        Returns
        -------
        Optional[dict[str, Any]]
            Exchange response describing the created order or ``None``.
        """
        pass

    @abstractmethod
    async def get_balance(self) -> Dict[str, float]:
        """Return account balances.

        Returns
        -------
        dict[str, float]
            Mapping of currency codes to available balances.
        """
        pass

    @abstractmethod
    async def get_open_orders(
        self, symbol: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Return currently open orders.

        Parameters
        ----------
        symbol : str, optional
            Restrict results to this market symbol.

        Returns
        -------
        list[dict[str, Any]]
            Collection of open orders on the exchange.
        """
        pass

    @abstractmethod
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel a previously placed order.

        Parameters
        ----------
        order_id : str
            Identifier of the order to cancel.
        symbol : str
            Market symbol of the order.

        Returns
        -------
        bool
            ``True`` if the order was successfully cancelled, ``False`` otherwise.
        """
        pass

    @abstractmethod
    async def shutdown(self):
        """Close any connections and release resources."""
        pass

    async def get_order_book(
        self, symbol: str, limit: int = 100
    ) -> Optional[Dict[str, Any]]:
        """Return order book data.

        Parameters
        ----------
        symbol : str
            Market symbol to query.
        limit : int, optional
            Maximum number of entries to return. Default is ``100``.

        Returns
        -------
        Optional[dict[str, Any]]
            Snapshot do livro de ordens.

        Raises
        ------
        NotImplementedError
            Quando a exchange não oferece a funcionalidade.
        """
        raise NotImplementedError("get_order_book não implementado")

    async def get_trades(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Return recent trades.

        Parameters
        ----------
        symbol : str
            Market symbol to query.
        limit : int, optional
            Maximum number of trades to return. Default is ``100``.

        Returns
        -------
        list[dict[str, Any]]
            Informações de trade.

        Raises
        ------
        NotImplementedError
            Quando a exchange não oferece a funcionalidade.
        """
        raise NotImplementedError("get_trades não implementado")
