from __future__ import annotations

import json
import os
from datetime import datetime
from flask import Blueprint, jsonify, request, current_app
from ..common_types import QuantumSignaturePacket

memory_bp = Blueprint("memory", __name__, url_prefix="/memory")


def _get_memory_file() -> str:
    """Return the path of the Quantum Pattern Memory persistence file.

    The environment variable ``QUALIA_QPM_MEMORY_FILE`` has priority to allow
    custom locations for the memory file. When the variable is unset, the
    default path defined in ``settings.qpm_memory_file`` is returned.

    Returns
    -------
    str
        File system path where memory snapshots are persisted.
    """

    env_path = os.getenv("QUALIA_QPM_MEMORY_FILE")
    if env_path:
        return env_path
    from ..config.settings import settings

    return settings.qpm_memory_file


@memory_bp.route("/snapshot", methods=["GET"])
def memory_snapshot():
    """Return persisted memory items after the given date."""
    since_param = request.args.get("since")
    if since_param:
        try:
            since_ts = float(since_param)
        except ValueError:
            try:
                since_ts = datetime.fromisoformat(since_param).timestamp()
            except Exception:
                return jsonify({"success": False, "message": "invalid date"}), 400
    else:
        since_ts = 0.0

    file_path = _get_memory_file()
    if not os.path.exists(file_path):
        return jsonify({"success": True, "items": []})

    try:
        with open(file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
    except (OSError, json.JSONDecodeError) as exc:
        message = f"failed to load memory file: {exc}"
        return jsonify({"success": False, "message": message}), 500

    items = []
    for patterns in data.get("memory", {}).values():
        for pattern in patterns:
            ts = pattern.get("timestamp")
            if ts is not None and float(ts) > since_ts:
                items.append(pattern)

    items.sort(key=lambda p: p.get("timestamp", 0))
    return jsonify({"success": True, "items": items})


@memory_bp.route("/warmstart/update", methods=["POST"])
def warmstart_update():
    """Trigger warm-start scenario update based on stored metrics."""

    state = current_app.extensions.get("qualia_state")
    qpm = getattr(state, "qpm_memory", None) if state else None
    if not qpm:
        return (
            jsonify({"success": False, "message": "qpm not available"}),
            503,
        )
    qpm.warmstart_manager.update_scenarios()
    return jsonify({"success": True})


@memory_bp.route("/import", methods=["POST"])
def import_patterns():
    """Import patterns provided in the request body."""

    state = current_app.extensions.get("qualia_state")
    qpm = getattr(state, "qpm_memory", None) if state else None
    if not qpm:
        return jsonify({"success": False, "message": "qpm not available"}), 503

    try:
        payload = request.get_json(force=True)
    except Exception:
        return jsonify({"success": False, "message": "invalid payload"}), 400

    if not isinstance(payload, list):
        return jsonify({"success": False, "message": "payload must be a list"}), 400

    imported = 0
    for item in payload:
        qsp_data = item.get("quantum_signature_packet")
        if not isinstance(qsp_data, dict):
            continue
        try:
            packet = QuantumSignaturePacket(**qsp_data)
        except Exception:
            continue
        qpm.store_pattern(
            packet,
            item.get("market_snapshot", {}),
            item.get("outcome", {}),
            item.get("decision_context"),
            timestamp=item.get("timestamp"),
            market_scenario=item.get("market_scenario"),
            extra_metadata=item.get("extra_metadata"),
            metadata=item.get("metadata"),
        )
        imported += 1

    if imported > 0 and qpm.auto_persist and qpm.persistence_path:
        qpm.save_to_file()

    return jsonify({"success": True, "imported": imported})


@memory_bp.route("/report", methods=["GET"])
def memory_report():
    """Return a detailed memory report and metacognition snapshot."""

    state = current_app.extensions.get("qualia_state")
    qpm = getattr(state, "qpm_memory", None) if state else None
    if not qpm:
        return jsonify({"success": False, "message": "qpm not available"}), 503
    report = qpm.get_memory_stats()
    metacog = getattr(state, "metacognition", None) if state else None
    if metacog:
        try:
            from ..metacognition import service as metacog_service

            report["metacognition"] = metacog_service.snapshot()
        except Exception:
            report["metacognition"] = {"focus": 0.0, "metrics": []}
    return jsonify({"success": True, "report": report})
