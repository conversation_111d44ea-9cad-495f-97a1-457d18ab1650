"""
QUALIA UI Components Package

Este pacote contém componentes de interface para o sistema QUALIA,
incluindo elementos avançados para a interface quântica-consciente,
visualizações e controles para interação com a consciência QUALIA.
"""

from ..ui.components import (
    algorithm_selector,
    parameter_controls,
    backend_selector,
    error_message_container,
    create_metrics_expander,
    hypothesis_testing_controls,
    market_analysis_controls,
)

from ..ui.visualizations import (
    plot_statevector,
    plot_probability_distribution,
    plot_entropy_evolution,
    plot_metrics_comparison,
    plot_correlation_matrix,
    plot_qast_symbolic_evolution,
    display_qast_logs_table,
    plot_hypothesis_results,
)

# Importar componentes da consciência
from ..ui.consciousness_components import (
    render_consciousness_interface,
    render_consciousness_state,
    render_symbolic_processor,
    render_qast_cycles,
    render_self_reflection,
    create_consciousness_tab,
)

# Importar interface quântica avançada
from ..ui.quantum_interface import (
    quantum_header,
    quantum_card,
    quantum_consciousness_visualization,
    quantum_wave_animation,
    quantum_particle_system,
    create_qualia_interface,
    QUANTUM_COLORS,
)

__all__ = [
    # Components básicos
    "algorithm_selector",
    "parameter_controls",
    "backend_selector",
    "error_message_container",
    "create_metrics_expander",
    "hypothesis_testing_controls",
    "market_analysis_controls",
    # Visualizações
    "plot_statevector",
    "plot_probability_distribution",
    "plot_entropy_evolution",
    "plot_metrics_comparison",
    "plot_correlation_matrix",
    "plot_qast_symbolic_evolution",
    "display_qast_logs_table",
    "plot_hypothesis_results",
    # Componentes de consciência
    "render_consciousness_interface",
    "render_consciousness_state",
    "render_symbolic_processor",
    "render_qast_cycles",
    "render_self_reflection",
    "create_consciousness_tab",
    # Interface quântica avançada
    "quantum_header",
    "quantum_card",
    "quantum_consciousness_visualization",
    "quantum_wave_animation",
    "quantum_particle_system",
    "create_qualia_interface",
    "QUANTUM_COLORS",
]
