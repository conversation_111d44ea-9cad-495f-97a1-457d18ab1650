# coding: utf-8
"""Adaptive evolution helper for the QUALIA universe.

Example
-------
>>> ace = AdaptiveConsciousnessEvolution(qualia_universe=None)
>>> ace.adjust_complexity(market_volatility=0.5)
"""

import copy
import json
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

try:
    from opentelemetry import trace
except Exception:  # pragma: no cover - optional dependency
    trace = None
from .core.qualia_logger import log_event
from .config.ace_config import load_ace_config
from .utils.logging_initializer import initialize_logging
from .config.settings import REPO_ROOT
import pandas as pd

import math
import numpy as np
from .utils.logger import get_logger
from .config.constants import get_min_counts_diversity_ratio
from .utils.feature_flags import dynamic_feature_toggle

# Constante usada para avaliar diversidade de contagens.
# Caso a configuração não esteja disponível (por exemplo, em testes),
# utiliza-se um valor padrão seguro.
try:  # pragma: no cover - simples fallback
    MIN_COUNTS_DIVERSITY_RATIO = get_min_counts_diversity_ratio()
except Exception:  # noqa: BLE001
    MIN_COUNTS_DIVERSITY_RATIO = 0.0

# Supondo que QUALIAQuantumUniverse está em src.qualia_universe
# Para evitar dependência circular direta na definição do arquivo,
# passaremos a instância do universo como argumento.
# from qualia_universe import QUALIAQuantumUniverse

logger = get_logger(__name__)


class AdaptiveConsciousnessEvolution:
    """
    Monitora as condições de mercado e ajusta a configuração do QUALIAQuantumUniverse
    para otimizar o desempenho em diferentes regimes (calmo vs. volátil).
    """

    _otoc_lookup_df: Optional[pd.DataFrame] = None

    def __init__(
        self, qualia_universe: Any, config: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Inicializa o AdaptiveConsciousnessEvolution.

        Args:
            qualia_universe: Instância do QUALIAQuantumUniverse a ser reconfigurada.
            config: Dicionário de configuração opcional.
                    Pode incluir thresholds, número de qubits para diferentes níveis, etc.
                    Ex: {
                        "complexity_threshold_calm": 0.3,
                        "complexity_threshold_volatile": 0.7,
                        "qubits_calm": 4,
                        "qubits_normal": 8,
                        "qubits_volatile": 12,
                        "entanglement_calm": "linear",
                        "entanglement_volatile": "full",
                        "scr_depth_calm": 5,
                        "scr_depth_volatile": 15
                    }
        """
        self.qualia_universe = qualia_universe
        # Pode ser 'calm', 'normal', 'volatile'
        self.current_complexity_level = "normal"
        self.last_metacognitive_feedback = {
            "type": "neutral",
            "confidence": 0.0,
            "details": {},
        }

        # YAA: Inicializar controlador de risco dinâmico
        self.dynamic_risk_controller = None
        self.risk_calibration_results = (
            {}
        )  # Resultados de calibração por símbolo  # YAA: Novo estado

        # adaptation_aggressiveness será lido da config, com default aqui.
        # self.adaptation_aggressiveness = 1.0 # Removido, será de self.config

        default_config = {
            "complexity_threshold_calm": 0.3,  # Abaixo disso, mercado calmo
            "complexity_threshold_volatile": 0.7,  # Acima disso, mercado volátil
            "qubits_calm": 4,
            "qubits_normal": 8,
            "qubits_volatile": 12,
            "entanglement_calm": "linear",
            "entanglement_normal": "linear",
            "entanglement_volatile": "full",
            "scr_depth_calm": 5,
            "scr_depth_normal": 10,
            "scr_depth_volatile": 15,
            "measure_frequency_calm": 2,
            "measure_frequency_normal": 1,
            "measure_frequency_volatile": 1,
            "adaptation_aggressiveness": 0.8,  # Menor agressividade para maior estabilidade
            "min_qubits": 2,  # YAA: Adicionado ao default_config
            "max_qubits": 12,  # YAA: Adicionado ao default_config (JSON pode sobrescrever para 10)
            # YAA: Para Throttle de Adaptação
            # Fator que define o quão sensível o ACE é a pequenas
            # variações de complexidade. Valor reduzido para maior
            # responsividade em mercados levemente mutáveis.
            "delta_threshold_non_linearity_factor": 0.03,
            # YAA: Para Ajuste Dinâmico de Agressividade (dos thresholds de complexidade)
            "complexity_threshold_volatility_factor": 0.5,  # Fator para volatilidade ajustar os thresholds base
            "complexity_min_threshold_calm": 0.1,  # Limite inferior para o threshold calmo dinâmico
            "complexity_max_threshold_volatile": 0.9,  # Limite superior para o threshold volátil dinâmico
            "qubit_increase_threshold_pct": 0.05,
        }
        file_config = config or load_ace_config()
        self.config = {**default_config, **file_config}

        if self.config.get("scr_depth_normal", 0) < 4:
            self.config["entanglement_normal"] = "full"
        # YAA: Inicializar adaptation_aggressiveness a partir da config consolidada
        self.adaptation_aggressiveness = self.config.get(
            "adaptation_aggressiveness", 0.8
        )
        # YAA: Para Throttle de Adaptação
        self.previous_market_complexity_metric = (
            self.config.get(  # YAA: Alterado nome para clareza
                "initial_market_complexity_metric", 0.5
            )
        )
        self.delta_threshold_non_linearity_factor = self.config.get(
            "delta_threshold_non_linearity_factor", 0.03
        )
        # YAA: Para Ajuste Dinâmico de Agressividade (dos thresholds de complexidade)
        self.base_complexity_threshold_calm = self.config["complexity_threshold_calm"]
        self.base_complexity_threshold_volatile = self.config[
            "complexity_threshold_volatile"
        ]
        self.complexity_threshold_volatility_factor = self.config.get(
            "complexity_threshold_volatility_factor", 0.5
        )
        self.complexity_min_threshold_calm = self.config.get(
            "complexity_min_threshold_calm", 0.1
        )
        self.complexity_max_threshold_volatile = self.config.get(
            "complexity_max_threshold_volatile", 0.9
        )
        self.qubit_increase_threshold_pct = self.config.get(
            "qubit_increase_threshold_pct", 0.05
        )

        # Carregar thresholds calibrados, se especificado
        if self.config.get("calibrated_thresholds"):
            self.apply_calibrated_thresholds(self.config["calibrated_thresholds"])

        # Definir qubits_normal dinamicamente baseado no número de encoders
        # ativos, caso não seja especificado na configuração recebida
        active_encoders: List[Any] = []
        if isinstance(self.config.get("encoders"), list):
            active_encoders = self.config.get("encoders", [])

        qmc = getattr(self.qualia_universe, "qmc", None)
        if qmc and hasattr(qmc, "quantum_encoding_interface"):
            qei = getattr(qmc, "quantum_encoding_interface", None)
            if qei and hasattr(qei, "get_all_encoders_list"):
                try:
                    active_encoders = qei.get_all_encoders_list()
                except Exception as exc:  # noqa: BLE001
                    logger.debug("ACE: erro ao obter lista de encoders ativos: %s", exc)

        if not (config and config.get("qubits_normal")):
            self.config["qubits_normal"] = len(active_encoders)

        logger.info(
            f"AdaptiveConsciousnessEvolution inicializado com configuração: {self.config}"
        )

        # YAA: Inicializar controlador de risco dinâmico se configurado
        if self.config.get("enable_dynamic_risk_control", False):
            try:
                from .market.dynamic_risk_controller import (
                    DynamicRiskController,
                )

                risk_profile = self.config.get("risk_profile", "balanced")
                dynamic_risk_config = self.config.get("dynamic_risk_config", {})

                self.dynamic_risk_controller = DynamicRiskController(
                    config=dynamic_risk_config,
                    adaptive_evolution=self,
                    risk_profile=risk_profile,
                )

                logger.info(
                    f"DynamicRiskController inicializado no ACE com perfil '{risk_profile}'"
                )

            except ImportError as e:
                logger.warning(f"Não foi possível importar DynamicRiskController: {e}")
            except Exception as e:
                logger.error(f"Erro ao inicializar DynamicRiskController: {e}")

    def receive_metacognitive_feedback(
        self, feedback_type: str, confidence_score: float, details: Dict
    ) -> None:
        """
        Recebe feedback do sistema de Metacognição para influenciar a adaptação.
        Este método é chamado por QUALIAMetacognitionTrading.
        Args:
            feedback_type (str): Tipo de feedback (e.g., "alta_confianca_positiva", "alta_confianca_negativa").
            confidence_score (float): Nível de confiança no feedback (0.0 a 1.0).
            details (Dict): Dicionário contendo detalhes da análise da QPM (e.g., avg_pnl_perc, win_rate).
        """
        logger.info(
            f"ACE: Recebido feedback metacognitivo - Tipo: {feedback_type}, Confiança: {confidence_score:.2f}"
        )
        logger.debug(f"ACE: Detalhes do feedback: {details}")
        self.last_metacognitive_feedback = {
            "type": feedback_type,
            "confidence": confidence_score,
            "details": details,
        }

        if not dynamic_feature_toggle("adaptive_aggressiveness", default=False):
            logger.debug("ACE: ajustes de agressividade desativados por feature flag")
            return

        # YAA: Esta lógica de ajuste da agressividade será influenciada/sobrescrita
        # pelas diretivas diretas passadas para assess_and_adapt, se disponíveis.
        # Modular a agressividade da adaptação ou os thresholds com base no feedback
        # Limites para agressividade para evitar valores extremos
        min_aggressiveness = 0.7  # Menos reativo, mais estável
        max_aggressiveness = 1.5  # Mais reativo
        neutral_aggressiveness = 1.0

        if feedback_type == "alta_confianca_positiva":
            # Se as diretivas explícitas não alterarem a agressividade, este feedback da QPM pode.
            # A ideia é que `ace_modulation_directives` tem precedência.
            self.adaptation_aggressiveness = max(
                min_aggressiveness,
                self.adaptation_aggressiveness * (1 - (0.2 * confidence_score)),
            )  # Reduz agressividade proporcional à confiança
            logger.info(
                f"ACE: Feedback positivo da QPM. Agressividade de adaptação (potencialmente) ajustada para {self.adaptation_aggressiveness:.2f}"
            )

        elif feedback_type == "alta_confianca_negativa":
            self.adaptation_aggressiveness = min(
                max_aggressiveness,
                self.adaptation_aggressiveness * (1 + (0.3 * confidence_score)),
            )  # Aumenta agressividade proporcional à confiança
            logger.info(
                f"ACE: Feedback negativo da QPM. Agressividade de adaptação (potencialmente) ajustada para {self.adaptation_aggressiveness:.2f}"
            )

        elif feedback_type == "baixa_confianca_mista":
            self.adaptation_aggressiveness = min(
                max_aggressiveness * 0.8,
                self.adaptation_aggressiveness * (1 + (0.1 * confidence_score)),
            )
            logger.info(
                f"ACE: Feedback misto/incerto da QPM. Agressividade de adaptação (potencialmente) ajustada para {self.adaptation_aggressiveness:.2f}"
            )

        elif feedback_type in [
            "sem_historico_relevante",
            "historico_insuficiente_relevante",
            "erro_analise_qpm",
            "sem_assinatura_quantica",
            "sem_historico_q_score_unico",
            "historico_insuficiente_q_score_dominante",
        ]:
            previous_aggressiveness = self.adaptation_aggressiveness
            if self.adaptation_aggressiveness > neutral_aggressiveness:
                self.adaptation_aggressiveness = max(
                    neutral_aggressiveness,
                    self.adaptation_aggressiveness * 0.95,
                )
            logger.info(
                "ACE: Feedback da QPM não conclusivo/ausente. "
                f"Agressividade ajustada de {previous_aggressiveness:.2f} para {self.adaptation_aggressiveness:.2f}"
            )
            if details.get("memory_empty"):
                logger.info("ACE: Ausência de feedback atribuída à memória QPM vazia.")
            elif details.get("similarity_threshold_used"):
                logger.info(
                    "ACE: Ausência de feedback possivelmente causada por limiar de similaridade elevado"
                )
        # YAA: Adicionar tratamento para feedback_combinado_neutro
        elif feedback_type == "feedback_combinado_neutro":
            # Para feedback neutro, podemos optar por não alterar a agressividade
            # ou reverter para um valor base/neutro se quisermos um comportamento mais estável.
            # Por ora, vamos logar e manter a agressividade ou considerar resetar para neutro.
            # Resetar para neutro pode ser mais seguro se este feedback indica incerteza.
            current_aggressiveness = self.adaptation_aggressiveness
            self.adaptation_aggressiveness = (
                neutral_aggressiveness  # Exemplo: resetar para neutro
            )
            logger.info(
                f"ACE: Recebido feedback 'feedback_combinado_neutro'. Agressividade de adaptação ajustada de {current_aggressiveness:.2f} para {self.adaptation_aggressiveness:.2f}."
            )
        else:
            logger.warning(
                f"ACE: Tipo de feedback metacognitivo desconhecido: {feedback_type}. Agressividade não alterada por este feedback."
            )

    def _calculate_market_complexity(self, market_data: Dict[str, Any]) -> float:
        """
        Calcula um índice de complexidade do mercado com base nos dados fornecidos.
        Agora espera 'volatility' e 'non_linearity' no market_data.

        Args:
            market_data: Dicionário contendo dados de mercado recentes.
                         Ex: {"volatility": 0.5, "non_linearity": 0.8}

        Returns:
            Um float entre 0 e 1 representando a complexidade do mercado.
        """
        volatility_metric = market_data.get(
            "volatility"
        )  # Espera a volatilidade já processada
        non_linearity_metric = market_data.get("non_linearity")

        if volatility_metric is not None and non_linearity_metric is not None:
            # Ambas as métricas já estão, ou deveriam estar, numa escala razoável (e.g., 0-1).
            # volatility_for_reconfig (passada como "volatility") é min(1.0, raw_vol * 30)
            # non_linearity é 1 - abs(autocorr_lag1)
            # Uma média simples pode ser um bom ponto de partida.
            calculated_complexity = (
                float(volatility_metric) + float(non_linearity_metric)
            ) / 2.0
            # Garantir que o resultado final esteja entre 0 e 1
            normalized_complexity = np.clip(calculated_complexity, 0.0, 1.0)

            logger.debug(
                f"Complexidade calculada a partir de volatility ({volatility_metric:.4f}) e non_linearity ({non_linearity_metric:.4f}): {normalized_complexity:.4f}"
            )
            return normalized_complexity
        else:
            missing_keys = []
            if volatility_metric is None:
                missing_keys.append("'volatility'")
            if non_linearity_metric is None:
                missing_keys.append("'non_linearity'")

            logger.warning(
                f"Chaves {', '.join(missing_keys)} ausentes em market_data para calcular complexidade. Retornando 0.5 (normal). Dados recebidos: {market_data}"
            )
            return (
                0.5  # Default para complexidade normal se não houver dados suficientes
            )

    def _calculate_volatility(self, prices: List[float]) -> float:
        """
        Calcula a volatilidade (desvio padrão dos retornos) de uma série de preços.
        Implementação da função privada solicitada.
        """
        if not prices or len(prices) < 2:
            return 0.0
        returns = np.diff(prices) / prices[:-1]
        return float(np.std(returns))

    def _measure_non_linearity(
        self, prices: List[float], *, use_gpu: bool | None = None
    ) -> float:
        """Calcula um índice de não-linearidade usando entropia de permutação.

        Esta métrica avalia a complexidade de uma série temporal considerando a
        distribuição de padrões ordinais. Valores próximos a ``0`` indicam uma
        série altamente previsível (linear), enquanto valores próximos a ``1``
        indicam comportamento mais aleatório.

        Parameters
        ----------
        prices
            Lista de preços históricos em ordem temporal.

        Returns
        -------
        float
            Entropia de permutação normalizada entre ``0`` e ``1``.
        """

        if not prices or len(prices) < 4:
            return 0.0

        if use_gpu is None:
            try:
                from qualia.utils.hardware_acceleration import gpu_available
            except Exception:  # pragma: no cover - optional import
                gpu_available = lambda: False  # type: ignore
            use_gpu = gpu_available()

        xp = np
        if use_gpu:
            try:  # pragma: no cover - optional dependency
                import cupy as cp

                xp = cp  # type: ignore[assignment]
            except Exception:  # pragma: no cover - fallback
                xp = np

        data = xp.asarray(prices, dtype=float)
        order = 3
        delay = 1
        n_embed = len(data) - (order - 1) * delay
        if n_embed <= 0:
            return 0.0

        patterns = xp.array(
            [data[i : i + order * delay : delay] for i in range(n_embed)]
        )
        ranks = xp.apply_along_axis(lambda x: xp.argsort(x).argsort(), 1, patterns)
        unique, counts = xp.unique(ranks, axis=0, return_counts=True)
        probs = counts.astype(float) / counts.sum()
        pe = -xp.sum(probs * xp.log2(probs))
        max_entropy = xp.log2(math.factorial(order))
        result = float(pe / max_entropy)
        try:
            return float(result.get())  # type: ignore[attr-defined]
        except Exception:  # pragma: no cover - non cupy or get unavailable
            return result

    def get_dynamic_risk_levels(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Retorna os níveis de risco dinâmicos atuais para um símbolo.

        Args:
            symbol: Símbolo do ativo

        Returns:
            Dicionário com níveis de risco ou None se não disponível
        """
        if symbol in self.risk_calibration_results:
            return self.risk_calibration_results[symbol]

        if self.dynamic_risk_controller:
            result = self.dynamic_risk_controller.get_current_risk_levels(symbol)
            if result:
                return {
                    "stop_loss_price": result.stop_loss_price,
                    "take_profit_price": result.take_profit_price,
                    "atr_value": result.atr_value,
                    "market_regime": result.market_regime,
                    "calibration_timestamp": result.timestamp.isoformat(),
                    "adjustment_reason": result.adjustment_reason,
                }

        return None

    def get_all_dynamic_risk_levels(self) -> Dict[str, Dict[str, Any]]:
        """
        Retorna todos os níveis de risco dinâmicos atuais.

        Returns:
            Dicionário com todos os níveis de risco por símbolo
        """
        return copy.deepcopy(self.risk_calibration_results)

    def is_dynamic_risk_enabled(self) -> bool:
        """
        Verifica se o controle de risco dinâmico está habilitado.

        Returns:
            True se habilitado
        """
        return self.dynamic_risk_controller is not None

    def get_risk_controller_metrics(self) -> Optional[Dict[str, Any]]:
        """
        Retorna métricas do controlador de risco dinâmico.

        Returns:
            Métricas do controlador ou None se não disponível
        """
        if self.dynamic_risk_controller:
            return self.dynamic_risk_controller.get_performance_metrics()
        return None

    # ------------------------------------------------------------------
    def apply_calibrated_thresholds(
        self, path_or_dict: Union[str, Path, Dict[str, float]]
    ) -> None:
        """Apply calibrated complexity thresholds to the ACE instance.

        Parameters
        ----------
        path_or_dict:
            Either a dictionary with ``complexity_threshold_calm`` and
            ``complexity_threshold_volatile`` keys or a path to a JSON file
            containing those values.
        """

        if isinstance(path_or_dict, (str, Path)):
            try:
                with open(path_or_dict, "r", encoding="utf-8") as fh:
                    data = json.load(fh)
            except Exception as exc:  # noqa: BLE001
                logger.warning(
                    "ACE: Falha ao carregar thresholds calibrados de %s: %s",
                    path_or_dict,
                    exc,
                )
                return
        elif isinstance(path_or_dict, dict):
            data = path_or_dict
        else:
            logger.warning(
                "ACE: Formato de thresholds calibrados inválido: %s",
                type(path_or_dict),
            )
            return

        calm_val = data.get("complexity_threshold_calm")
        volatile_val = data.get("complexity_threshold_volatile")
        if calm_val is None or volatile_val is None:
            logger.warning("ACE: thresholds calibrados não contêm as chaves esperadas")
            return

        try:
            calm_f = float(calm_val)
            volatile_f = float(volatile_val)
        except (TypeError, ValueError):
            logger.warning("ACE: valores de thresholds calibrados inválidos")
            return

        calm_f = float(np.clip(calm_f, self.complexity_min_threshold_calm, 1.0))
        volatile_f = float(
            np.clip(volatile_f, 0.0, self.complexity_max_threshold_volatile)
        )

        self.base_complexity_threshold_calm = calm_f
        self.base_complexity_threshold_volatile = volatile_f
        self.config["complexity_threshold_calm"] = calm_f
        self.config["complexity_threshold_volatile"] = volatile_f

        logger.info(
            "ACE: thresholds calibrados aplicados - calm=%.4f volatile=%.4f",
            calm_f,
            volatile_f,
        )

    def _should_throttle_qubit_increase(
        self, target_n_qubits: int, complexity_metric: float
    ) -> bool:
        """Verifica se um aumento de qubits deve ser ignorado por falta de variação de complexidade."""
        if not self.qualia_universe:
            return False

        current_q = getattr(self.qualia_universe, "n_qubits", 0)
        if target_n_qubits <= current_q:
            return False

        last_metric = getattr(self.qualia_universe, "last_qubit_increase_metric", None)
        if last_metric is None:
            return False

        change = abs(complexity_metric - last_metric)
        if change < self.qubit_increase_threshold_pct:
            logger.info(
                "ACE: Aumento de qubits bloqueado pelo throttle; "
                f"mudança de complexidade {change:.4f} < {self.qubit_increase_threshold_pct:.4f}"
            )
            return True

        return False

    @classmethod
    def _auto_scr_depth(cls, num_qubits: int, target_otoc: float = 0.98) -> int:
        """Return depth with OTOC closest to ``target_otoc``.

        Parameters
        ----------
        num_qubits
            Number of qubits for which to estimate scrambling depth.
        target_otoc
            Desired OTOC value used to select the depth. Default is ``0.98``.

        Returns
        -------
        int
            Recommended scrambling depth. Falls back to ``ceil(1.5*num_qubits)``
            when no lookup data is available.
        """

        if cls._otoc_lookup_df is None:
            csv_path = REPO_ROOT / "data" / "lookup" / "otoc_scrambling_data.csv"
            try:
                cls._otoc_lookup_df = pd.read_csv(csv_path)
            except Exception as exc:  # pragma: no cover - file may not exist
                logger.debug("ACE: falha ao carregar dados de OTOC: %s", exc)
                cls._otoc_lookup_df = None

        df = cls._otoc_lookup_df
        if df is not None:
            subset = df[df["num_qubits"] == num_qubits]
            if not subset.empty:
                idx = (subset["otoc"] - target_otoc).abs().idxmin()
                return int(subset.loc[idx, "scr_depth"])

        return math.ceil(1.5 * num_qubits)

    def assess_and_adapt(
        self,
        market_obs_for_adaptation: Dict[str, Any],
        # YAA: Alterado para Dict para compatibilidade com
        # dataclass serializada
        ace_modulation_directives: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """
        Avalia a complexidade do mercado e adapta a configuração do QUALIAUniverse se necessário.
        Deve ser chamado periodicamente (ex: a cada 5 minutos).

        Args:
            market_obs_for_adaptation: Dados de mercado recentes para calcular a complexidade.
                                     No QUALIARealTimeTrader, isso é passado como um dict com chaves
                                     como 'symbol', 'timeframe', 'volatility', 'non_linearity'.
            ace_modulation_directives: Diretivas opcionais do MetacognitiveContext (como um dicionário)
                                       para modular o comportamento da ACE.
                                       Ex: {'universe_target_complexity': 'increase', 'adaptation_rate_factor': 0.8}

        Returns:
            True se uma reconfiguração foi acionada, False caso contrário.
        """
        # YAA: Aplicar diretivas de modulação da metacognição, se disponíveis
        initial_aggressiveness = (
            self.adaptation_aggressiveness
        )  # Preservar para possível restauração
        target_complexity_directive = None  # e.g. 'increase', 'decrease', 'maintain'
        adaptation_rate_factor_directive = 1.0  # e.g. 0.8 (slower), 1.2 (faster)
        override_on_metacognition = False
        qubit_increase = 0
        skip_circuit_mutation = False

        if ace_modulation_directives:
            logger.info(
                f"ACE: Recebidas diretivas de modulação: {ace_modulation_directives}"
            )
            # Atualizar agressividade com base nas diretivas, se presentes
            if ace_modulation_directives.get("adaptation_rate_factor") is not None:
                self.adaptation_aggressiveness = initial_aggressiveness * float(
                    ace_modulation_directives["adaptation_rate_factor"]
                )
                self.adaptation_aggressiveness = np.clip(
                    self.adaptation_aggressiveness, 0.5, 1.5
                )  # Manter em limites razoáveis
                logger.info(
                    f"ACE: Agressividade de adaptação ajustada por diretiva para {self.adaptation_aggressiveness:.2f}"
                )

            target_complexity_directive = ace_modulation_directives.get(
                "universe_target_complexity"
            )
            if target_complexity_directive:
                logger.info(
                    f"ACE: Diretiva de complexidade do universo: {target_complexity_directive}"
                )

            override_on_metacognition = bool(
                ace_modulation_directives.get("override_on_metacognition", False)
            )
            qubit_increase = int(ace_modulation_directives.get("qubit_increase", 0))
            skip_circuit_mutation = bool(
                ace_modulation_directives.get("skip_circuit_mutation", False)
            )

        if skip_circuit_mutation:
            logger.info(
                "ACE: skip_circuit_mutation ativo - reconfiguração do circuito ignorada."
            )
            return False

        # YAA: Passo 1 - Ajuste Dinâmico dos Thresholds de Complexidade
        volatility_metric = market_obs_for_adaptation.get(
            "volatility", 0.05
        )  # Usar um default pequeno se ausente
        # Clamp volatility_metric para evitar ajustes extremos nos thresholds
        clamped_volatility_metric = np.clip(volatility_metric, 0.0, 1.0)

        dynamic_threshold_calm = self.base_complexity_threshold_calm * (
            1 + clamped_volatility_metric * self.complexity_threshold_volatility_factor
        )
        dynamic_threshold_calm = np.clip(
            dynamic_threshold_calm,
            self.complexity_min_threshold_calm,
            self.base_complexity_threshold_volatile - 0.05,
        )  # Evitar cruzar com volátil

        dynamic_threshold_volatile = self.base_complexity_threshold_volatile * (
            1 + clamped_volatility_metric * self.complexity_threshold_volatility_factor
        )
        dynamic_threshold_volatile = np.clip(
            dynamic_threshold_volatile,
            self.base_complexity_threshold_calm + 0.05,
            self.complexity_max_threshold_volatile,
        )  # Evitar cruzar com calmo

        # Atualizar os thresholds na config interna para _calculate_market_complexity usar
        current_config_threshold_calm = self.config["complexity_threshold_calm"]
        current_config_threshold_volatile = self.config["complexity_threshold_volatile"]
        self.config["complexity_threshold_calm"] = dynamic_threshold_calm
        self.config["complexity_threshold_volatile"] = dynamic_threshold_volatile
        logger.debug(
            f"ACE: Thresholds de complexidade dinâmicos: Calmo={dynamic_threshold_calm:.3f} (base={self.base_complexity_threshold_calm:.3f}), Volátil={dynamic_threshold_volatile:.3f} (base={self.base_complexity_threshold_volatile:.3f}) baseado em volatilidade={volatility_metric:.3f}"
        )

        # YAA: Usar volatilidade e não-linearidade diretamente de market_obs_for_adaptation
        # Estes valores já são calculados e normalizados em QUALIARealTimeTrader
        # A função _calculate_market_complexity interna pode ser simplificada ou usar estes diretamente.
        # Por agora, vamos assumir que _calculate_market_complexity usa os valores da config que acabamos de ajustar.
        market_complexity_index = self._calculate_market_complexity(
            market_obs_for_adaptation
        )
        logger.info(
            f"ACE: Índice de complexidade de mercado calculado: {market_complexity_index:.4f}"
        )
        if market_complexity_index > 0.4:
            qubit_increase = max(qubit_increase, 7)
            max_q = self.config.get("max_qubits", 12)
            current_q = getattr(self.qualia_universe, "n_qubits", 0)
            qubit_increase = min(qubit_increase, max(0, max_q - current_q))

        # YAA: Passo 2 - Throttle de Adaptação
        non_linearity_metric = market_obs_for_adaptation.get(
            "non_linearity", 0.5
        )  # Usar um default se ausente
        clamped_non_linearity_metric = np.clip(non_linearity_metric, 0.0, 1.0)

        counts_diversity_ratio = market_obs_for_adaptation.get("counts_diversity_ratio")
        local_delta_factor = self.delta_threshold_non_linearity_factor
        if (
            counts_diversity_ratio is not None
            and counts_diversity_ratio < MIN_COUNTS_DIVERSITY_RATIO
        ):
            local_delta_factor = 0.02

        dynamic_delta_threshold = (
            clamped_non_linearity_metric
            * local_delta_factor
            / max(self.adaptation_aggressiveness, 0.1)
        )

        complexity_metric_change = abs(
            market_complexity_index - self.previous_market_complexity_metric
        )

        if (
            complexity_metric_change < dynamic_delta_threshold
            and target_complexity_directive is None
        ):
            logger.info(
                f"ACE: Mudança de complexidade ({complexity_metric_change:.4f}) abaixo do threshold dinâmico ({dynamic_delta_threshold:.4f}) "
                f"e sem diretiva de override. Adaptação não acionada pelo throttle."
            )
            self.previous_market_complexity_metric = market_complexity_index
            # Restaurar agressividade se foi modificada por diretiva apenas para esta chamada
            self.adaptation_aggressiveness = initial_aggressiveness
            # Restaurar thresholds da config para os valores base, caso _calculate_market_complexity seja chamado de novo antes da próxima adaptação.
            self.config["complexity_threshold_calm"] = current_config_threshold_calm
            self.config["complexity_threshold_volatile"] = (
                current_config_threshold_volatile
            )
            return False

        # Determinar novo nível de complexidade
        # YAA: Lógica restaurada e corrigida para usar os thresholds dinâmicos de self.config
        if (
            market_complexity_index < self.config["complexity_threshold_calm"]
        ):  # Usa os valores dinâmicos em self.config
            new_complexity_level = "calm"
        elif (
            market_complexity_index > self.config["complexity_threshold_volatile"]
        ):  # Usa os valores dinâmicos em self.config
            new_complexity_level = "volatile"
        else:
            new_complexity_level = "normal"

        logger.info(
            f"ACE: Novo nível de complexidade determinado (pré-diretiva): {new_complexity_level} baseado em índice {market_complexity_index:.4f}"
        )

        # Considerar diretiva de complexidade do universo, que pode sobrescrever o
        # nível calculado ou forçar uma reconfiguração.
        reconfigure_forced_by_directive = False

        # Atualiza o nível de complexidade atual.
        # No final (se a adaptação prosseguir ou não), atualizar
        self.previous_market_complexity_metric = market_complexity_index

        if self.current_complexity_level != new_complexity_level:
            previous_level = self.current_complexity_level
            logger.info(
                f"ACE: Mudança de nível de complexidade detectada ou forçada: {previous_level} -> {new_complexity_level}"
            )
            log_event(
                event_type="ace.complexity_transition",
                payload={
                    "previous_level": previous_level,
                    "new_level": new_complexity_level,
                    "market_complexity_index": market_complexity_index,
                },
                source="adaptive_evolution",
                level="info",
            )
            if trace:
                tracer = trace.get_tracer(__name__)
                span_cm = tracer.start_as_current_span(
                    "ace.complexity_transition",
                    attributes={
                        "previous_level": previous_level,
                        "new_level": new_complexity_level,
                    },
                )
            else:
                from contextlib import nullcontext

                span_cm = nullcontext()

            with span_cm:
                self.current_complexity_level = new_complexity_level

            # YAA: Realizar recalibração de risco dinâmico quando há mudança de complexidade
            if self.dynamic_risk_controller:
                try:
                    # Preparar dados para recalibração
                    calibration_data = market_obs_for_adaptation.copy()
                    calibration_data.update(
                        {
                            "complexity_level": new_complexity_level,
                            "volatility": volatility_metric,
                            "non_linearity": non_linearity_metric,
                            "market_complexity_index": market_complexity_index,
                        }
                    )

                    # Executar recalibração automática
                    risk_results = (
                        self.dynamic_risk_controller.integrate_with_adaptive_evolution(
                            calibration_data
                        )
                    )

                    # Armazenar resultados
                    if risk_results.get("integration_status") == "success":
                        self.risk_calibration_results.update(
                            risk_results.get("calibration_results", {})
                        )
                        logger.info(
                            f"ACE: Recalibração de risco dinâmico completada para {len(risk_results.get('calibration_results', {}))} símbolos"
                        )
                    else:
                        logger.warning(
                            f"ACE: Recalibração de risco falhou: {risk_results.get('error_message', 'Erro desconhecido')}"
                        )

                except Exception as e:
                    logger.error(
                        f"ACE: Erro durante recalibração de risco dinâmico: {e}"
                    )

            # Preparar parâmetros de reconfiguração
            params_to_reconfigure = {}
            if self.current_complexity_level == "calm":
                params_to_reconfigure["n_qubits"] = self.config["qubits_calm"]
                params_to_reconfigure["entanglement_style"] = self.config[
                    "entanglement_calm"
                ]
                params_to_reconfigure["scr_depth"] = self.config["scr_depth_calm"]
                params_to_reconfigure["measure_frequency"] = self.config[
                    "measure_frequency_calm"
                ]
            elif self.current_complexity_level == "volatile":
                params_to_reconfigure["n_qubits"] = self.config["qubits_volatile"]
                params_to_reconfigure["entanglement_style"] = self.config[
                    "entanglement_volatile"
                ]
                params_to_reconfigure["scr_depth"] = self.config["scr_depth_volatile"]
                params_to_reconfigure["measure_frequency"] = self.config[
                    "measure_frequency_volatile"
                ]
            else:  # Normal
                params_to_reconfigure["n_qubits"] = self.config["qubits_normal"]
                params_to_reconfigure["entanglement_style"] = self.config[
                    "entanglement_normal"
                ]
                params_to_reconfigure["scr_depth"] = self.config["scr_depth_normal"]
                params_to_reconfigure["measure_frequency"] = self.config[
                    "measure_frequency_normal"
                ]

            if override_on_metacognition:
                current_q = getattr(
                    self.qualia_universe,
                    "n_qubits",
                    params_to_reconfigure.get("n_qubits", 0),
                )
                params_to_reconfigure["n_qubits"] = current_q + max(qubit_increase, 2)
                reconfigure_forced_by_directive = True

            # YAA: Clipar target_n_qubits usando min_qubits e max_qubits da config
            min_q = self.config.get("min_qubits", 2)
            max_q = self.config.get(
                "max_qubits", 12
            )  # O JSON pode ter definido max_qubits para 10
            if "n_qubits" in params_to_reconfigure:
                current_target_n_qubits = params_to_reconfigure["n_qubits"]
                params_to_reconfigure["n_qubits"] = np.clip(
                    current_target_n_qubits, min_q, max_q
                )
                if params_to_reconfigure["n_qubits"] != current_target_n_qubits:
                    logger.info(
                        f"ACE: n_qubits ({current_target_n_qubits}) ajustado para {params_to_reconfigure['n_qubits']} para respeitar limites [{min_q}-{max_q}]."
                    )

            if self.qualia_universe and hasattr(self.qualia_universe, "reconfigure"):
                target_q = params_to_reconfigure.get(
                    "n_qubits", getattr(self.qualia_universe, "n_qubits", 0)
                )
                if self._should_throttle_qubit_increase(
                    target_q, market_complexity_index
                ):
                    self.adaptation_aggressiveness = initial_aggressiveness
                    self.config["complexity_threshold_calm"] = (
                        current_config_threshold_calm
                    )
                    self.config["complexity_threshold_volatile"] = (
                        current_config_threshold_volatile
                    )
                    self.previous_market_complexity_metric = market_complexity_index
                    return False
                if skip_circuit_mutation:
                    logger.info(
                        "ACE: skip_circuit_mutation ativo - reconfiguração do circuito ignorada."
                    )
                    self.adaptation_aggressiveness = initial_aggressiveness
                    self.config["complexity_threshold_calm"] = (
                        current_config_threshold_calm
                    )
                    self.config["complexity_threshold_volatile"] = (
                        current_config_threshold_volatile
                    )
                    self.previous_market_complexity_metric = market_complexity_index
                    return False
                logger.info(
                    f"Acionando reconfiguração do QUALIAUniverse para o nível '{self.current_complexity_level}' com params: {params_to_reconfigure}"
                )
                try:
                    self.qualia_universe.reconfigure(
                        **params_to_reconfigure,
                        qubit_increase_metric=market_complexity_index,
                    )
                    return True
                except Exception as e:
                    logger.error(f"Erro ao tentar reconfigurar QUALIAUniverse: {e}")
                    return False
            else:
                logger.warning(
                    "QUALIAUniverse não fornecido ou não suporta reconfiguração."
                )
                return False
        else:
            logger.info(
                f"ACE: Nenhuma mudança de complexidade necessária. Nível atual: {self.current_complexity_level}"
            )

            # YAA: Mesmo sem mudança de complexidade, verificar se precisa de recalibração periódica
            if self.dynamic_risk_controller:
                try:
                    calibration_data = market_obs_for_adaptation.copy()
                    calibration_data.update(
                        {
                            "complexity_level": self.current_complexity_level,
                            "volatility": volatility_metric,
                            "non_linearity": non_linearity_metric,
                            "market_complexity_index": market_complexity_index,
                        }
                    )

                    # Verificar recalibração periódica
                    risk_results = (
                        self.dynamic_risk_controller.integrate_with_adaptive_evolution(
                            calibration_data
                        )
                    )

                    if risk_results.get(
                        "integration_status"
                    ) == "success" and risk_results.get("calibration_results"):
                        self.risk_calibration_results.update(
                            risk_results.get("calibration_results", {})
                        )
                        logger.debug(
                            f"ACE: Recalibração periódica completada para {len(risk_results.get('calibration_results', {}))} símbolos"
                        )

                except Exception as e:
                    logger.debug(f"ACE: Erro durante recalibração periódica: {e}")

            if override_on_metacognition:
                params_to_reconfigure = {}
                if self.current_complexity_level == "calm":
                    params_to_reconfigure["n_qubits"] = self.config["qubits_calm"]
                    params_to_reconfigure["entanglement_style"] = self.config[
                        "entanglement_calm"
                    ]
                    params_to_reconfigure["scr_depth"] = self.config["scr_depth_calm"]
                    params_to_reconfigure["measure_frequency"] = self.config[
                        "measure_frequency_calm"
                    ]
                elif self.current_complexity_level == "volatile":
                    params_to_reconfigure["n_qubits"] = self.config["qubits_volatile"]
                    params_to_reconfigure["entanglement_style"] = self.config[
                        "entanglement_volatile"
                    ]
                    params_to_reconfigure["scr_depth"] = self.config[
                        "scr_depth_volatile"
                    ]
                    params_to_reconfigure["measure_frequency"] = self.config[
                        "measure_frequency_volatile"
                    ]
                else:
                    params_to_reconfigure["n_qubits"] = self.config["qubits_normal"]
                    params_to_reconfigure["entanglement_style"] = self.config[
                        "entanglement_normal"
                    ]
                    params_to_reconfigure["scr_depth"] = self.config["scr_depth_normal"]
                    params_to_reconfigure["measure_frequency"] = self.config[
                        "measure_frequency_normal"
                    ]

                current_q = getattr(
                    self.qualia_universe,
                    "n_qubits",
                    params_to_reconfigure.get("n_qubits", 0),
                )
                params_to_reconfigure["n_qubits"] = current_q + max(qubit_increase, 2)

                min_q = self.config.get("min_qubits", 2)
                max_q = self.config.get("max_qubits", 12)
                params_to_reconfigure["n_qubits"] = int(
                    np.clip(params_to_reconfigure["n_qubits"], min_q, max_q)
                )

                if self.qualia_universe and hasattr(
                    self.qualia_universe, "reconfigure"
                ):
                    target_q = params_to_reconfigure.get("n_qubits", current_q)
                    if self._should_throttle_qubit_increase(
                        target_q, market_complexity_index
                    ):
                        self.adaptation_aggressiveness = initial_aggressiveness
                        self.config["complexity_threshold_calm"] = (
                            current_config_threshold_calm
                        )
                        self.config["complexity_threshold_volatile"] = (
                            current_config_threshold_volatile
                        )
                        self.previous_market_complexity_metric = market_complexity_index
                        return False
                    if skip_circuit_mutation:
                        logger.info(
                            "ACE: skip_circuit_mutation ativo - reconfiguração do circuito ignorada."
                        )
                        self.adaptation_aggressiveness = initial_aggressiveness
                        self.config["complexity_threshold_calm"] = (
                            current_config_threshold_calm
                        )
                        self.config["complexity_threshold_volatile"] = (
                            current_config_threshold_volatile
                        )
                        self.previous_market_complexity_metric = market_complexity_index
                        return False
                    logger.info(
                        f"Acionando reconfiguração do QUALIAUniverse para o nível '{self.current_complexity_level}' com params: {params_to_reconfigure}"
                    )
                    try:
                        self.qualia_universe.reconfigure(
                            **params_to_reconfigure,
                            qubit_increase_metric=market_complexity_index,
                        )
                        return True
                    except Exception as e:
                        logger.error(f"Erro ao tentar reconfigurar QUALIAUniverse: {e}")
                        return False
                else:
                    logger.warning(
                        "QUALIAUniverse não fornecido ou não suporta reconfiguração."
                    )
                    return False

            # Restaurar agressividade se foi modificada por diretiva
            self.adaptation_aggressiveness = initial_aggressiveness
            # Restaurar thresholds da config para os valores base
            self.config["complexity_threshold_calm"] = current_config_threshold_calm
            self.config["complexity_threshold_volatile"] = (
                current_config_threshold_volatile
            )
            return False


# Exemplo de uso (pode ser movido para um script de teste)
if __name__ == "__main__":
    initialize_logging(log_level="INFO")

    class MockUniverse:
        def __init__(self):
            self.n_qubits = 8
            self.entanglement_style = "linear"
            self.scr_depth = 10
            self.measure_frequency = 1
            logger.info(
                f"MockUniverse inicializado: qubits={self.n_qubits}, ent={self.entanglement_style}, depth={self.scr_depth}, meas_freq={self.measure_frequency}"
            )

        def reconfigure(self, **kwargs):
            logger.info(f"MockUniverse: Reconfigurando com {kwargs}")
            self.n_qubits = kwargs.get("n_qubits", self.n_qubits)
            self.entanglement_style = kwargs.get(
                "entanglement_style", self.entanglement_style
            )
            self.scr_depth = kwargs.get("scr_depth", self.scr_depth)
            self.measure_frequency = kwargs.get(
                "measure_frequency", self.measure_frequency
            )
            logger.info(
                f"MockUniverse: Novos params: qubits={self.n_qubits}, ent={self.entanglement_style}, depth={self.scr_depth}, meas_freq={self.measure_frequency}"
            )

    mock_uni = MockUniverse()
    adapter = AdaptiveConsciousnessEvolution(qualia_universe=mock_uni)

    # Simular dados de mercado calmo
    market_data_calm = {
        "prices": np.linspace(100, 101, 50).tolist()
    }  # Baixa volatilidade
    logger.info("\nTestando com mercado calmo...")
    adapter.assess_and_adapt(market_data_calm)

    # Simular dados de mercado normal
    market_data_normal = {
        "prices": (np.sin(np.linspace(0, 10, 50)) * 2 + 100).tolist()
    }  # Volatilidade moderada
    logger.info("\nTestando com mercado normal...")
    adapter.assess_and_adapt(market_data_normal)

    # Simular dados de mercado volátil
    prices_volatile = [100]
    for _ in range(49):
        prices_volatile.append(
            prices_volatile[-1] + np.random.normal(0, 2)
        )  # Alta volatilidade
    market_data_volatile = {"prices": prices_volatile}
    logger.info("\nTestando com mercado volátil...")
    adapter.assess_and_adapt(market_data_volatile)

    # Testar funções privadas
    logger.info(
        f"_calculate_volatility([10,11,10.5,12,11.5]): {adapter._calculate_volatility([10,11,10.5,12,11.5])}"
    )
    logger.info(
        f"_measure_non_linearity([10,11,10.5,12,11.5,11,10,9]): {adapter._measure_non_linearity([10,11,10.5,12,11.5,11,10,9])}"
    )
