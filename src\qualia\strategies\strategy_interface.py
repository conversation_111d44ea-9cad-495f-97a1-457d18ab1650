"""
Interface base para todas as estratégias de trading em QUALIA.
"""

import logging
from ..utils.logger import get_logger
from abc import ABC, abstractmethod
from typing import Callable, Dict, Any, Optional, List, Type, Union
from ..config.config_manager import ConfigManager
from pydantic import BaseModel, Field
import numpy as np
import pandas as pd

from ..strategies.strategy_factory import register_strategy_in_factory

logger = get_logger(__name__)
# logger.setLevel(logging.DEBUG)  # Deixar DEBUG para expor problemas de inicialização


class TradingContext(BaseModel):  # ou TypedDict
    """
    Contexto fornecido para a estratégia no momento da decisão.
    Pode ser expandido conforme necessário.
    """

    symbol: str
    timeframe: str
    current_price: float
    # Campos extras como wallet_state ou current_position podem ser incluídos
    # conforme a estratégia evoluir.
    ohlcv: pd.DataFrame  # Para acesso a dados históricos se necessário
    timestamp: pd.Timestamp  # Momento da decisão
    wallet_state: Dict[str, Any]
    liquidity: Optional[float] = None
    volatility: Optional[float] = None
    strategy_metrics: Optional[Dict[str, Any]] = None
    quantum_metrics: Optional[Dict[str, Any]] = None
    market_state: Optional[str] = None
    risk_mode: Optional[str] = None
    current_bid: Optional[float] = None
    current_ask: Optional[float] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        arbitrary_types_allowed = True


class OrderDecision(BaseModel):
    """
    Representa a decisão de trading tomada pela estratégia.
    Inclui sinal, confiança e, opcionalmente, níveis de stop-loss e take-profit.
    """

    signal: str  # "BUY", "SELL", "HOLD", "CLOSE"
    confidence: float = Field(default=0.0, ge=0.0, le=1.0)
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    # Lista de motivos ou justificativas para a decisão
    reasons: List[str] = Field(default_factory=list)
    # Fator opcional de tamanho de ordem sugerido pela estratégia
    size_factor: Optional[float] = Field(default=None, ge=0.0, le=1.0)
    # Identificador opcional da decisão para rastreamento
    decision_id: Optional[str] = None
    # Referência ao snapshot de QSP usado durante a análise
    qsp_snapshot_id: Optional[str] = None


class TradingStrategy(ABC):
    """
    Classe base abstrata para todas as estratégias de trading.
    As estratégias devem herdar desta classe e implementar os métodos abstratos.
    """

    strategy_name: str = "BaseTradingStrategy"  # Nome padrão
    strategy_alias: Optional[str] = None  # Alias para registro na fábrica
    version: str = "0.1.0"

    def __init__(
        self,
        params: Optional[Dict[str, Any]] = None,
        context: Optional[Dict[str, Any]] = None,
        config_manager: Optional["ConfigManager"] = None,
    ) -> None:
        """Construtor da estratégia.

        Parameters
        ----------
        params
            Dicionário de parâmetros específicos da estratégia.
        context
            Dicionário de contexto mais amplo, podendo incluir QPM, Universo, etc.
        config_manager
            Instância de :class:`~qualia.config.config_manager.ConfigManager` com
            as configurações carregadas.
        """
        self.params = params if params is not None else {}
        self.context = context if context is not None else {}
        self.config_manager = config_manager
        self.symbol = self.context.get("symbol", self.params.get("symbol", "N/A"))
        self.timeframe = self.context.get(
            "timeframe", self.params.get("timeframe", "N/A")
        )

        if self.symbol == "N/A":
            raise ValueError(
                "Contexto incompleto: 'symbol' deve ser fornecido em params ou context"
            )

        # Inicializar QPM se fornecido no contexto
        self.qpm_memory = self.context.get("qpm_instance")
        if self.qpm_memory:
            logger.info(
                f"Estratégia {self.strategy_name} ({self.symbol}@{self.timeframe}) inicializada com QPM."
            )
        else:
            logger.info(
                f"Estratégia {self.strategy_name} ({self.symbol}@{self.timeframe}) inicializada sem QPM."
            )

        # Permitir que a estratégia acesse o módulo de consciência QUALIA para análises internas
        self.qualia_consciousness_module = self.context.get(
            "qualia_consciousness_module"
        )
        if self.qualia_consciousness_module:
            logger.info(
                f"Estratégia {self.strategy_name} ({self.symbol}@{self.timeframe}) tem acesso ao QUALIAConsciousness."
            )

        # Permitir acesso ao executivo de metacognição
        self.metacognition_executive = self.context.get("metacognition_executive")
        if self.metacognition_executive:
            logger.info(
                f"Estratégia {self.strategy_name} ({self.symbol}@{self.timeframe}) tem acesso ao QUALIAMetacognitionTrading."
            )

        # YAA TASK-06: Inicializar o contêiner para o histórico de dados do warmup
        self._initial_history: Optional[pd.DataFrame] = None

        self._initialize_specific_parameters()
        logger.info(
            f"Estratégia {self.strategy_name} v{self.version} para {self.symbol}@{self.timeframe} inicializada com params: {self.params}"
        )

    def _initialize_specific_parameters(self) -> None:
        """
        Método para ser sobrescrito por subclasses para inicializar parâmetros
        específicos da estratégia a partir de self.params.
        """
        # Exemplo: self.my_param = self.params.get("my_param_key", default_value)
        pass

    def set_initial_history(self, history: pd.DataFrame) -> None:
        """
        Define o histórico de dados inicial para a estratégia.
        Este método é chamado pelo DataWarmupManager para injetar dados
        pré-carregados, evitando que a estratégia precise buscá-los.
        """
        if not history.empty:
            # Usar o alias se disponível, senão o nome da classe
            strategy_id = self.strategy_alias or self.strategy_name
            logger.info(
                f"TASK-06: {strategy_id}: Injetando histórico inicial com {len(history)} candles."
            )
            self._initial_history = history.copy()
        else:
            strategy_id = self.strategy_alias or self.strategy_name
            logger.warning(f"TASK-06: {strategy_id}: Tentativa de injetar histórico vazio.")

    @abstractmethod
    def analyze_market(
        self,
        market_data: pd.DataFrame,
        trading_context: TradingContext,
        quantum_metrics: Optional[Dict[str, Any]] = None,
        similar_past_patterns: Optional[List[Dict[str, Any]]] = None,
    ) -> OrderDecision:
        """
        Analisa os dados de mercado e o contexto para tomar uma decisão de trading.

        A assinatura esperada é ``analyze_market(market_data, trading_context, quantum_metrics=None, similar_past_patterns=None)``.

        Args:
            market_data: DataFrame do Pandas com dados OHLCV e indicadores.
                         A última linha representa o candle mais recente (não fechado).
            trading_context: Instância de TradingContext com informações relevantes.
                Algumas implementações aceitam ``context`` como nome alternativo
                para compatibilidade retroativa.
            quantum_metrics: Dicionário opcional com métricas quânticas do sistema QUALIA.
            similar_past_patterns: Lista opcional de padrões passados similares recuperados da QPM.

        Returns:
            Um objeto OrderDecision contendo o sinal de trading ("BUY", "SELL", "HOLD", "CLOSE"),
            confiança, e opcionalmente stop-loss e take-profit.
        """
        pass

    def get_info(self) -> Dict[str, Any]:
        """
        Retorna informações sobre a estratégia.
        Pode ser sobrescrito para incluir mais detalhes.
        """
        return {
            "strategy_name": self.strategy_name,
            "version": self.version,
            "symbol": self.symbol,
            "timeframe": self.timeframe,
            "params": self.params,
        }

    def update_parameters(self, new_params: Dict[str, Any]) -> None:
        """
        Atualiza os parâmetros da estratégia em tempo de execução.
        Útil para otimização ou adaptação.
        """
        logger.debug(
            f"Estratégia {self.strategy_name} ({self.symbol}@{self.timeframe}): Atualizando parâmetros de {self.params} para {new_params}"
        )
        self.params.update(new_params)
        self._initialize_specific_parameters()  # Re-inicializar com os novos parâmetros
        logger.debug(
            f"Estratégia {self.strategy_name} ({self.symbol}@{self.timeframe}): Parâmetros atualizados para {self.params}"
        )

    def update_from_evolved(self, evolved_strategy: "TradingStrategy") -> None:
        """Substitui os parâmetros atuais pelos de ``evolved_strategy``.

        Parameters
        ----------
        evolved_strategy : TradingStrategy
            Estratégia gerada pelo processo evolutivo cujo conjunto de
            parâmetros deve substituir o da instância atual.
        """
        if not isinstance(evolved_strategy, TradingStrategy):
            raise TypeError("evolved_strategy deve ser uma TradingStrategy")

        logger.debug(
            f"Estratégia {self.strategy_name} sendo atualizada com parâmetros da estratégia evoluída {evolved_strategy.strategy_name}"
        )

        self.update_parameters(evolved_strategy.params)

    def get_params(self) -> Dict[str, Any]:
        """Return current strategy parameters."""

        return self.params

    def set_params(self, params: Dict[str, Any]) -> None:
        """Set strategy parameters in bulk."""

        self.params = params


def register_strategy(
    name: Optional[str] = None,
    category: str = "generic",
    description: str = "",
    version: str = "1.0.0",
    tags: Optional[List[str]] = None,
    legacy: bool = False,
) -> Callable[[Type[TradingStrategy]], Type[TradingStrategy]]:
    """Decorador para registrar uma classe de estratégia.

    Parameters
    ----------
    name : str, optional
        Alias utilizado quando ``strategy_alias`` não está definido na classe.
    category : str, optional
        Categoria textual da estratégia.
    description : str, optional
        Descrição resumida.
    version : str, optional
        Versão semântica.
    tags : list of str, optional
        Marcadores para classificação.
    legacy : bool, optional
        Marca a estratégia como legada ou desabilitada.

    Returns
    -------
    Callable[[Type[TradingStrategy]], Type[TradingStrategy]]
        Função decoradora que registra a classe na ``StrategyFactory``.
    """
    logger.debug(
        f"INTERFACE: Decorador @register_strategy chamado com name/alias '{name}'"
    )
    tags = tags if tags is not None else []

    def decorator(cls: Type[TradingStrategy]) -> Type[TradingStrategy]:
        logger.debug(
            f"INTERFACE: Decorador interno executando para classe {cls.__name__} com name/alias '{name}'"
        )

        # Determinar o alias primário para registro na fábrica
        primary_alias = getattr(
            cls, "strategy_alias", None
        )  # Tenta obter de um atributo de classe
        if not primary_alias and name:
            primary_alias = name  # Usa o argumento 'name' do decorador como alias
        if not primary_alias:  # Fallback se nenhum foi fornecido
            primary_alias = cls.__name__.replace("Strategy", "").lower()
            if not primary_alias:  # Fallback final para o nome da classe em minúsculas
                primary_alias = cls.__name__.lower()

        # A função real de registro que interage com o dicionário _strategy_registry
        # está em strategy_factory.py para centralizar a manipulação do registro.
        register_strategy_in_factory(primary_alias, cls)
        logger.debug(
            f"INTERFACE: Chamada para register_strategy_in_factory('{primary_alias}', {cls.__name__}) concluída."
        )
        if name and name != primary_alias:
            register_strategy_in_factory(name, cls)
            logger.debug(
                "INTERFACE: Alias adicional '%s' registrado para %s via decorador",
                name,
                cls.__name__,
            )

        # Metadados da estratégia podem ser mantidos na própria classe caso a
        # StrategyFactory venha a utilizá-los futuramente.
        # Exemplo de como armazenar na classe:
        cls.strategy_metadata = {
            "category": category,
            "description": description,
            "version": version,
            "tags": tags,
            "alias": primary_alias,
            "legacy": legacy,
        }
        # logger.debug(f"INTERFACE: Metadados definidos para {cls.__name__}: {cls.strategy_metadata}")

        return cls

    return decorator
