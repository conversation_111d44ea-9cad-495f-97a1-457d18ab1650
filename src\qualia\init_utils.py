from __future__ import annotations

import logging
import async<PERSON>
import functools
from pathlib import Path
from threading import Lock
from typing import Any, Dict, Optional, List
import importlib

import dotenv

from .utils.logger import get_logger, setup_logging as central_setup_logging
from .config.settings import get_env
from .core.universe import QUALIAQuantumUniverse
from .memory import get_qpm_instance
from .core.consciousness import QUALIAConsciousness
from .risk.manager import create_risk_manager
from .core.simulation_qast_core import SimulationQASTCore
from .farsight.holographic_extension import HolographicFarsightEngine
from .consciousness.holographic_universe import HolographicMarketUniverse
from .personas.base import BasePersona


_missing_env_warned = False
_missing_env_lock = Lock()
logger = get_logger(__name__)


def load_environment(env_path: Optional[str] = None) -> None:
    """Load environment variables from a ``.env`` file."""
    global _missing_env_warned

    root = Path(__file__).resolve().parents[2]
    env_file = Path(
        env_path or get_env("QUALIA_ENV_PATH", str(root / ".env"), warn=False)
    )

    if env_file.exists():
        logger.info(
            "Carregando variáveis de ambiente do arquivo",
            context={"env_file": str(env_file)},
        )
        dotenv.load_dotenv(dotenv_path=str(env_file), override=True)
    else:
        with _missing_env_lock:
            if not _missing_env_warned:
                logger.warning(
                    "Arquivo .env não encontrado",
                    context={"env_file": str(env_file)},
                )
                _missing_env_warned = True


def setup_logging(log_level: str, log_file: Path) -> None:
    """Wrapper to configure QUALIA logging via :mod:`qualia.utils.logger`."""

    central_setup_logging(
        {
            "level": log_level,
            "file": str(log_file),
            "console": True,
        }
    )


_core_components: Dict[str, Any] | None = None


def _initialize_personas(persona_configs: List[Dict[str, Any]]) -> List[BasePersona]:
    """Initializes persona agents based on configuration."""
    if not persona_configs:
        logger.warning("Nenhuma configuração de persona encontrada. Usando defaults.")
        persona_configs = [
            {
                "type": "RetailCluster",
                "name": "RetailInvestor",
                "module": "qualia.personas.retail_cluster",
                "config": {},
            },
            {
                "type": "MomentumQuant",
                "name": "MomentumTrader",
                "module": "qualia.personas.momentum_quant",
                "config": {},
            },
        ]

    personas: List[BasePersona] = []
    for p_config in persona_configs:
        try:
            persona_module_path = p_config.get("module")
            persona_type = p_config.get("type")
            persona_name = p_config.get("name")
            persona_config = p_config.get("config", {})
            if not persona_module_path or not persona_type or not persona_name:
                raise KeyError("Persona 'module', 'type' and 'name' are required.")

            module = importlib.import_module(persona_module_path)
            persona_class = getattr(module, persona_type)
            personas.append(persona_class(persona_name, persona_config))

        except (KeyError, ImportError, AttributeError) as e:
            logger.warning(
                f"Skipping persona with invalid config: {p_config} - Error: {e}"
            )

    logger.info(f"Initialized {len(personas)} personas.")
    return personas


async def setup_core_components(
    config: Dict[str, Any], symbols: List[str], timeframes: List[str]
) -> Dict[str, Any]:
    """Setup QUALIA core components with dependency injection."""

    global _core_components
    if _core_components is not None:
        return _core_components

    # CORREÇÃO YAA: Converter Settings object para dict se necessário
    if hasattr(config, "__dict__"):
        # Se é um objeto Settings, converter para dict
        config_dict = {}
        for attr in dir(config):
            if not attr.startswith("_") and not callable(getattr(config, attr)):
                config_dict[attr] = getattr(config, attr)
    else:
        config_dict = config or {}

    # YAA: Parâmetros válidos para QUALIAQuantumUniverse (baseado no construtor)
    valid_universe_params = {
        "n_qubits",
        "scr_depth",
        "base_lambda",
        "alpha",
        "retro_strength",
        "num_ctc_qubits",
        "measure_frequency",
        "thermal_coefficient",
        "initial_state_vector",
        "backend_name",
        "qast_feedback_enabled",
        "lambda_factor_multiplier",
        "enrichment_cycles",
        "adaptive_threshold",
        "entanglement_style",
        "max_history_size",
        "initial_state_type",
        "eqci_config",
        "informational_mass",
        "hawking_factor",
        "mass_threshold",
        "thermal_J_coupling",
        "thermal_beta_inverse_temp",
        "shots",
        "qpu_steps",
        "thermal_noise_enabled",
        "thermal_noise_temperature",
        "temperature",
        "allow_qft_fallback",
        "qpm_config",
        "qpm_instance",
        "min_counts_diversity_ratio",
        "max_circuit_depth",
        "max_circuit_operations",
        "statsd_client",
        "otoc_frequency",
        "use_gpu",
    }

    default_universe_cfg = {
        "n_qubits": 8,
        "scr_depth": 10,
        "base_lambda": 0.5,
        "alpha": 0.1,
        "retro_strength": 0.1,
        "num_ctc_qubits": 0,
    }

    # YAA: Filtrar apenas parâmetros válidos do config_dict
    filtered_config = {
        k: v for k, v in config_dict.items() if k in valid_universe_params
    }
    default_universe_cfg.update(filtered_config)

    default_qpm_cfg = {"enable_warmstart": False}
    qpm_config = (
        config_dict.get("qpm", {}) if isinstance(config_dict.get("qpm"), dict) else {}
    )
    default_qpm_cfg.update(qpm_config)

    if "risk_manager" not in default_qpm_cfg:
        default_qpm_cfg["risk_manager"] = None

    qpm = get_qpm_instance(default_qpm_cfg)
    
    # YAA: Executar a inicialização bloqueante do Universe em um executor de thread
    logger.info("Iniciando inicialização do QUALIAQuantumUniverse em thread separada para não bloquear o loop...")
    loop = asyncio.get_running_loop()
    blocking_constructor = functools.partial(
        QUALIAQuantumUniverse, **default_universe_cfg, qpm_instance=qpm
    )
    universe = await loop.run_in_executor(None, blocking_constructor)
    logger.info("QUALIAQuantumUniverse inicializado com sucesso.")

    consciousness_config = (
        config_dict.get("consciousness", {})
        if isinstance(config_dict.get("consciousness"), dict)
        else {}
    )
    consciousness = QUALIAConsciousness(**consciousness_config)

    # Inicialização do SimulationQASTCore e suas dependências
    farsight_engine = HolographicFarsightEngine(**config_dict.get("farsight_engine", {}))

    social_universe_config = config_dict.get("social_simulation_universe", {})
    holographic_market_universe = HolographicMarketUniverse(**social_universe_config)

    # Carregar Personas da configuração
    personas = _initialize_personas(config_dict.get("personas", []))

    simulation_core = SimulationQASTCore(
        personas=personas,
        farsight_engine=farsight_engine,
        social_universe=holographic_market_universe,
        config=config_dict.get("simulation_qast_core", {}),
    )

    components = {
        "universe": universe,
        "qpm": qpm,
        "consciousness": consciousness,
        "simulation_core": simulation_core,  # Adiciona o simulation_core
        "multi_exchange_manager": None,  # Placeholder para injeção posterior
    }
    _core_components = components
    logger.info("Core components initialized via setup_core_components()")

    # YAA: Multi-Exchange Manager para diversificação dimensional
    multi_exchange_config = config_dict.get("multi_exchange", {})

    if multi_exchange_config.get("enabled", False):
        logger.info("🌌 Configurando Multi-Exchange Manager")

        from .core.multi_exchange_manager import MultiExchangeManager

        try:
            multi_exchange_manager = MultiExchangeManager(config_dict)

            # Tentar inicializar para verificar conectividade
            # CORREÇÃO YAA: Não inicializar aqui para evitar blocking,
            # mas verificar configuração básica
            dimensional_config = multi_exchange_config.get("dimensional", {})
            exchanges_config = multi_exchange_config.get("exchanges", {})

            enabled_exchanges = [
                name
                for name, ex_config in exchanges_config.items()
                if ex_config.get("enabled", False)
            ]

            if enabled_exchanges:
                components["multi_exchange_manager"] = multi_exchange_manager
                logger.info(
                    f"✅ Multi-Exchange Manager configurado com exchanges: {enabled_exchanges}"
                )
                logger.info(
                    f"🎯 Spread mínimo: {dimensional_config.get('min_spread_threshold', 'N/A')} | "
                    f"Latência máxima: {dimensional_config.get('max_latency_ms', 'N/A')}ms"
                )
            else:
                logger.warning("⚠️ Multi-Exchange habilitado mas nenhuma exchange ativa")
                components["multi_exchange_manager"] = None

        except Exception as e:
            logger.error(f"❌ Falha ao configurar Multi-Exchange Manager: {e}")
            # Não quebrar o sistema por causa do multi-exchange
            components["multi_exchange_manager"] = None
    else:
        logger.info("📄 Multi-Exchange desabilitado - usando exchange única")
        components["multi_exchange_manager"] = None

    return components
