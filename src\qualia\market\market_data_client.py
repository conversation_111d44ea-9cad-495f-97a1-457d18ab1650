from __future__ import annotations

from typing import Any, Dict, Optional, TYPE_CHECKING

import pandas as pd

if TYPE_CHECKING:
    from .base_integration import CryptoDataFetcher
    from ..common.specs import MarketSpec


class MarketDataClient:
    """Client for ticker and OHLCV operations."""

    def __init__(self, fetcher: "CryptoDataFetcher") -> None:
        self._fetcher = fetcher

    async def fetch_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Fetch ticker data delegating to the underlying fetcher."""
        return await self._fetcher.fetch_ticker(symbol)

    async def watch_ticker(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Watch ticker updates via WebSocket."""
        return await self._fetcher.watch_ticker(symbol)

    async def fetch_ohlcv(
        self, spec: "MarketSpec", since: Optional[int] = None, limit: int = 100
    ) -> pd.DataFrame:
        """Fetch OHLCV data for a given market specification."""
        return await self._fetcher.fetch_ohlcv(spec, since=since, limit=limit)
