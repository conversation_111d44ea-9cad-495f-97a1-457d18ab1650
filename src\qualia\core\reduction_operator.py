"""Quantum information reduction operator.

Implements a simplified version of Ô_RIQ from the MICQI model. The
operator models decoherence through an effective collapse rate derived
from the self‐gravitation energy of a mass distribution.

Examples
--------
>>> op = ReductionOperator({"masses": [1.0], "positions": [[0.0]]})
>>> await op.reduce_information(np.eye(1), timestamp=0.0)
"""

from __future__ import annotations

from dataclasses import dataclass
import logging
from typing import Iterable, Tuple, List, Any

import numpy as np

logger = logging.getLogger(__name__)

# Physical constants (in SI units)
_PLANCK_REDUCED = 1.054_571_817e-34  # ħ
_GRAVITATIONAL = 6.674_30e-11  # G


@dataclass
class ReductionState:
    """Result of a reduction step."""

    reduced_density: np.ndarray
    quantum_entropy: float
    collapse_rate: float
    timestamp: float


class ReductionOperator:
    """Operator implementing Ô_RIQ from the MICQI model."""

    def __init__(
        self, config: dict | None = None, *, history_maxlen: int = 1000
    ) -> None:
        cfg = config or {}
        self.phi = np.array(cfg.get("phi", [1.0]), dtype=float)
        masses = np.array(cfg.get("masses", [1.0]), dtype=float)
        positions = np.array(cfg.get("positions", [[0.0]] * len(masses)), dtype=float)
        self.mass_operator = np.diag(masses)
        self.positions = positions
        self.collapse_rate_base = float(cfg.get("gamma_or", 1.0))
        self.current_state: ReductionState | None = None
        self.reduction_history: List[ReductionState] = []
        self.history_maxlen = int(history_maxlen)

    # ------------------------------------------------------------------
    def _self_gravitation_energy(self) -> float:
        masses = np.diag(self.mass_operator)
        pos = self.positions.reshape(len(masses), -1)
        e_g = 0.0
        for i in range(len(masses)):
            for j in range(i + 1, len(masses)):
                r = np.linalg.norm(pos[i] - pos[j])
                if r > 0:
                    e_g += masses[i] * masses[j] / r
        return _GRAVITATIONAL * e_g

    def _von_neumann_entropy(self, rho: np.ndarray) -> float:
        vals = np.linalg.eigvalsh(rho)
        vals = np.clip(vals.real, 0.0, 1.0)
        nz = vals[vals > 0]
        if nz.size == 0:
            return 0.0
        return float(-np.sum(nz * np.log(nz)))

    async def reduce_information(
        self, density_matrix: np.ndarray, timestamp: float
    ) -> ReductionState:
        """Apply a single reduction step to ``density_matrix``."""
        if density_matrix.shape[0] != density_matrix.shape[1]:
            raise ValueError("density_matrix must be square")

        integral = float(np.sum(self.phi * np.diag(self.mass_operator)))
        e_g = self._self_gravitation_energy()
        gamma_or = e_g / _PLANCK_REDUCED
        rate = self.collapse_rate_base * integral**2 * gamma_or
        reduced = density_matrix - rate * density_matrix
        entropy = self._von_neumann_entropy(reduced)
        state = ReductionState(reduced, entropy, rate, timestamp)
        self.current_state = state
        self.reduction_history.append(state)
        if len(self.reduction_history) > self.history_maxlen:
            self.reduction_history.pop(0)
        return state

    def is_reduced(self) -> bool:
        """Check if reduction has significant effect."""
        return bool(np.any(self.mass_operator))

    def get_state_dict(self) -> dict[str, Any]:
        """Return a serializable representation of the operator state."""
        return {
            "collapse_rate_base": self.collapse_rate_base,
            "history_length": len(self.reduction_history),
            "current_rate": (
                self.current_state.collapse_rate if self.current_state else 0.0
            ),
        }
