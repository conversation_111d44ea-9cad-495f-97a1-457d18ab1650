"""Observer Operator - Quantum measurement and state collapse.

Implements quantum observer effects for market state measurement.

"""

import numpy as np
from typing import List, Dict, <PERSON>, <PERSON><PERSON>, Optional
import os
import json
from dataclasses import dataclass, asdict
import logging
import asyncio
from enum import Enum

from qualia.config import config
from qualia.utils.persistence import convert_to_serializable
from qualia.metrics.quantum_metrics import l1_coherence

logger = logging.getLogger(__name__)


class ObservationType(Enum):
    """Types of quantum observations"""

    POSITION = "position"
    MOMENTUM = "momentum"
    ENERGY = "energy"
    PHASE = "phase"
    COHERENCE = "coherence"
    ENTANGLEMENT = "entanglement"


@dataclass
class QuantumMeasurement:
    """Represents a quantum measurement result"""

    measurement_id: str
    observation_type: ObservationType
    eigenvalue: float
    uncertainty: float
    collapse_probability: float
    measurement_time: float
    basis_state: np.ndarray


@dataclass
class ObserverState:
    """State representation for observer operations"""

    active_measurements: List[QuantumMeasurement]
    system_state: np.ndarray
    collapsed_state: Optional[np.ndarray]
    measurement_basis: np.ndarray
    uncertainty_principle: Dict[str, float]
    observation_effect: float
    coherence: float
    entanglement: float
    timestamp: float


class ObserverOperator:
    """
    Quantum-inspired observer operator for state measurement

    Implements quantum measurement theory applied to market data,
    including state collapse, uncertainty principle, and observer effects.
    """

    def __init__(
        self,
        user_config: Optional[Dict[str, Any]] = None,
        *,
        history_maxlen: int = 1000,
    ):
        """Initialize the observer with optional configuration values."""

        defaults = {
            "measurement_frequency": 1.0,
            "uncertainty_tolerance": 0.1,
            "collapse_threshold": 0.9,
            "observer_matrix": np.eye(2),
        }
        if user_config:
            defaults.update(user_config)

        self.measurement_frequency = defaults.get("measurement_frequency")  # Hz
        self.uncertainty_tolerance = defaults.get("uncertainty_tolerance")
        self.collapse_threshold = defaults.get("collapse_threshold")

        # Quantum measurement parameters
        self.planck_scale = 1e-6  # Effective Planck constant for market quantum effects
        self.decoherence_time = 60.0  # Seconds for quantum decoherence

        # Observer state tracking
        self.current_state: Optional[ObserverState] = None
        self.observer_history: List[ObserverState] = []
        self.history_maxlen = int(history_maxlen)
        self.measurement_queue: List[Tuple[ObservationType, float]] = []

        # Unitary observer matrix (U_obs)
        self.observer_matrix = np.array(defaults.get("observer_matrix"), dtype=complex)
        atol = config.observer_unitary_atol
        if self.observer_matrix.shape[0] != self.observer_matrix.shape[
            1
        ] or not np.allclose(
            self.observer_matrix @ self.observer_matrix.conj().T,
            np.eye(self.observer_matrix.shape[0]),
            atol=atol,
        ):
            logger.warning("observer_matrix provided is not unitary; using identity")
            self.observer_matrix = np.eye(2, dtype=complex)

        # Persistence configuration
        self.persistence_dir = config.observer_persistence_dir
        self.persistence_filename = config.observer_persistence_filename
        os.makedirs(self.persistence_dir, exist_ok=True)
        self._persistence_path = os.path.join(
            self.persistence_dir, self.persistence_filename
        )

        # Measurement basis vectors
        self._initialize_measurement_bases()

        # Scheduled measurements
        self.measurement_task: Optional[asyncio.Task] = None
        self.is_observing = False

        logger.info(
            f"ObserverOperator initialized with {self.measurement_frequency} Hz frequency"
        )

    def _initialize_measurement_bases(self):
        """Initialize orthogonal measurement basis vectors"""
        # Standard computational basis
        self.position_basis = np.array([[1, 0], [0, 1]], dtype=complex)

        # Momentum basis (Fourier transform of position)
        self.momentum_basis = np.array([[1, 1], [1, -1]], dtype=complex) / np.sqrt(2)

        # Energy eigenbasis (Pauli-Z)
        self.energy_basis = np.array([[1, 0], [0, -1]], dtype=complex)

        # Phase basis (Pauli-Y)
        self.phase_basis = np.array([[0, -1j], [1j, 0]], dtype=complex)

        # Coherence basis (Pauli-X)
        self.coherence_basis = np.array([[0, 1], [1, 0]], dtype=complex)

        # Default measurement basis
        self.current_measurement_basis = self.position_basis

    async def start_observation(self):
        """Start continuous quantum observation"""
        if not self.is_observing:
            self.is_observing = True
            self.measurement_task = asyncio.create_task(self._observation_loop())
            logger.info("Quantum observation started")

    async def stop_observation(self):
        """Stop continuous quantum observation"""
        self.is_observing = False
        if self.measurement_task:
            self.measurement_task.cancel()
            try:
                await self.measurement_task
            except asyncio.CancelledError:
                pass
        logger.info("Quantum observation stopped")

    async def _observation_loop(self):
        """Main observation loop for continuous measurement"""
        try:
            while self.is_observing:
                await asyncio.sleep(1.0 / self.measurement_frequency)

                # Process queued measurements
                if self.measurement_queue:
                    observation_type, timestamp = self.measurement_queue.pop(0)
                    await self._perform_measurement(observation_type, timestamp)

        except asyncio.CancelledError:
            logger.info("Observation loop cancelled")
        except Exception as e:
            logger.error(f"Error in observation loop: {e}")

    async def observe_system(
        self,
        quantum_state: np.ndarray,
        observation_type: ObservationType,
        timestamp: float,
    ) -> ObserverState:
        """Perform quantum observation on a system state.

        Parameters
        ----------
        quantum_state : np.ndarray
            Current quantum state vector.
        observation_type : ObservationType
            Type of measurement to perform.
        timestamp : float
            Current timestamp.

        Returns
        -------
        ObserverState
            Observer state containing the measurement results.
        """
        try:
            # Normalize quantum state
            if np.linalg.norm(quantum_state) > 0:
                quantum_state = quantum_state / np.linalg.norm(quantum_state)
            else:
                # Create default state if input is zero
                quantum_state = np.array([1.0, 0.0])

            # Extend state to match measurement basis if needed
            if len(quantum_state) < 2:
                quantum_state = np.pad(quantum_state, (0, 2 - len(quantum_state)))[:2]
            elif len(quantum_state) > 2:
                quantum_state = quantum_state[:2]

            # Select measurement basis
            measurement_basis = self._select_measurement_basis(observation_type)

            # Perform quantum measurement
            measurement = await self._perform_quantum_measurement(
                quantum_state, observation_type, measurement_basis, timestamp
            )

            # Calculate state collapse
            collapsed_state = self._calculate_state_collapse(
                quantum_state, measurement, measurement_basis
            )

            # Calculate uncertainty principle violations
            uncertainty_principle = self._calculate_uncertainty_relations(
                quantum_state, measurement_basis
            )

            # Calculate observer effect strength
            observation_effect = self._calculate_observer_effect(
                quantum_state, collapsed_state
            )

            try:
                from qualia.metrics.quantum_metrics import l1_coherence

                coherence_val = (
                    l1_coherence(collapsed_state)
                    if collapsed_state is not None
                    else l1_coherence(quantum_state)
                )
            except Exception:
                coherence_val = 1.0

            try:
                from .entanglement import multipartite_entanglement

                entanglement_val = (
                    multipartite_entanglement(collapsed_state)
                    if collapsed_state is not None
                    else multipartite_entanglement(quantum_state)
                )
            except Exception:
                entanglement_val = 0.0

            # Create observer state
            state = ObserverState(
                active_measurements=[measurement],
                system_state=quantum_state,
                collapsed_state=collapsed_state,
                measurement_basis=measurement_basis,
                uncertainty_principle=uncertainty_principle,
                observation_effect=observation_effect,
                coherence=float(coherence_val),
                entanglement=float(entanglement_val),
                timestamp=timestamp,
            )

            self.current_state = state
            self.observer_history.append(state)
            self.save_history()

            # Limit history size
            if len(self.observer_history) > self.history_maxlen:
                self.observer_history.pop(0)

            logger.debug(
                f"Quantum observation complete: type={observation_type.value}, "
                f"collapse_prob={measurement.collapse_probability:.3f}"
            )

            return state

        except Exception as e:
            logger.error(f"Error in quantum observation: {e}")
            return self._create_default_state(quantum_state, timestamp)

    def _select_measurement_basis(
        self, observation_type: ObservationType
    ) -> np.ndarray:
        """Select appropriate measurement basis for observation type"""
        basis_map = {
            ObservationType.POSITION: self.position_basis,
            ObservationType.MOMENTUM: self.momentum_basis,
            ObservationType.ENERGY: self.energy_basis,
            ObservationType.PHASE: self.phase_basis,
            ObservationType.COHERENCE: self.coherence_basis,
            ObservationType.ENTANGLEMENT: self.position_basis,  # Default for entanglement
        }

        return basis_map.get(observation_type, self.position_basis).copy()

    async def _perform_quantum_measurement(
        self,
        quantum_state: np.ndarray,
        observation_type: ObservationType,
        measurement_basis: np.ndarray,
        timestamp: float,
    ) -> QuantumMeasurement:
        """Perform the actual quantum measurement"""
        try:
            # Apply the observer unitary transformation
            transformed_state = self.observer_matrix @ quantum_state

            # Project state onto measurement basis
            projections = []
            eigenvalues = []

            for i, basis_vector in enumerate(measurement_basis):
                # Calculate projection amplitude
                projection_amplitude = np.vdot(basis_vector, transformed_state)
                probability = abs(projection_amplitude) ** 2
                projections.append(probability)
                eigenvalues.append(i)  # Simple eigenvalue assignment

            # Normalize probabilities
            total_prob = sum(projections)
            if total_prob > 0:
                projections = [p / total_prob for p in projections]

            # Quantum measurement (probabilistic collapse)
            measurement_outcome = np.random.choice(len(projections), p=projections)
            measured_eigenvalue = eigenvalues[measurement_outcome]
            collapse_probability = projections[measurement_outcome]

            # Calculate measurement uncertainty
            uncertainty = self._calculate_measurement_uncertainty(
                quantum_state, measurement_basis, observation_type
            )

            # Create measurement result
            measurement_id = (
                f"{observation_type.value}_{timestamp:.0f}_{measurement_outcome}"
            )

            measurement = QuantumMeasurement(
                measurement_id=measurement_id,
                observation_type=observation_type,
                eigenvalue=float(measured_eigenvalue),
                uncertainty=uncertainty,
                collapse_probability=collapse_probability,
                measurement_time=timestamp,
                basis_state=measurement_basis[measurement_outcome].copy(),
            )

            return measurement

        except Exception as e:
            logger.warning(f"Quantum measurement failed: {e}")
            return self._create_default_measurement(observation_type, timestamp)

    def _calculate_state_collapse(
        self,
        original_state: np.ndarray,
        measurement: QuantumMeasurement,
        measurement_basis: np.ndarray,
    ) -> Optional[np.ndarray]:
        """Calculate post-measurement collapsed state"""
        try:
            if measurement.collapse_probability >= self.collapse_threshold:
                # Strong measurement - collapse to eigenstate
                collapsed_state = measurement.basis_state.copy()

                # Normalize
                if np.linalg.norm(collapsed_state) > 0:
                    collapsed_state = collapsed_state / np.linalg.norm(collapsed_state)

                return collapsed_state
            else:
                # Weak measurement - partial collapse
                collapse_strength = measurement.collapse_probability

                # Linear combination of original and collapsed states
                target_basis = measurement.basis_state

                collapsed_state = (
                    1 - collapse_strength
                ) * original_state + collapse_strength * target_basis

                # Normalize
                if np.linalg.norm(collapsed_state) > 0:
                    collapsed_state = collapsed_state / np.linalg.norm(collapsed_state)

                return collapsed_state

        except Exception as e:
            logger.warning(f"State collapse calculation failed: {e}")
            return None

    def _calculate_measurement_uncertainty(
        self,
        quantum_state: np.ndarray,
        measurement_basis: np.ndarray,
        observation_type: ObservationType,
    ) -> float:
        """Calculate measurement uncertainty according to quantum principles"""
        try:
            # Calculate variance of measurement operator
            measurement_operator = measurement_basis

            # Expectation value
            expectation = np.real(
                np.vdot(quantum_state, measurement_operator @ quantum_state)
            )

            # Expectation of squared operator
            squared_operator = measurement_operator @ measurement_operator
            expectation_squared = np.real(
                np.vdot(quantum_state, squared_operator @ quantum_state)
            )

            # Variance (uncertainty squared)
            variance = expectation_squared - expectation**2
            uncertainty = np.sqrt(max(variance, 0))

            # Apply minimum uncertainty (effective Planck scale)
            uncertainty = max(uncertainty, self.planck_scale)

            return float(uncertainty)

        except Exception as e:
            logger.warning(f"Uncertainty calculation failed: {e}")
            return self.planck_scale

    def _calculate_uncertainty_relations(
        self, quantum_state: np.ndarray, measurement_basis: np.ndarray
    ) -> Dict[str, float]:
        """Calculate uncertainty relations between different observables"""
        uncertainty_relations = {}

        try:
            # Position-Momentum uncertainty
            position_uncertainty = self._calculate_measurement_uncertainty(
                quantum_state, self.position_basis, ObservationType.POSITION
            )
            momentum_uncertainty = self._calculate_measurement_uncertainty(
                quantum_state, self.momentum_basis, ObservationType.MOMENTUM
            )

            uncertainty_relations["position_momentum"] = (
                position_uncertainty * momentum_uncertainty
            )

            # Energy-Time uncertainty (using decoherence time)
            energy_uncertainty = self._calculate_measurement_uncertainty(
                quantum_state, self.energy_basis, ObservationType.ENERGY
            )
            time_uncertainty = self.decoherence_time

            uncertainty_relations["energy_time"] = energy_uncertainty * time_uncertainty

            # Phase-Amplitude uncertainty
            phase_uncertainty = self._calculate_measurement_uncertainty(
                quantum_state, self.phase_basis, ObservationType.PHASE
            )
            coherence_uncertainty = self._calculate_measurement_uncertainty(
                quantum_state, self.coherence_basis, ObservationType.COHERENCE
            )

            uncertainty_relations["phase_amplitude"] = (
                phase_uncertainty * coherence_uncertainty
            )

            return uncertainty_relations

        except Exception as e:
            logger.warning(f"Uncertainty relations calculation failed: {e}")
            return {"position_momentum": self.planck_scale}

    def _calculate_observer_effect(
        self, original_state: np.ndarray, collapsed_state: Optional[np.ndarray]
    ) -> float:
        """Calculate the strength of observer effect on the system"""
        try:
            if collapsed_state is None:
                return 0.0

            # Calculate fidelity between original and collapsed states
            fidelity = abs(np.vdot(original_state, collapsed_state)) ** 2

            # Observer effect is the complement of fidelity
            observer_effect = 1.0 - fidelity

            return float(observer_effect)

        except Exception as e:
            logger.warning(f"Observer effect calculation failed: {e}")
            return 0.0

    def _create_default_measurement(
        self, observation_type: ObservationType, timestamp: float
    ) -> QuantumMeasurement:
        """Create default measurement for error cases"""
        measurement_id = f"default_{observation_type.value}_{timestamp:.0f}"

        return QuantumMeasurement(
            measurement_id=measurement_id,
            observation_type=observation_type,
            eigenvalue=0.0,
            uncertainty=self.planck_scale,
            collapse_probability=0.5,
            measurement_time=timestamp,
            basis_state=np.array([1.0, 0.0]),
        )

    def _create_default_state(
        self, quantum_state: np.ndarray, timestamp: float
    ) -> ObserverState:
        """Create default observer state for error cases"""
        if len(quantum_state) < 2:
            quantum_state = np.array([1.0, 0.0])

        return ObserverState(
            active_measurements=[],
            system_state=quantum_state[:2],
            collapsed_state=None,
            measurement_basis=self.position_basis,
            uncertainty_principle={"position_momentum": self.planck_scale},
            observation_effect=0.0,
            coherence=1.0,
            entanglement=0.0,
            timestamp=timestamp,
        )

    async def _perform_measurement(
        self, observation_type: ObservationType, timestamp: float
    ):
        """Perform a scheduled measurement"""
        if self.current_state is not None:
            await self.observe_system(
                self.current_state.system_state, observation_type, timestamp
            )

    def schedule_measurement(self, observation_type: ObservationType, timestamp: float):
        """Schedule a measurement to be performed"""
        self.measurement_queue.append((observation_type, timestamp))

    def is_state_collapsed(self) -> bool:
        """Check if the current state has collapsed"""
        if self.current_state is None or self.current_state.collapsed_state is None:
            return False

        return (
            len(self.current_state.active_measurements) > 0
            and self.current_state.active_measurements[-1].collapse_probability
            >= self.collapse_threshold
        )

    def get_measurement_statistics(self) -> Dict[str, Any]:
        """Get statistics of recent measurements"""
        if not self.observer_history:
            return {}

        recent_states = self.observer_history[-100:]  # Last 100 states

        # Collect measurement data
        collapse_probabilities = []
        observer_effects = []
        uncertainties = []

        for state in recent_states:
            if state.active_measurements:
                collapse_probabilities.extend(
                    [m.collapse_probability for m in state.active_measurements]
                )
                uncertainties.extend([m.uncertainty for m in state.active_measurements])
            observer_effects.append(state.observation_effect)

        return {
            "total_measurements": len(collapse_probabilities),
            "average_collapse_probability": (
                np.mean(collapse_probabilities) if collapse_probabilities else 0.0
            ),
            "average_uncertainty": np.mean(uncertainties) if uncertainties else 0.0,
            "average_observer_effect": np.mean(observer_effects),
            "collapsed_states": sum(
                1 for p in collapse_probabilities if p >= self.collapse_threshold
            ),
        }

    def calculate_entropy(self) -> float:
        """Return normalized entropy of collapse probabilities."""

        if self.current_state is None or not self.current_state.active_measurements:
            return 0.0

        probs = np.array(
            [m.collapse_probability for m in self.current_state.active_measurements],
            dtype=float,
        )
        total = probs.sum()
        if total == 0:
            return 0.0
        probs = probs / total
        probs = probs[probs > 0]
        ent = float(np.sum(-probs * np.log(probs)))
        max_ent = np.log(probs.size)
        return ent / max_ent if max_ent > 0 else 0.0

    def get_quantum_summary(self) -> Dict[str, Any]:
        """Get summary of current quantum state"""
        if self.current_state is None:
            return {}

        return {
            "is_collapsed": self.is_state_collapsed(),
            "observer_effect": float(self.current_state.observation_effect),
            "active_measurements": len(self.current_state.active_measurements),
            "uncertainty_relations": self.current_state.uncertainty_principle,
            "measurement_frequency": self.measurement_frequency,
            "is_observing": self.is_observing,
            "timestamp": self.current_state.timestamp,
        }

    def average_uncertainty(self) -> float:
        """Return average measurement uncertainty from history."""
        values: list[float] = []
        for state in self.observer_history:
            values.extend(m.uncertainty for m in state.active_measurements)
        if not values:
            return 0.0
        return float(np.mean(values))

    def measurement_rate(self) -> float:
        """Return number of measurements per second based on history.

        Returns
        -------
        float
            Measurement frequency derived from the timestamps stored in
            :attr:`observer_history`.

        Examples
        --------
        >>> op = ObserverOperator()
        >>> op.measurement_rate()
        0.0
        """

        if len(self.observer_history) < 2:
            return 0.0

        start = self.observer_history[0].timestamp
        end = self.observer_history[-1].timestamp
        if end <= start:
            return 0.0

        count = sum(len(s.active_measurements) for s in self.observer_history)
        return float(count / (end - start))

    def get_state_dict(self) -> Dict[str, Any]:
        """Get serializable state dictionary"""
        return {
            "measurement_frequency": self.measurement_frequency,
            "uncertainty_tolerance": self.uncertainty_tolerance,
            "collapse_threshold": self.collapse_threshold,
            "is_observing": self.is_observing,
            "current_observer_effect": (
                float(self.current_state.observation_effect)
                if self.current_state
                else 0.0
            ),
            "current_coherence": (
                float(self.current_state.coherence)
                if self.current_state is not None
                else 1.0
            ),
            "current_entanglement": (
                float(self.current_state.entanglement)
                if self.current_state is not None
                else 0.0
            ),
            "is_collapsed": self.is_state_collapsed(),
            "total_measurements": len(self.observer_history),
            "queued_measurements": len(self.measurement_queue),
        }

    def _to_serializable(self, obj: Any) -> Any:
        """Convert ``obj`` into a JSON serializable form."""
        if isinstance(obj, dict):
            return {k: self._to_serializable(v) for k, v in obj.items()}
        if isinstance(obj, (list, tuple)):
            return [self._to_serializable(v) for v in obj]
        if isinstance(obj, np.ndarray):
            return [self._to_serializable(v) for v in obj.tolist()]
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, Enum):
            return obj.value
        if isinstance(obj, complex):
            return {"real": obj.real, "imag": obj.imag}
        return obj

    def save_history(self) -> None:
        """Persist ``observer_history`` to a JSON file."""
        data = {
            "observer_history": [
                self._to_serializable(asdict(s)) for s in self.observer_history
            ]
        }
        try:
            with open(self._persistence_path, "w", encoding="utf-8") as fh:
                json.dump(data, fh)
        except (OSError, TypeError, ValueError) as exc:
            logger.warning("Falha ao persistir observer history: %s", exc)

    def load_history(self) -> List[Dict[str, Any]]:
        """Load persisted history from disk."""
        if not os.path.exists(self._persistence_path):
            return []
        try:
            with open(self._persistence_path, "r", encoding="utf-8") as fh:
                data = json.load(fh)
                return data.get("observer_history", [])
        except (OSError, json.JSONDecodeError) as exc:
            logger.warning("Falha ao carregar observer history: %s", exc)
            return []


def observe_system(field: np.ndarray, observer: np.ndarray) -> np.ndarray:
    """Apply a linear observation transformation.

    Parameters
    ----------
    field
        Matrix representing the system state.
    observer
        Observation matrix to apply.
    """

    if not isinstance(field, np.ndarray) or not isinstance(observer, np.ndarray):
        raise TypeError("field and observer must be numpy arrays")
    if field.ndim != 2 or observer.ndim != 2:
        raise ValueError("field and observer must be two-dimensional")
    if field.shape[0] != field.shape[1]:
        raise ValueError("field must be square")
    if observer.shape != field.shape:
        raise ValueError("observer must have the same shape as field")

    atol = config.observer_unitary_atol
    if not np.allclose(observer @ observer.T, np.eye(observer.shape[0]), atol=atol):
        raise ValueError("observer must be unitary")

    return observer @ field @ observer.T


def apply_observer_adjustment(
    field: np.ndarray, observer_matrix: np.ndarray
) -> np.ndarray:
    """Apply the quantum observer adjustment operator.

    Parameters
    ----------
    field
        Matrix representing the system state.
    observer_matrix
        Unitary matrix describing the observer adjustment.

    Returns
    -------
    np.ndarray
        Adjusted field after observation.
    """

    if not isinstance(field, np.ndarray) or not isinstance(observer_matrix, np.ndarray):
        raise TypeError("field and observer_matrix must be numpy arrays")
    if field.ndim != 2 or observer_matrix.ndim != 2:
        raise ValueError("field and observer_matrix must be two-dimensional")
    if field.shape[0] != field.shape[1]:
        raise ValueError("field must be square")
    if observer_matrix.shape != field.shape:
        raise ValueError("observer_matrix must have the same shape as field")

    atol = config.observer_unitary_atol
    if not np.allclose(
        observer_matrix @ observer_matrix.conj().T,
        np.eye(observer_matrix.shape[0]),
        atol=atol,
    ):
        raise ValueError("observer_matrix must be unitary")

    return observer_matrix @ field @ observer_matrix.T


class QualiaSelfObserver:
    """Minimal self-observer used for tests."""

    def __init__(
        self,
        persistence_dir: str | None = None,
        persistence_filename: str | None = None,
    ) -> None:
        self.persistence_dir = persistence_dir or config.observer_persistence_dir
        self.persistence_filename = (
            persistence_filename or config.observer_persistence_filename
        )
        self.observed_patterns: List[Dict[str, Any]] = []
        self.entropy_deltas: List[Dict[str, Any]] = []

        os.makedirs(self.persistence_dir, exist_ok=True)
        self._filepath = os.path.join(self.persistence_dir, self.persistence_filename)
        if os.path.exists(self._filepath):
            try:
                with open(self._filepath, "r", encoding="utf-8") as fh:
                    data = json.load(fh)
                    self.observed_patterns = data.get("observed_patterns", [])
                    self.entropy_deltas = data.get("entropy_deltas", [])
            except (json.JSONDecodeError, OSError, TypeError) as exc:
                logger.warning(
                    "Arquivo de persistência do QualiaSelfObserver inválido ou corrompido: %s",
                    exc,
                )
                self.observed_patterns = []
                self.entropy_deltas = []

    def _persist(self) -> None:
        data = {
            "observed_patterns": self.observed_patterns,
            "entropy_deltas": self.entropy_deltas,
        }
        serializable = convert_to_serializable(data)
        try:
            with open(self._filepath, "w", encoding="utf-8") as fh:
                json.dump(serializable, fh)
        except (TypeError, ValueError) as exc:
            logger.warning("Falha ao persistir dados do QualiaSelfObserver: %s", exc)

    def observe_patterns(
        self, patterns: List[Dict[str, Any]], trace_id: str | None = None
    ) -> None:
        self.observed_patterns.extend([convert_to_serializable(p) for p in patterns])
        self._persist()

    def observe_entropy_delta(
        self,
        delta_entropy: float,
        new_entropy: float,
        improved: bool,
        trace_id: str | None = None,
    ) -> None:
        self.entropy_deltas.append(
            {
                "delta_entropy": float(delta_entropy),
                "new_entropy": float(new_entropy),
                "improved": bool(improved),
            }
        )
        self._persist()


class QuantumObserver:
    """Simple observer adjusting the field based on coherence."""

    def __init__(self, learning_rate: float = 0.01) -> None:
        self.learning_rate = float(learning_rate)

    def observe_and_adjust(self, field: np.ndarray, step: int) -> np.ndarray:
        """Return field adjusted by coherence-driven feedback.

        Parameters
        ----------
        field
            Quantum field array.
        step
            Current time step.

        Returns
        -------
        np.ndarray
            Adjusted field after applying observer feedback.
        """
        if not isinstance(field, np.ndarray):
            raise TypeError("field must be a numpy array")
        if field.ndim == 0:
            raise ValueError("field must have at least one dimension")
        coherence = l1_coherence(field)
        factor = 1.0 + self.learning_rate * coherence * float(step)
        return field * factor
