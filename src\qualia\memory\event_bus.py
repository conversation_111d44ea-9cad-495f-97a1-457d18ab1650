from __future__ import annotations

"""Tipos de eventos relacionados à camada de memória.

Reexporta :class:`SimpleEventBus` para compatibilidade e define eventos
utilizados durante a escrita e leitura de padrões persistentes.
"""

from dataclasses import dataclass
from typing import Any, Dict

from ..events import SystemEvent

# Import SimpleEventBus for backward compatibility
from ..utils.event_bus import EventBus as SimpleEventBus


@dataclass(kw_only=True)
class MemoryWritten(SystemEvent):
    """Payload for memory write events."""

    pattern_id: str
    metadata: Dict[str, Any] | None = None


@dataclass(kw_only=True)
class MemoryEvicted(SystemEvent):
    """Payload for memory eviction events."""

    pattern_id: str


@dataclass(kw_only=True)
class MemoryReset(SystemEvent):
    """Payload for memory reset events."""

    reason: str | None = None


@dataclass(kw_only=True)
class MemoryPersisted(SystemEvent):
    """Payload for memory persistence events."""

    path: str
    patterns: int


@dataclass(kw_only=True)
class MemoryLoaded(SystemEvent):
    """Payload for memory load events."""

    path: str
    patterns: int


# Constantes de streams mantidas para retrocompatibilidade,
# mas a abordagem baseada em tipos de evento é preferível.
IMAGE_STREAM = "stream.image"
NEWS_STREAM = "stream.news"
AUDIO_STREAM = "stream.audio"
VIDEO_STREAM = "stream.video"
SOCIAL_SENTIMENT_STREAM = "social.sentiment"
ONCHAIN_SIGNAL_STREAM = "onchain.signal"

__all__ = [
    "MemoryWritten",
    "MemoryEvicted",
    "MemoryReset",
    "MemoryPersisted",
    "MemoryLoaded",
    "SimpleEventBus",
    "NEWS_STREAM",
    "AUDIO_STREAM",
    "VIDEO_STREAM",
    "IMAGE_STREAM",
    "SOCIAL_SENTIMENT_STREAM",
    "ONCHAIN_SIGNAL_STREAM",
]
