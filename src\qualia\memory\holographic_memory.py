"""HolographicMemory - armazenamento vetorial com busca por similaridade.

Inspirado em :mod:`memory_system`, este módulo mantém vetores de características
associados a metadados. A consulta retorna itens mais recentes com pontuação
ponderada por decaimento temporal.
"""

from __future__ import annotations

import time
from dataclasses import dataclass
from typing import Any, Dict, List, Tuple

import numpy as np
from numpy.linalg import norm


@dataclass
class MemoryItem:
    """Representa um item armazenado na :class:`HolographicMemory`."""

    vector: np.ndarray
    centroid: float
    metadata: Dict[str, Any]
    timestamp: float


class HolographicMemory:
    """Armazena vetores e realiza busca considerando recência."""

    def __init__(
        self, max_items: int = 10000, half_life: float = 3600.0, decay_rate: float = 0.0
    ) -> None:
        """Inicializa a memória holográfica.

        Parameters
        ----------
        max_items : int, optional
            Quantidade máxima de vetores armazenados.
        half_life : float, optional
            Tempo em segundos para que a influência de um item seja reduzida pela
            metade durante consultas.
        """
        self.max_items = max_items
        self.half_life = half_life
        self.decay_rate = decay_rate
        self._data: List[MemoryItem] = []

    def _clean_memory(self, *, now: float | None = None) -> None:
        """Remove itens antigos respeitando ``decay_rate`` e ``max_items``."""

        now = now if now is not None else time.time()
        if self.decay_rate > 0:
            threshold = self.half_life * np.log(self.decay_rate) / np.log(0.5)
            self._data = [
                item for item in self._data if now - item.timestamp <= threshold
            ]
        excess = len(self._data) - self.max_items
        if excess > 0:
            self._data = self._data[excess:]

    def _encode_hologram(self, vector: np.ndarray) -> Tuple[float, np.ndarray]:
        """Return centroid and compressed residuals."""
        vec = np.asarray(vector, dtype=float)
        centroid = float(np.mean(vec))
        pad = (4 - (vec.size % 4)) % 4
        if pad:
            vec = np.pad(vec, (0, pad))
        residuals = vec - centroid
        compressed = residuals.reshape(-1, 4).mean(axis=1)
        return centroid, compressed

    def store(
        self,
        vector: np.ndarray,
        metadata: Dict[str, Any] | None = None,
        *,
        timestamp: float | None = None,
    ) -> None:
        """Armazena ``vector`` junto a ``metadata`` opcional."""
        meta = metadata or {}
        ts = timestamp if timestamp is not None else time.time()
        centroid, encoded = self._encode_hologram(vector)
        item = MemoryItem(encoded, centroid, meta, ts)
        self._data.append(item)
        self._clean_memory(now=ts)

    def _decay_weight(self, age: float) -> float:
        if self.half_life <= 0:
            return 1.0
        return 0.5 ** (age / self.half_life)

    def query(
        self,
        vector: np.ndarray,
        top_n: int = 5,
        *,
        qualia_state: Any | None = None,
    ) -> List[Dict[str, Any]]:
        """Retorna os itens mais similares ao ``vector`` considerando decaimento."""
        if not self._data:
            return []
        now = time.time()
        self._clean_memory(now=now)
        centroid_q, q_enc = self._encode_hologram(vector)
        q = q_enc.reshape(1, -1)
        q_norm = q / max(norm(q), 1e-12)
        forget_lambda = (
            float(getattr(qualia_state, "research_focus", 0.0))
            if qualia_state is not None
            else 0.0
        )
        results: List[Tuple[float, MemoryItem, float]] = []
        for item in self._data:
            s_vec = item.vector.reshape(1, -1)
            s_norm = s_vec / max(norm(s_vec), 1e-12)
            sim = float(np.dot(q_norm, s_norm.T))
            age = now - item.timestamp
            score = sim * self._decay_weight(age)
            if forget_lambda:
                score *= float(np.exp(-forget_lambda * age))
            if centroid_q != item.centroid:
                score *= float(np.exp(-abs(centroid_q - item.centroid)))
            results.append((score, item, sim))
        results.sort(key=lambda r: r[0], reverse=True)
        output: List[Dict[str, Any]] = []
        for score, item, raw_sim in results[:top_n]:
            output.append(
                {
                    "similarity": score,
                    "raw_similarity": raw_sim,
                    "metadata": item.metadata,
                    "age": now - item.timestamp,
                }
            )
        return output


__all__ = ["HolographicMemory", "MemoryItem"]
