# QUALIA Binance Trading System - Environment Variables Template
# Sistema quântico-computacional altamente avançado e auto-evolutivo
#
# INSTRUÇÕES:
# 1. Copie este arquivo para .env: cp .env.template .env
# 2. Preencha com suas credenciais reais da Binance
# 3. NUNCA commite o arquivo .env no git

# =============================================================================
# CREDENCIAIS BINANCE
# =============================================================================
# Obtenha suas chaves em: https://www.binance.com/en/my/settings/api-management
BINANCE_API_KEY=your_binance_api_key_here
BINANCE_SECRET_KEY=your_binance_secret_key_here

# =============================================================================
# CONFIGURAÇÕES DE AMBIENTE
# =============================================================================
# Ambiente de execução (production/sandbox)
TRADING_ENVIRONMENT=production

# Modo de trading (live/paper)
TRADING_MODE=paper

# =============================================================================
# CONFIGURAÇÕES OPCIONAIS
# =============================================================================
# Logging level (DEBUG/INFO/WARNING/ERROR)
LOG_LEVEL=INFO

# Diretório de logs personalizado (opcional)
# LOG_DIR=custom_logs_directory

# Configurações de notificação (opcional)
# TELEGRAM_BOT_TOKEN=your_telegram_bot_token
# TELEGRAM_CHAT_ID=your_telegram_chat_id

# =============================================================================
# CONFIGURAÇÕES DE DESENVOLVIMENTO
# =============================================================================
# Ativar modo debug (true/false)
DEBUG_MODE=false

# Ativar logs verbosos (true/false)
VERBOSE_LOGGING=false
