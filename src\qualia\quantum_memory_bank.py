import os
import tempfile
import json
import logging
from dataclasses import dataclass, field
from typing import Dict, List, Any

logger = logging.getLogger(__name__)


def _load_json(file_path: str, default: Any = None) -> Any:
    """Carrega ``file_path`` como JSON, retornando ``default`` em caso de erro."""
    try:
        with open(file_path, "r", encoding="utf-8") as fh:
            return json.load(fh)
    except Exception as e:
        logger.warning("Erro ao carregar %s: %s", file_path, e)
        return default


def _save_json(data: Any, file_path: str) -> None:
    """Salva ``data`` em ``file_path`` como JSON."""
    try:
        with open(file_path, "w", encoding="utf-8") as fh:
            json.dump(data, fh, indent=2, ensure_ascii=False)
    except Exception as e:
        logger.warning("Erro ao salvar %s: %s", file_path, e)

@dataclass
class QuantumMemoryBank:
    """Armazena e agrega métricas de avaliações anteriores."""

    file_path: str | None = None
    records: List[Dict[str, float]] = field(default_factory=list)

    def __post_init__(self) -> None:
        if self.file_path is None:
            self.file_path = os.path.join(tempfile.gettempdir(), "qsi_memory.json")
        self._load()

    def _load(self) -> None:
        data = _load_json(self.file_path, default=[])
        if isinstance(data, list):
            self.records = [dict(item) for item in data]

    def _save(self) -> None:
        _save_json(self.records, self.file_path)

    def add_metrics(self, metrics: Dict[str, float]) -> None:
        self.records.append(metrics)
        self._save()

    def aggregate_metrics(self) -> Dict[str, float]:
        if not self.records:
            return {}
        # Considera todos os tipos de métricas existentes no histórico
        keys = set().union(*(rec.keys() for rec in self.records))
        sums = {k: 0.0 for k in keys}
        for rec in self.records:
            for k in keys:
                sums[k] += rec.get(k, 0.0)
        return {k: sums[k] / len(self.records) for k in keys}
