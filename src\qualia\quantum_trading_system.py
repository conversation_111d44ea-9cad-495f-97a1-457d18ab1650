from __future__ import annotations

"""Simplified QuantumTradingSystem used in examples.

This module implements a small toy version of the M-ICCI cycle using the
core operators defined in :mod:`src.qualia.core`. It is not intended for
production trading but serves as a reference implementation of how the
operators interact.
"""

from dataclasses import dataclass, field
import numpy as np
from numpy.typing import NDArray

from .core import (
    apply_folding,
    apply_resonance,
    apply_emergence,
    apply_retrocausality,
    observe_system,
    inject_noise,
)


@dataclass
class QuantumTradingSystem:
    """Toy implementation of the QUALIA trading system.

    Parameters
    ----------
    dimension:
        Size of the internal square field.
    alpha:
        Damping factor for the folding operator.
    strength:
        Strength of the morphic resonance operator.
    beta:
        Amplification factor for emergence.
    gamma:
        Mixing weight for the retrocausality operator.
    noise_sigma:
        Standard deviation of the injected noise.
    noise_mode:
        Type of noise to inject, ``"white"`` or ``"periodic"``.
    """

    dimension: int = 2
    alpha: float = 0.8
    strength: float = 0.12
    beta: float = 0.2
    gamma: float = 0.3
    noise_sigma: float = 0.0
    noise_mode: str = "white"
    _step: int = field(init=False, repr=False, default=0)
    state: NDArray[np.float64] = field(init=False)
    observer_matrix: NDArray[np.float64] = field(init=False)

    def __post_init__(self) -> None:
        self.state = np.zeros((self.dimension, self.dimension))
        self.observer_matrix = np.eye(self.dimension)
        self._step = 0

    def forecast_future_state(self) -> NDArray[np.float64]:
        """Return a naive forecast of the next state."""
        return np.copy(self.state)

    def evolve_system(self) -> NDArray[np.float64]:
        """Evolve ``state`` through the five operators in sequence."""
        noisy_state = inject_noise(
            self.state,
            sigma=self.noise_sigma,
            mode=self.noise_mode,
            step=self._step,
        )
        field = apply_folding(noisy_state, alpha=self.alpha)
        field = apply_resonance(field, strength=self.strength)
        field = apply_emergence(field, beta=self.beta)
        future = self.forecast_future_state()
        field = apply_retrocausality(field, future, gamma=self.gamma)
        field = observe_system(field, self.observer_matrix)
        self.state = field
        self._step += 1
        return field
