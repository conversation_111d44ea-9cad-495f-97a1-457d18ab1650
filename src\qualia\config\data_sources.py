from __future__ import annotations

from typing import Any, Dict

from ..utils.logger import get_logger
from .yaml_loader import load_yaml_config
from .settings import get_config_file_path

_logger = get_logger(__name__)
_DEFAULT_PATH = get_config_file_path("data_sources.yaml")


def load_data_sources_defaults() -> Dict[str, Any]:
    """Carrega configurações de fontes de dados."""
    return load_yaml_config("QUALIA_DATA_SOURCES", _DEFAULT_PATH, logger=_logger)


__all__ = ["load_data_sources_defaults"]
