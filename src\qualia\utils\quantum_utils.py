"""
Quantum utility functions for the QUALIA framework

Este módulo contém funções utilitárias para cálculos quânticos, incluindo
cálculo de entropia, manipulação de dados quânticos e processamento de
resultados de simulação. Os limites de complexidade de circuitos podem ser
substituídos via variáveis de ambiente ``QUALIA_MAX_CIRCUIT_DEPTH`` e
``QUALIA_MAX_CIRCUIT_OPERATIONS``.
"""

import numpy as np
import math
from typing import Dict, Union, List, Optional, Any, Sequence
from qiskit.quantum_info import Statevector, DensityMatrix
from qiskit import QuantumCircuit
from qiskit.circuit import Qubit
from qiskit.circuit.library import StatePreparation, HGate, SGate
from qiskit.circuit import CircuitInstruction
from ..core.encoders import MAX_CIRCUIT_DEPTH, MAX_CIRCUIT_OPERATIONS
from ..config.settings import get_env
from .logger import get_logger

logger = get_logger(__name__)

_DEFAULT_MAX_DEPTH = MAX_CIRCUIT_DEPTH
_DEFAULT_MAX_OPS = MAX_CIRCUIT_OPERATIONS
_DEFAULT_MIN_CX_RATIO = 5.0  # Alta entanglement para maximizar diversidade
_DEFAULT_MAX_LATENCY_MS = 0.005
_LOW_ENTROPY_THRESHOLD = 0.2
_LOW_DIVERSITY_THRESHOLD = 0.3
_MIN_TRIM_DEPTH = 26  # Circuitos não podem ser aparados abaixo desta profundidade


def _load_max_latency_ms(default: float) -> float:
    """Return latency limit in milliseconds from environment or default."""

    try:
        return float(get_env("QUALIA_MAX_CIRCUIT_LATENCY_MS", str(default), warn=False))
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Invalid value for QUALIA_MAX_CIRCUIT_LATENCY_MS: %s", exc)
        return default


def _auto_min_cx_ratio(
    n_qubits: int, backend_name: Optional[str], base: float
) -> float:
    """Return heuristic CX ratio based on qubits and backend."""

    ratio = base
    if backend_name and "simulator" in backend_name.lower():
        ratio *= 0.75  # simulators tolerate more aggressive trimming
    if n_qubits <= 4:
        ratio *= 0.6
    elif n_qubits >= 15:
        ratio *= 1.1
    # Permitir razões maiores que 1.0 para cenários de alto entrelaçamento
    return max(0.1, ratio)


def _load_min_cx_ratio(
    default: float, n_qubits: int, backend_name: Optional[str] = None
) -> float:
    """Return CX ratio limit from environment or heuristics."""

    env_value = get_env("QUALIA_MIN_CX_RATIO", None, warn=False)
    if env_value is not None:
        try:
            return float(env_value)
        except Exception as exc:  # pragma: no cover - defensive
            logger.error("Invalid value for QUALIA_MIN_CX_RATIO: %s", exc)
            return default
    return _auto_min_cx_ratio(n_qubits, backend_name, default)


def _load_circuit_limit(name: str, default: int) -> int:
    """Return circuit complexity limit overridden via environment variables."""

    try:
        return int(get_env(f"QUALIA_{name}", str(default), warn=False))
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Invalid value for QUALIA_%s: %s", name, exc)
        return default


def calculate_entropy(counts: Dict[str, int]) -> float:
    """
    Calculate Shannon entropy from measurement counts

    Args:
        counts: Dictionary of measurement counts

    Returns:
        Shannon entropy value
    """
    try:
        total = sum(counts.values())
        if total == 0:
            raise ValueError("counts sum to zero")

        probabilities = [count / total for count in counts.values()]

        entropy = 0.0
        for prob in probabilities:
            if prob > 0:
                entropy -= prob * math.log2(prob)

        return entropy
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Failed to calculate entropy: %s", exc)
        return float("nan")


def clean_metrics_data(metrics_data: List[Optional[float]]) -> List[float]:
    """
    Clean metrics data by removing None values and converting to floats

    Args:
        metrics_data: List of metric values that may contain None

    Returns:
        List of float values with None values removed
    """
    return [float(x) for x in metrics_data if x is not None]


def parse_complex_string(s: str) -> complex:
    """
    Parse a complex number from string representation

    Args:
        s: String representation of complex number

    Returns:
        Complex number
    """
    try:
        return complex(s.replace("i", "j"))
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Invalid complex string '%s': %s", s, exc)
        return 0j


def bitstring_to_int(bitstring: str) -> int:
    """
    Convert a binary string to integer

    Args:
        bitstring: Binary string representation (e.g., '101')

    Returns:
        Integer value
    """
    return int(bitstring, 2)


def int_to_bitstring(value: int, n_bits: int) -> str:
    """
    Convert integer to fixed-width binary string

    Args:
        value: Integer value to convert
        n_bits: Width of the binary string

    Returns:
        Binary string representation with fixed width
    """
    return format(value, f"0{n_bits}b")


def hamming_distance(a: Union[str, int], b: Union[str, int]) -> int:
    """
    Calculate Hamming distance between two binary strings or integers

    Args:
        a: First binary string or integer
        b: Second binary string or integer

    Returns:
        Hamming distance (number of differing bits)
    """
    # Convert to binary strings if integers
    if isinstance(a, int) and isinstance(b, int):
        # Find number of bits needed
        n_bits = max(a.bit_length(), b.bit_length())
        a_str = format(a, f"0{n_bits}b")
        b_str = format(b, f"0{n_bits}b")
    else:
        a_str = str(a)
        b_str = str(b)

    # Ensure equal length
    max_len = max(len(a_str), len(b_str))
    a_str = a_str.zfill(max_len)
    b_str = b_str.zfill(max_len)

    # Count differing bits
    return sum(1 for bit_a, bit_b in zip(a_str, b_str) if bit_a != bit_b)


def sv_entropy(state: Union[np.ndarray, List[complex]]) -> float:
    """Return the Shannon entropy of a statevector.

    Parameters
    ----------
    state:
        Statevector amplitudes as a ``numpy.ndarray`` or list.

    Returns
    -------
    float
        Shannon entropy ``-\sum p_i log2(p_i)`` computed from ``|state|^2``.
    """

    try:
        vec = np.asarray(state, dtype=complex).ravel()
        probabilities = np.abs(vec) ** 2
        probabilities = probabilities[probabilities > 0]
        if probabilities.size == 0:
            return 0.0
        entropy_val = -float(np.sum(probabilities * np.log2(probabilities)))
        return max(entropy_val, 0.0)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("sv_entropy failed: %s", exc)
        return 0.0


def linear_entropy(
    state: Union[np.ndarray, List[List[complex]], List[complex]],
) -> float:
    """Return the linear entropy of a state.

    The metric is ``1 - Tr(\rho^2)``. ``state`` may be a statevector or
    a density matrix.

    Parameters
    ----------
    state:
        Statevector or density matrix representation.

    Returns
    -------
    float
        Linear entropy value in ``[0, 1]`` for valid density matrices.
    """

    try:
        arr = np.asarray(state, dtype=complex)
        if arr.ndim == 1:
            probabilities = np.abs(arr) ** 2
            purity = float(np.sum(probabilities ** 2))
        else:
            purity = float(np.real(np.trace(arr.conj().T @ arr)))
        lin_entropy = 1.0 - purity
        return max(lin_entropy, 0.0)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("linear_entropy failed: %s", exc)
        return 0.0


def measure_entropy(statevector: Statevector) -> Dict[str, float]:
    """Return Shannon and linear entropy for a given statevector."""

    return {
        "sv_entropy": sv_entropy(statevector),
        "linear_entropy": linear_entropy(statevector),
    }


def fidelity(state1: np.ndarray, state2: np.ndarray) -> float:
    """
    Calculate fidelity between two quantum states

    Args:
        state1: First quantum state vector
        state2: Second quantum state vector

    Returns:
        Fidelity value (0 to 1)
    """
    try:
        overlap = np.abs(np.vdot(state1, state2))
        return float(overlap ** 2)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Failed to compute fidelity: %s", exc)
        return 0.0


def prepare_statevector(
    circuit: QuantumCircuit, state: np.ndarray, qubits: Sequence[Qubit] | None = None
) -> QuantumCircuit:
    """
    Prepare a quantum state using StatePreparation instead of non-unitary initialize

    Args:
        circuit: Quantum circuit to modify
        state: State vector to prepare
        qubits: Sequence of qubits where the state should be prepared. If
            ``None``, ``circuit.qubits`` is used.

    Returns:
        Circuit with state preparation added
    """
    # Normalize state vector
    state = state / np.linalg.norm(state)

    # Create StatePreparation gate
    state_prep = StatePreparation(state)

    # Add state preparation to circuit
    circuit.append(state_prep, qubits or circuit.qubits)

    return circuit


def _post_trim_randomize(
    circ: QuantumCircuit, rng: np.random.Generator
) -> QuantumCircuit:
    """Apply small random rotations after trimming.

    Each qubit receives an ``rx(pi/4)`` followed by a random ``rz`` rotation in
    ``[0, 2π)`` drawn from ``rng``. The circuit is modified in place and also
    returned for convenience.

    Parameters
    ----------
    circ
        Circuit already trimmed.
    rng
        ``numpy.random.Generator`` instance used for the random angles.

    Returns
    -------
    QuantumCircuit
        The randomized circuit.
    """

    try:
        for q in range(circ.num_qubits):
            circ.rx(np.pi / 4, q)
            circ.rz(float(rng.uniform(0.0, 2 * np.pi)), q)
    except Exception as exc:  # pragma: no cover - defensive
        logger.error("Failed post-trim randomization: %s", exc)
    return circ


def _pad_circuit_depth(circuit: QuantumCircuit, target_depth: int) -> QuantumCircuit:
    """Return a *copy* of *circuit* padded with barriers until ``depth`` >= *target_depth*.

    The padding is intentionally minimal (barriers only) so it does **not** change
    the unitary evolution of the circuit – it merely extends its *logical* depth
    as reported by ``QuantumCircuit.depth``. This avoids a hard failure when the
    circuit is too shallow while still complying with hardware/back-end or
    strategy requirements that expect a minimum depth.
    """
    padded = circuit.copy()
    from qiskit.circuit.library import IGate

    while padded.depth() < target_depth:
        for q in padded.qubits:
            padded.append(IGate(), [q])
    return padded


def _calculate_min_cx(
    n_qubits: int,
    min_cx_ratio: float,
    entropy: float | None,
    diversity_ratio: float | None,
) -> int:
    """Return minimum CX gate count required for entanglement preservation.

    Parameters
    ----------
    n_qubits
        Number of qubits in the circuit.
    min_cx_ratio
        Target CX ratio per qubit.
    entropy
        Optional entropy estimate.
    diversity_ratio
        Optional diversity ratio from measurement counts.

    Returns
    -------
    int
        Minimum number of CX gates.
    """

    min_cx = max(1, int(math.ceil(min_cx_ratio * n_qubits)))

    if (
        entropy is not None
        and entropy < _LOW_ENTROPY_THRESHOLD
        and diversity_ratio is not None
        and diversity_ratio < _LOW_DIVERSITY_THRESHOLD
    ):
        dynamic_min = n_qubits
        if dynamic_min > min_cx:
            logger.info(
                "Low entropy %.3f and diversity %.3f -> min_cx adjusted to %d",
                entropy,
                diversity_ratio,
                dynamic_min,
            )
        min_cx = max(min_cx, dynamic_min)

    return min_cx


def _remove_operations_until_within_limits(
    circuit: QuantumCircuit,
    max_depth: int,
    max_ops: int,
    min_cx: int,
    preserve_single_qubit_gates: bool,
) -> QuantumCircuit:
    """Remove operations until circuit complies with depth and op count.

    Parameters
    ----------
    circuit
        Circuit to trim in place.
    max_depth
        Maximum allowed depth.
    max_ops
        Maximum allowed operations.
    min_cx
        Minimum CX gate count to maintain.
    preserve_single_qubit_gates
        Whether single-qubit gates should not be removed.

    Returns
    -------
    QuantumCircuit
        The trimmed circuit.
    """

    trimmed = circuit
    removable_ops: list[tuple[int, int, tuple]] = []
    cx_count = trimmed.count_ops().get("cx", 0)

    for idx, inst in enumerate(trimmed.data):
        if hasattr(inst, "operation"):
            op, qargs, cargs = inst.operation, inst.qargs, inst.cargs
        else:
            op, qargs, cargs = inst
        if op.name in {"barrier", "measure"}:
            continue

        if op.name == "cx":
            cx_count -= 1
            if cx_count >= min_cx:
                removable_ops.append((1, idx, (op, qargs, cargs)))
            cx_count += 1
        else:
            if preserve_single_qubit_gates:
                continue
            test_circuit = trimmed.copy()
            test_circuit.data.pop(idx)
            if test_circuit.count_ops().get("cx", 0) >= min_cx:
                removable_ops.append((0, idx, (op, qargs, cargs)))

    removable_ops.sort(key=lambda x: (x[0], x[1]))

    while (
        trimmed.depth() > max_depth or sum(trimmed.count_ops().values()) > max_ops
    ) and removable_ops:
        _, _, (op, qargs, cargs) = removable_ops.pop(0)
        target = None
        for inst in trimmed.data:
            if hasattr(inst, "operation"):
                if inst.operation == op and inst.qargs == qargs and inst.cargs == cargs:
                    target = inst
                    break
            else:
                if inst == (op, qargs, cargs):
                    target = inst
                    break
        if target is not None:
            trimmed.data.remove(target)

        try:
            has_measure = any(op.name == "measure" for op, _, _ in trimmed.data)
            has_kraus = any(op.name == "kraus" for op, _, _ in trimmed.data)

            circuit_for_state = trimmed
            if has_measure or has_kraus:
                circuit_for_state = trimmed.copy()
                circuit_for_state.data = [
                    (op, qargs, cargs)
                    for op, qargs, cargs in circuit_for_state.data
                    if op.name not in {"measure", "kraus"}
                ]

            if has_kraus:
                dm = DensityMatrix.from_instruction(circuit_for_state)
                state_data = dm.data
            else:
                sv = Statevector.from_instruction(circuit_for_state)
                state_data = sv.data

            non_zero = np.count_nonzero(np.abs(np.asarray(state_data)) > 1e-9)
            div_ratio = non_zero / float(2 ** trimmed.num_qubits)
        except Exception as exc:  # pragma: no cover - defensive
            logger.warning("Failed diversity check during trim: %s", exc)
            div_ratio = 1.0

        if div_ratio < 0.40:
            logger.info(
                "Stopping trim due to diversity ratio %.2f below threshold",
                div_ratio,
            )
            break

        if trimmed.depth() > max_depth or sum(trimmed.count_ops().values()) > max_ops:
            removable_ops = []
            cx_count = trimmed.count_ops().get("cx", 0)
            for idx, (op, qargs, cargs) in enumerate(trimmed.data):
                if op.name in {"barrier", "measure"}:
                    continue

                if op.name == "cx":
                    cx_count -= 1
                    if cx_count >= min_cx:
                        removable_ops.append((1, idx, (op, qargs, cargs)))
                    cx_count += 1
                else:
                    test_circuit = trimmed.copy()
                    test_circuit.data.pop(idx)
                    if test_circuit.count_ops().get("cx", 0) >= min_cx:
                        removable_ops.append((0, idx, (op, qargs, cargs)))

            removable_ops.sort(key=lambda x: (x[0], x[1]))

    return trimmed


def _post_process_trim(
    circuit: QuantumCircuit,
    post_randomize_layers: int,
    diversity_ratio: float,
) -> QuantumCircuit:
    """Apply final adjustments after trimming.

    Parameters
    ----------
    circuit
        Circuit already trimmed.
    post_randomize_layers
        Number of randomization layers to apply.
    diversity_ratio
        Diversity ratio used to trigger extra trimming.

    Returns
    -------
    QuantumCircuit
        The processed circuit.
    """

    trimmed = circuit

    if hasattr(trimmed, "_remove_redundant_barriers"):
        trimmed._remove_redundant_barriers()
    else:
        i = 1
        while i < len(trimmed.data):
            prev = trimmed.data[i - 1]
            curr = trimmed.data[i]
            prev_name = prev.operation.name if hasattr(prev, "operation") else prev[0].name
            curr_name = curr.operation.name if hasattr(curr, "operation") else curr[0].name
            if prev_name == "barrier" and curr_name == "barrier":
                trimmed.data.pop(i)
            else:
                i += 1

    rng = np.random.default_rng()
    for _ in range(max(0, post_randomize_layers)):
        _post_trim_randomize(trimmed, rng)

    meas_indices = []
    for idx, inst in enumerate(trimmed.data):
        if hasattr(inst, "operation"):
            op = inst.operation
        else:
            op = inst[0]
        if op.name == "measure":
            meas_indices.append(idx)
    from qiskit.circuit import CircuitInstruction

    for idx in reversed(meas_indices):
        inst = trimmed.data[idx]
        qubit = inst.qargs[0] if hasattr(inst, "qargs") else inst[1][0]
        trimmed.data.insert(idx, CircuitInstruction(HGate(), [qubit], []))
        trimmed.data.insert(idx + 1, CircuitInstruction(SGate(), [qubit], []))

    if trimmed.depth() > 80 and diversity_ratio > 0.60:
        trimmed_circuit = trim_preserving_cx(trimmed, depth_cap=80, cx_cap=20)
        return trimmed_circuit

    return trimmed


def enforce_circuit_limits(
    circuit: QuantumCircuit,
    max_depth: int | None = None,
    max_ops: int | None = None,
    min_cx_ratio: float | None = None,
    min_depth: int = _MIN_TRIM_DEPTH,
    backend_name: Optional[str] = None,
    backend: Optional[Any] = None,
    max_latency_ms: Optional[float] = None,
    entropy: Optional[float] = None,
    counts_diversity_ratio: Optional[float] = None,
    gate_budget_per_qubit: int | None = None,
    post_randomize_layers: int = 1,
    auto_pad_depth: bool = True,
) -> QuantumCircuit:
    """
    Return a circuit trimmed to comply with complexity limits while preserving entanglement.

    Args:
        circuit: Circuit to trim
        max_depth: Maximum circuit depth
        max_ops: Maximum number of operations
        min_cx_ratio: Minimum CX gates per qubit ratio. If ``None`` the value is
            loaded from ``QUALIA_MIN_CX_RATIO`` or heuristically determined.
        min_depth: Minimum circuit depth
        backend_name: Backend name used for heuristic ratio determination
        - Ratio ≤ 1.0 to ensure realistic entanglement targets
        - Higher values may prevent trimming
        - Lower values allow more aggressive trimming
        backend: Backend instance used to derive gate lengths for latency-based
            depth limits
        max_latency_ms: Maximum allowed circuit latency in milliseconds used in
            conjunction with ``backend`` to compute a dynamic ``max_depth``
        entropy: Current measured entropy used to adjust trimming aggressiveness
        counts_diversity_ratio: Diversity of measurement counts used together
            with ``entropy`` to detect potential over-trimming
        gate_budget_per_qubit: Optional limit of operations per qubit. When set,
            operations exceeding this budget are pruned while trying to preserve
            entanglement.
        post_randomize_layers: Number of times ``_post_trim_randomize`` is
            applied after trimming and before measurement rotation.

    Returns:
        Trimmed circuit that maintains minimum entanglement requirements and não viola ``min_depth``

    Raises:
        ValueError: If cannot maintain minimum entanglement requirements
    """
    # Skip trimming when called without explicit limits (universe pipeline)
    if (
        max_depth is None
        and max_ops is None
        and min_cx_ratio is None
        and backend_name is None
        and backend is None
        and max_latency_ms is None
        and entropy is None
        and counts_diversity_ratio is None
        and gate_budget_per_qubit is None
        and post_randomize_layers == 1
        and min_depth == _MIN_TRIM_DEPTH
    ):
        logger.info("Skipping circuit trimming to preserve full circuit structure.")
        return circuit

    if max_depth is None:
        max_depth = _load_circuit_limit("MAX_CIRCUIT_DEPTH", _DEFAULT_MAX_DEPTH)
    if max_ops is None:
        max_ops = _load_circuit_limit("MAX_CIRCUIT_OPERATIONS", _DEFAULT_MAX_OPS)
    if min_cx_ratio is None:
        min_cx_ratio = _load_min_cx_ratio(
            _DEFAULT_MIN_CX_RATIO, len(circuit.qubits), backend_name
        )

    if backend is not None:
        if max_latency_ms is None:
            max_latency_ms = _load_max_latency_ms(_DEFAULT_MAX_LATENCY_MS)
        props = getattr(backend, "properties", lambda: None)()
        gate_lengths: list[float] = []
        if props is not None:
            try:
                gates = getattr(props, "gates", [])
                for gate in gates:
                    for param in getattr(gate, "parameters", []):
                        if getattr(param, "name", "") == "gate_length" and hasattr(
                            param, "value"
                        ):
                            gate_lengths.append(float(param.value))
            except Exception as exc:  # pragma: no cover - defensive
                logger.warning("Failed to load gate lengths: %s", exc)

        if gate_lengths:
            max_gate_len = max(gate_lengths)  # seconds
            dynamic_depth = max(1, int((max_latency_ms / 1000.0) / max_gate_len))
            if max_depth is None:
                max_depth = dynamic_depth
            else:
                max_depth = min(max_depth, dynamic_depth)

    # Se o circuito já está dentro dos limites (mas pode estar abaixo de min_depth),
    # não precisamos aparar – apenas validar profundidade mínima.
    if (
        circuit.depth() <= max_depth
        and sum(circuit.count_ops().values()) <= max_ops
        and gate_budget_per_qubit is None
    ):
        if circuit.depth() < min_depth:
            if auto_pad_depth:
                circuit = _pad_circuit_depth(circuit, min_depth)
            else:
                raise ValueError(
                    f"Circuit depth {circuit.depth()} is below required min_depth {min_depth}"
                )
        trimmed = circuit
        if hasattr(trimmed, "_remove_redundant_barriers"):
            trimmed._remove_redundant_barriers()
        else:
            i = 1
            while i < len(trimmed.data):
                prev_inst = trimmed.data[i - 1]
                curr_inst = trimmed.data[i]
                prev_name = prev_inst.operation.name
                curr_name = curr_inst.operation.name
                if prev_name == "barrier" and curr_name == "barrier":
                    trimmed.data.pop(i)
                else:
                    i += 1
        # Validação de profundidade mínima
        if trimmed.depth() < min_depth:
            if auto_pad_depth:
                trimmed = _pad_circuit_depth(trimmed, min_depth)
            else:
                raise ValueError(
                    f"Trimmed circuit depth {trimmed.depth()} is below required min_depth {min_depth}"
                )
        return trimmed

    # Calculate minimum required CX gates (rounded up)
    min_cx = _calculate_min_cx(len(circuit.qubits), min_cx_ratio, entropy, counts_diversity_ratio)
    preserve_single_qubit_gates = True
    if max_latency_ms is not None and (min_cx_ratio or 0.0) <= 0.0:
        preserve_single_qubit_gates = False
    trimmed = circuit.copy()
    trimmed = _remove_operations_until_within_limits(
        trimmed,
        max_depth,
        max_ops,
        min_cx,
        preserve_single_qubit_gates,
    )

    if gate_budget_per_qubit is not None:

        def _qubit_op_counts(qc: QuantumCircuit) -> dict[int, int]:
            counts = {i: 0 for i in range(qc.num_qubits)}
            for op, qargs, _ in qc.data:
                if op.name in {"barrier", "measure"}:
                    continue
                if op.name == "cx" and len(qargs) == 2:
                    counts[getattr(qargs[0], "index", getattr(qargs[0], "_index"))] += 1
                    counts[getattr(qargs[1], "index", getattr(qargs[1], "_index"))] += 1
                elif len(qargs) == 1:
                    counts[getattr(qargs[0], "index", getattr(qargs[0], "_index"))] += 1
            return counts

        counts = _qubit_op_counts(trimmed)
        overs = [q for q, c in counts.items() if c > gate_budget_per_qubit]
        while overs:
            removed = False
            for idx in range(len(trimmed.data) - 1, -1, -1):
                op, qargs, cargs = trimmed.data[idx]
                if op.name in {"barrier", "measure"}:
                    continue
                if (
                    len(qargs) == 1
                    and counts[getattr(qargs[0], "index", getattr(qargs[0], "_index"))]
                    > gate_budget_per_qubit
                ):
                    trimmed.data.pop(idx)
                    counts[getattr(qargs[0], "index", getattr(qargs[0], "_index"))] -= 1
                    removed = True
                    break
            if not removed:
                for idx in range(len(trimmed.data) - 1, -1, -1):
                    op, qargs, cargs = trimmed.data[idx]
                    if op.name == "cx" and len(qargs) == 2:
                        idx0 = getattr(qargs[0], "index", getattr(qargs[0], "_index"))
                        idx1 = getattr(qargs[1], "index", getattr(qargs[1], "_index"))
                        if (
                            counts[idx0] > gate_budget_per_qubit
                            or counts[idx1] > gate_budget_per_qubit
                        ) and trimmed.count_ops().get("cx", 0) - 1 >= min_cx:
                            trimmed.data.pop(idx)
                            counts[idx0] -= 1
                            counts[idx1] -= 1
                            removed = True
                            break
            if not removed:
                break
            overs = [q for q, c in counts.items() if c > gate_budget_per_qubit]

    # Final validation
    final_cx = trimmed.count_ops().get("cx", 0)
    # Validação de profundidade mínima ao final do trimming
    if trimmed.depth() < min_depth:
        raise ValueError(
            f"Trimmed circuit depth {trimmed.depth()} is below required min_depth {min_depth}"
        )
    if final_cx < min_cx:
        raise ValueError(
            f"Failed to maintain minimum CX requirement: {final_cx} < {min_cx}"
            f"\nCircuit depth: {trimmed.depth()}"
            f"\nOperations: {sum(trimmed.count_ops().values())}"
        )

    # Log metrics, incluindo cx_ratio para acompanhar equilíbrio entre CX e total de portas
    total_ops_final = sum(trimmed.count_ops().values())
    cx_ratio = final_cx / max(1, total_ops_final)
    logger.info(
        "Circuit trim metrics: initial depth/ops %s/%s; final depth/ops %s/%s; "
        "CX gates %s (min required: %s) | cx_ratio=%.3f",
        circuit.depth(),
        sum(circuit.count_ops().values()),
        trimmed.depth(),
        total_ops_final,
        final_cx,
        min_cx,
        cx_ratio,
    )

    trimmed = _post_process_trim(trimmed, post_randomize_layers, counts_diversity_ratio)
    return trimmed


def validate_circuit_limits(
    circuit: QuantumCircuit,
    max_depth: int = _load_circuit_limit("MAX_CIRCUIT_DEPTH", _DEFAULT_MAX_DEPTH),
    max_ops: int = _load_circuit_limit("MAX_CIRCUIT_OPERATIONS", _DEFAULT_MAX_OPS),
) -> None:
    """Raise ``ValueError`` if circuit exceeds complexity limits."""

    depth = circuit.depth()
    operations = circuit.size()
    if depth > max_depth or operations > max_ops:
        logger.error(
            "Circuit exceeds limits: depth=%d, ops=%d; max_depth=%d, max_ops=%d",
            depth,
            operations,
            max_depth,
            max_ops,
        )
        raise ValueError(
            f"Circuit exceeds limits: depth={depth}, ops={operations}; "
            f"max_depth={max_depth}, max_ops={max_ops}"
        )


def trim_preserving_cx(
    circuit: QuantumCircuit, depth_cap: int, cx_cap: int
) -> QuantumCircuit:
    """
    Trim a circuit to a specified depth while preserving CX gates.

    Args:
        circuit: Circuit to trim
        depth_cap: Maximum allowed depth
        cx_cap: Maximum allowed CX gates

    Returns:
        Trimmed circuit
    """
    # Create copy to modify
    trimmed = circuit.copy()

    # Track operations we can safely remove
    removable_ops: list[tuple[int, int, tuple]] = []

    # Calculate initial CX count
    cx_count = trimmed.count_ops().get("cx", 0)

    # First pass: identify removable operations while preserving entanglement
    for idx, (op, qargs, cargs) in enumerate(trimmed.data):
        if op.name in ["barrier", "measure"]:
            continue

        # Check if removing this operation would violate entanglement requirement
        if op.name == "cx":
            cx_count -= 1
            if cx_count >= cx_cap:
                removable_ops.append((1, idx, (op, qargs, cargs)))
            cx_count += 1
        else:
            # Create minimal test circuit with just this operation removed
            test_circuit = trimmed.copy()
            test_circuit.data.pop(idx)
            if test_circuit.count_ops().get("cx", 0) >= cx_cap:
                removable_ops.append((0, idx, (op, qargs, cargs)))

    removable_ops.sort(key=lambda x: (x[0], x[1]))

    # Second pass: remove operations while respecting depth and ops limits
    while trimmed.depth() > depth_cap and removable_ops:
        # Remove operation with least impact on entanglement
        _, _, (op, qargs, cargs) = removable_ops.pop(0)
        trimmed.data.remove((op, qargs, cargs))

        # Update removable list if needed
        if trimmed.depth() > depth_cap:
            removable_ops = []
            cx_count = trimmed.count_ops().get("cx", 0)
            for idx, (op, qargs, cargs) in enumerate(trimmed.data):
                if op.name in ["barrier", "measure"]:
                    continue

                if op.name == "cx":
                    cx_count -= 1
                    if cx_count >= cx_cap:
                        removable_ops.append((1, idx, (op, qargs, cargs)))
                    cx_count += 1
                else:
                    test_circuit = trimmed.copy()
                    test_circuit.data.pop(idx)
                    if test_circuit.count_ops().get("cx", 0) >= cx_cap:
                        removable_ops.append((0, idx, (op, qargs, cargs)))

            removable_ops.sort(key=lambda x: (x[0], x[1]))

    return trimmed
