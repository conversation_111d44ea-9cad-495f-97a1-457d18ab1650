"""Saturation monitoring for the HolographicMarketUniverse."""

from __future__ import annotations

from dataclasses import dataclass
from typing import Optional

from ..utils.logger import get_logger
from ..memory.event_bus import SimpleEventBus
from ..events import HolographicSaturationAlert
from .metrics import get_collector

logger = get_logger(__name__)


@dataclass
class HolographicSaturationMonitor:
    """Monitor saturation metrics of a ``HolographicMarketUniverse``."""

    universe: any
    alert_threshold: float = 0.05
    event_bus: Optional[SimpleEventBus] = None

    def __post_init__(self) -> None:
        self.metrics = get_collector()

    def record_memory_state(self) -> None:
        """Record current memory size and decay rate."""
        memory = getattr(self.universe, "memory", None)
        if memory is None:
            return
        size = len(getattr(memory, "_data", []))
        decay = float(getattr(memory, "decay_rate", 0.0))
        self.metrics.record_metric("holographic.memory_size", float(size))
        self.metrics.record_metric("holographic.decay_rate", decay)

    def check_useful_events(self) -> None:
        """Verify ratio of useful events and emit alerts if needed."""
        total = getattr(self.universe, "_event_injection_count", 0)
        useful = len(getattr(self.universe, "patterns_history", []))
        if total <= 0:
            return
        ratio = useful / float(total)
        self.metrics.record_metric("holographic.useful_event_ratio", ratio)
        if ratio < self.alert_threshold:
            logger.warning(
                "Useful event ratio %.2f%% below threshold %.2f%%",
                ratio * 100.0,
                self.alert_threshold * 100.0,
            )
            if self.event_bus is not None:
                self.event_bus.publish(
                    "holographic.saturation.alert",
                    HolographicSaturationAlert(
                        memory_size=len(getattr(self.universe.memory, "_data", [])),
                        decay_rate=float(
                            getattr(self.universe.memory, "decay_rate", 0.0)
                        ),
                        useful_ratio=ratio,
                        threshold=self.alert_threshold,
                    ),
                )
