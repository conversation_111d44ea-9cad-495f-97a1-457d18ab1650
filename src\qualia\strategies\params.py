from dataclasses import dataclass, field
from typing import Any, Dict, List

from pydantic import BaseModel, Field, ConfigDict

__all__ = ["EnhancedScalpingParams", "QualiaTSVFParams"]


@dataclass
class EnhancedScalpingParams:
    """Parameter set for :class:`QUALIAEnhancedScalpingStrategy`."""

    risk_profile: str = "balanced"
    use_quantum_metrics: bool = True
    simple_pilot_mode: bool = False
    simple_rsi_period: int = 14
    simple_rsi_threshold_buy: int = 30
    simple_rsi_threshold_sell: int = 70
    simple_pilot_confidence: float = 0.7
    atr_sl_multiplier_k: float = 0.0

    profit_target_pct: float = 0.3
    stop_loss_pct: float = 0.25
    max_position_size: float = 0.15
    min_volatility: float = 0.4
    max_spread_pct: float = 0.1
    mean_reversion_threshold: float = 2.0
    momentum_threshold: float = 0.4
    min_volume_percentile: int = 40

    vwap_periods: Dict[str, int] = field(
        default_factory=lambda: {"1m": 60, "5m": 24, "15m": 16, "1h": 8}
    )
    ema_periods: List[int] = field(default_factory=lambda: [9, 21, 55, 100])
    rsi_period: int = 14
    atr_period: int = 14
    bb_period: int = 20
    bb_std: float = 2.0
    sma_short_period: int = 10
    sma_long_period: int = 20


class QualiaTSVFParams(BaseModel):
    """Parameter set for :class:`QualiaTSVFStrategy`."""

    tsvf_vector_size: int = Field(default=100, ge=1)
    tsvf_alpha: float = Field(default=0.3, ge=0.0, le=1.0)
    tsvf_gamma: float = Field(default=0.1, ge=0.0, le=1.0)
    cE: float = Field(default=0.1)
    cH: float = Field(default=0.05)
    coherence_threshold: float = Field(default=0.55, ge=-1.0, le=1.0)
    entropy_threshold: float = Field(default=0.4, ge=0.0, le=1.0)
    otoc_delta: int = Field(default=1)
    otoc_window: int = Field(default=168, ge=1)

    s1_tsvf_window: int = Field(default=24, ge=1)
    s1_strength_threshold: float = Field(default=0.02)

    s2_sma_short_period: int = Field(default=50, ge=1)
    s2_sma_long_period: int = Field(default=100, ge=1)
    s2_rsi_period: int = Field(default=14, ge=1)
    s2_rsi_oversold: int = Field(default=30, ge=0)
    s2_rsi_overbought: int = Field(default=70, ge=0)

    s3_resample_period: str = "4h"
    s3_tsvf_window: int = Field(default=6, ge=1)
    s3_strength_threshold: float = Field(default=0.015)

    meta_sharpe_window_hours: int = Field(default=168, ge=1)
    meta_transaction_cost: float = Field(default=0.0005, ge=0.0)
    meta_decision_threshold: float = Field(default=0.1)

    entropy_history_window: int = Field(default=20, ge=1)
    h4_window: int = Field(default=20, ge=1)
    strength_percentile: int = Field(default=80, ge=0, le=100)

    profit_target: float = Field(default=5.0)
    max_drawdown: float = Field(default=1.0)
    horizon: int = Field(default=24, ge=1)
    use_resonance: bool = False
    use_folding: bool = False

    model_config = ConfigDict(validate_assignment=True)
