#!/usr/bin/env python3
"""
Script para aplicar manualmente os thresholds calibrados
Atualiza diretamente o arquivo qualia_config.yaml
"""

import yaml
from pathlib import Path
from datetime import datetime

def apply_calibrated_thresholds():
    """Aplica os thresholds calibrados manualmente"""
    
    # Thresholds calibrados da última execução
    calibrated_thresholds = {
        'consciousness': 0.854,
        'coherence': 0.820,
        'confidence': 0.719,
        'volume_surge_min': 0.856,
        'momentum_min': 0.008
    }
    
    print("APLICACAO MANUAL DE THRESHOLDS CALIBRADOS")
    print("=" * 60)
    
    config_path = Path('config/qualia_config.yaml')
    
    if not config_path.exists():
        print(f"ERRO: Arquivo nao encontrado: {config_path}")
        return
    
    try:
        # Ler configuração atual
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Backup do arquivo original
        backup_path = config_path.with_suffix('.yaml.backup')
        with open(backup_path, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        print(f"Backup criado: {backup_path}")
        
        # Mostrar valores atuais vs calibrados
        print("\nCOMPARACAO DE VALORES:")
        print("-" * 40)
        
        if 'quantum_thresholds' not in config:
            config['quantum_thresholds'] = {}
        
        for metric, new_value in calibrated_thresholds.items():
            old_value = config['quantum_thresholds'].get(metric, 'N/A')
            config['quantum_thresholds'][metric] = round(float(new_value), 3)
            
            print(f"{metric}:")
            print(f"   Atual: {old_value}")
            print(f"   Calibrado: {new_value:.3f}")
            
            if isinstance(old_value, (int, float)):
                change = new_value - old_value
                change_pct = (change / old_value) * 100 if old_value != 0 else 0
                print(f"   Mudanca: {change:+.3f} ({change_pct:+.1f}%)")
            print()
        
        # Adicionar metadados
        config['quantum_thresholds']['_calibration_metadata'] = {
            'last_calibration': datetime.now().isoformat(),
            'calibration_method': 'historical_simulation_manual_apply',
            'calibrated_by': 'manual_script',
            'data_points': 2282,
            'success_rate': '24.4%',
            'profit_threshold': '1.5%',
            'time_horizon': '4h'
        }
        
        # Confirmar aplicação
        response = input("Aplicar estes thresholds? (s/N): ").lower()
        
        if response in ['s', 'sim', 'y', 'yes']:
            # Salvar configuração atualizada
            with open(config_path, 'w', encoding='utf-8') as f:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            print(f"\nSUCESSO: Configuracao atualizada salva em: {config_path}")
            print("THRESHOLDS CALIBRADOS APLICADOS COM SUCESSO!")
            
            # Mostrar resumo final
            print("\nTHRESHOLDS FINAIS:")
            print("-" * 30)
            for metric, value in calibrated_thresholds.items():
                print(f"   {metric}: {value:.3f}")
                
        else:
            print("\nOperacao cancelada. Nenhuma alteracao foi feita.")
            
    except Exception as e:
        print(f"ERRO: {e}")

if __name__ == "__main__":
    apply_calibrated_thresholds()
