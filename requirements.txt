# QUALIA Binance Trading System Dependencies
# Sistema quântico-computacional altamente avançado e auto-evolutivo

# Core trading and exchange
ccxt>=4.0.0
binance-connector>=3.0.0

# Data processing and analysis
numpy>=1.24.0
pandas>=2.0.0
scipy>=1.10.0

# Configuration and environment
pyyaml>=6.0
python-dotenv>=1.0.0

# Async and networking
aiohttp>=3.8.0
asyncio-throttle>=1.0.0

# Logging and monitoring
colorlog>=6.7.0

# Mathematical and statistical
scikit-learn>=1.3.0
statsmodels>=0.14.0

# Utilities
dataclasses-json>=0.5.0
typing-extensions>=4.5.0

# Development and testing (optional)
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0
