# src/qualia/personas/momentum_quant.py

from __future__ import annotations

from typing import Dict, Any
import numpy as np

from .base import BasePersona

class MomentumQuant(BasePersona):
    """
    Persona que representa um fundo quantitativo de momentum.

    - Reage a alta volatilidade e rompimentos de preço.
    - Ignora o sentimento social/notícias.
    - Tenta capturar tendências de curto prazo.
    """

    def _initialize_state(self) -> Dict[str, Any]:
        """
        Inicializa o estado com foco em volatilidade e thresholds de ação.
        """
        return {
            "confidence": 0.7,  # Começa com confiança relativamente alta em seu modelo
            "volatility_threshold": self.config.get("volatility_threshold", 0.02), # 2% de volatilidade para se tornar ativo
            "price_change_threshold": self.config.get("price_change_threshold", 0.03) # 3% de mudança de preço para agir
        }

    def update_state(self, market_data: Dict[str, Any], external_data: Dict[str, Any]) -> None:
        """
        Esta persona não se importa com dados externos (notícias, sentimento).
        Seu estado é relativamente estático, baseado em sua configuração inicial.
        Poderia ser estendido para ajustar seus thresholds com base na performance passada.
        """
        # No-op por enquanto, pois seus gatilhos são baseados no estado de mercado imediato.
        pass

    def get_probable_action(self, market_state: Dict[str, Any]) -> Dict[str, float]:
        """
        Gera um `DecisionVector` baseado na volatilidade e na mudança de preço.
        """
        volatility = market_state.get("volatility", 0.0)
        price_change = market_state.get("price_change_pct", 0.0)

        # Se a volatilidade for baixa, a persona fica inativa.
        if volatility < self.state["volatility_threshold"]:
            return {"BUY": 0.05, "SELL": 0.05, "HOLD": 0.9}

        # Se a volatilidade é alta, a persona age na direção da mudança de preço.
        if price_change > self.state["price_change_threshold"]:
            # Forte tendência de alta
            confidence = min((price_change / (self.state["price_change_threshold"] * 2)) * self.state["confidence"], 1.0)
            return {"BUY": confidence, "SELL": (1 - confidence) / 2, "HOLD": (1 - confidence) / 2}
        
        elif price_change < -self.state["price_change_threshold"]:
            # Forte tendência de baixa
            confidence = min((abs(price_change) / (self.state["price_change_threshold"] * 2)) * self.state["confidence"], 1.0)
            return {"BUY": (1 - confidence) / 2, "SELL": confidence, "HOLD": (1 - confidence) / 2}
        
        else:
            # Volatilidade alta mas sem direção clara (ex: dentro de um range)
            return {"BUY": 0.1, "SELL": 0.1, "HOLD": 0.8}
