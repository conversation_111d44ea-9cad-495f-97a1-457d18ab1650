"""Retrocausality Operator - Future-influencing-past analysis.

Implements quantum-inspired retrocausal effects for market prediction.

Notes
-----
The :mod:`qualia.core.emergence` step is executed before the retrocausal
prediction phase. Emergent patterns are therefore computed prior to
retrocausal inference in the current implementation.

"""

import numpy as np
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass
import logging
from collections import deque
from scipy.stats import entropy

from ..config.feature_flags import feature_toggle
from ..intentions import EnvelopeAdjustmentProvider, IntentionEnvelope
from ..memory.event_bus import SimpleEventBus
from ..retro.insight_collector import RETROCAUSAL_INSIGHT_EVENT

logger = logging.getLogger(__name__)


@dataclass
class RetrocausalEvent:
    """Represents a potential retrocausal event"""

    event_id: str
    future_time: float
    past_influence_time: float
    influence_strength: float
    causal_direction: str  # 'future_to_past' or 'past_to_future'
    event_type: str
    confidence: float


@dataclass
class RetrocausalityState:
    """State representation for retrocausality operations"""

    active_events: List[RetrocausalEvent]
    temporal_field_strength: float
    causal_loops: List[Tuple[str, str]]
    prediction_confidence: float
    timestamp: float
    otoc_metric: float = 0.0


@dataclass
class TSVFOutput:
    """Output of :func:`tsvf_channel` calculations."""

    state_vector: np.ndarray
    coherence: float
    entropy: float
    emitted: float
    signal: float
    otoc: float


class RetrocausalityOperator(EnvelopeAdjustmentProvider):
    """
    Quantum-inspired retrocausality operator for temporal analysis

    Analyzes potential future-to-past influences in market data,
    detecting patterns that suggest future events may influence
    current market conditions.
    """

    def __init__(
        self,
        config: Dict[str, Any],
        event_bus: SimpleEventBus | None = None,
        *,
        history_maxlen: int = 1000,
    ):
        self.temporal_window = config.get("temporal_window", 3600)  # 1 hour default
        self.influence_threshold = config.get("influence_threshold", 0.3)
        self.causality_strength = config.get(
            "causality_strength", 0.618
        )  # Golden ratio
        self.max_lookback = config.get("max_lookback", 86400)  # 24 hours

        # TSVF parameters
        self.tsvf_window = config.get("tsvf_window", 24)
        self.tsvf_alpha = config.get("tsvf_alpha", 0.3)
        self.tsvf_gamma = config.get("tsvf_gamma", 0.1)
        self.tsvf_cE = config.get("tsvf_cE", 0.1)
        self.tsvf_cH = config.get("tsvf_cH", 0.05)
        self.tsvf_size = config.get("tsvf_size", 100)
        # Factor to reduce entropy when OTOC reveals strong temporal correlation
        self.entropy_reduction = float(config.get("entropy_reduction", 1.0))

        # Blending parameters
        self.blend_decay = config.get("blend_decay", 0.0)
        self.future_shift = int(config.get("future_shift", 0))

        # Forecast parameters
        self.forecast_window = config.get("forecast_window", 5)

        self.event_bus = event_bus
        self.publish_events = feature_toggle("retrocausality_module")
        self.last_tsvf_output: TSVFOutput | None = None

        # Temporal tracking
        self.current_state: Optional[RetrocausalityState] = None
        self.retrocausality_history: List[RetrocausalityState] = []
        self.history_maxlen = int(history_maxlen)
        self.event_registry: Dict[str, RetrocausalEvent] = {}

        # Time series buffers for analysis
        self.past_buffer = deque(maxlen=1000)
        self.future_predictions = deque(maxlen=100)
        self.temporal_correlations: Dict[str, float] = {}

        logger.info(
            f"RetrocausalityOperator initialized with {self.temporal_window}s window"
        )

    def forecast_future_state(
        self, field_history: Optional[np.ndarray] | None = None
    ) -> np.ndarray:
        """Estimate future state using a moving average forecast."""

        try:
            if field_history is None:
                if not self.past_buffer:
                    return np.array([0.0])
                field_history = np.array([np.mean(d) for d, _ in self.past_buffer])

            if field_history.size == 0:
                return np.array([0.0])

            window = min(self.forecast_window, field_history.size)
            forecast = float(np.mean(field_history[-window:]))
            return np.array([forecast])
        except Exception as exc:  # pragma: no cover - safeguard forecast errors
            logger.warning("Future state forecast failed: %s", exc)
            last_val = (
                float(field_history[-1])
                if field_history is not None and field_history.size > 0
                else 0.0
            )
            return np.array([last_val])

    async def analyze_retrocausality(
        self, current_data: np.ndarray, predicted_future: np.ndarray, timestamp: float
    ) -> RetrocausalityState:
        """Analyze retrocausal influences in market data.

        Parameters
        ----------
        current_data : np.ndarray
            Current market state.
        predicted_future : np.ndarray
            Forecast of future market conditions.
        timestamp : float
            Timestamp of the observation.

        Returns
        -------
        RetrocausalityState
            Representation of detected retrocausal effects at ``timestamp``.
        """
        try:
            # Store current data
            self.past_buffer.append((current_data.copy(), timestamp))
            self.future_predictions.append((predicted_future.copy(), timestamp))

            # Need sufficient history for analysis
            if len(self.past_buffer) < 10:
                return self._create_initial_state(timestamp)

            # Detect retrocausal events
            active_events = await self._detect_retrocausal_events(
                current_data, predicted_future, timestamp
            )

            # Calculate temporal field strength
            field_strength = self._calculate_temporal_field_strength()

            # Detect causal loops
            causal_loops = self._detect_causal_loops(active_events)

            # Calculate prediction confidence based on retrocausal consistency
            prediction_confidence = self._calculate_prediction_confidence(
                current_data, predicted_future
            )

            # Update event registry
            self._update_event_registry(active_events, timestamp)

            # Create retrocausality state
            state = RetrocausalityState(
                active_events=active_events,
                temporal_field_strength=field_strength,
                causal_loops=causal_loops,
                prediction_confidence=prediction_confidence,
                timestamp=timestamp,
            )

            self.current_state = state
            self.retrocausality_history.append(state)

            # Limit history size
            if len(self.retrocausality_history) > self.history_maxlen:
                self.retrocausality_history.pop(0)

            past_prices = np.array([np.mean(d) for d, _ in self.past_buffer])
            future_prices = np.array([np.mean(predicted_future)])

            self.last_tsvf_output = self.tsvf_channel(past_prices, future_prices)
            state.otoc_metric = self.last_tsvf_output.otoc

            if self.event_bus and self.publish_events:
                self.event_bus.publish(
                    "retrocausality.tsvf",
                    {
                        "coherence": self.last_tsvf_output.coherence,
                        "entropy": self.last_tsvf_output.entropy,
                        "signal": self.last_tsvf_output.signal,
                        "otoc": self.last_tsvf_output.otoc,
                        "timestamp": timestamp,
                    },
                )
                self.event_bus.publish(
                    RETROCAUSAL_INSIGHT_EVENT,
                    {
                        "timestamp": timestamp,
                        "prediction": predicted_future.tolist(),
                        "temporal_field_strength": field_strength,
                        "prediction_confidence": prediction_confidence,
                    },
                )

            logger.debug(
                f"Retrocausality analysis complete: {len(active_events)} events, "
                f"field_strength={field_strength:.3f}"
            )

            return state

        except Exception as e:
            logger.error(f"Error in retrocausality analysis: {e}")
            return self._create_initial_state(timestamp)

    async def _detect_retrocausal_events(
        self, current_data: np.ndarray, predicted_future: np.ndarray, timestamp: float
    ) -> List[RetrocausalEvent]:
        """Detect potential retrocausal events"""
        events = []

        try:
            # Analyze correlation between predicted future and past changes
            past_changes = self._extract_past_changes()
            future_indicators = self._extract_future_indicators(predicted_future)

            if len(past_changes) == 0 or len(future_indicators) == 0:
                return events

            # Find temporal correlations that suggest retrocausality
            retrocausal_correlations = self._find_retrocausal_correlations(
                past_changes, future_indicators, timestamp
            )

            # Convert correlations to events
            for correlation in retrocausal_correlations:
                event = self._create_retrocausal_event(correlation, timestamp)
                if event:
                    events.append(event)

            # Detect preparation patterns (market preparing for future events)
            preparation_events = self._detect_preparation_patterns(
                current_data, predicted_future, timestamp
            )
            events.extend(preparation_events)

            # Detect anticipation anomalies
            anticipation_events = self._detect_anticipation_anomalies(
                current_data, timestamp
            )
            events.extend(anticipation_events)

            return events

        except Exception as e:
            logger.warning(f"Retrocausal event detection failed: {e}")
            return []

    def _extract_past_changes(self) -> List[Tuple[np.ndarray, float]]:
        """Extract significant changes from past data buffer"""
        changes = []

        try:
            if len(self.past_buffer) < 2:
                return changes

            past_data = list(self.past_buffer)
            data_arr = np.stack([d for d, _ in past_data])
            times = np.array([t for _, t in past_data])

            diffs = np.diff(data_arr, axis=0)
            magnitudes = np.linalg.norm(diffs, axis=1)
            threshold = np.std(np.linalg.norm(data_arr, axis=1)) * 0.5

            valid = magnitudes > threshold
            for diff, time in zip(diffs[valid], times[1:][valid]):
                changes.append((diff, float(time)))

            return changes

        except Exception as e:
            logger.warning(f"Past change extraction failed: {e}")
            return []

    def _extract_future_indicators(self, predicted_future: np.ndarray) -> List[float]:
        """Extract key indicators from future predictions"""
        try:
            if len(predicted_future) == 0:
                return []

            # Extract various statistical measures as indicators
            indicators = [
                np.mean(predicted_future),
                np.std(predicted_future),
                np.max(predicted_future),
                np.min(predicted_future),
            ]

            # Add trend indicators if multi-dimensional
            if len(predicted_future.shape) > 1 and predicted_future.shape[0] > 1:
                trend = np.mean(np.diff(predicted_future, axis=0))
                indicators.append(trend)

            # Add momentum indicators
            if len(predicted_future) > 1:
                momentum = (
                    predicted_future[-1] - predicted_future[0]
                    if predicted_future.ndim == 1
                    else np.mean(predicted_future[-1] - predicted_future[0])
                )
                indicators.append(momentum)

            return indicators

        except Exception as e:
            logger.warning(f"Future indicator extraction failed: {e}")
            return []

    def _find_retrocausal_correlations(
        self,
        past_changes: List[Tuple[np.ndarray, float]],
        future_indicators: List[float],
        timestamp: float,
    ) -> List[Dict[str, Any]]:
        """Find correlations suggesting retrocausal effects"""
        correlations = []

        try:
            if len(past_changes) == 0 or len(future_indicators) == 0:
                return correlations

            # Prepare data for correlation analysis
            changes_arr = np.stack([c for c, _ in past_changes])
            change_magnitudes = np.linalg.norm(changes_arr, axis=1)
            change_times = np.array([t for _, t in past_changes])

            fut_vals = np.asarray(future_indicators, dtype=float)
            time_weights = np.exp(-(timestamp - change_times) / self.temporal_window)

            strengths = np.abs(fut_vals[:, None]) * change_magnitudes * time_weights
            mask = strengths > self.influence_threshold
            idx_f, idx_p = np.nonzero(mask)
            for i, j in zip(idx_f, idx_p):
                correlations.append(
                    {
                        "future_indicator_idx": int(i),
                        "past_change_idx": int(j),
                        "strength": float(strengths[i, j]),
                        "future_time": timestamp + self.temporal_window,
                        "past_time": float(change_times[j]),
                        "indicator_value": float(fut_vals[i]),
                        "change_magnitude": float(change_magnitudes[j]),
                    }
                )

            # Sort by strength
            correlations.sort(key=lambda x: x["strength"], reverse=True)

            return correlations[:20]  # Limit to top 20

        except Exception as e:
            logger.warning(f"Retrocausal correlation analysis failed: {e}")
            return []

    def _create_retrocausal_event(
        self, correlation: Dict[str, Any], timestamp: float
    ) -> Optional[RetrocausalEvent]:
        """Create retrocausal event from correlation data"""
        try:
            event_id = f"retro_{correlation['future_indicator_idx']}_{correlation['past_change_idx']}_{timestamp:.0f}"

            # Determine causal direction based on temporal relationship
            if correlation["future_time"] > correlation["past_time"]:
                causal_direction = "future_to_past"
            else:
                causal_direction = "past_to_future"

            # Calculate confidence based on strength and temporal separation
            temporal_separation = abs(
                correlation["future_time"] - correlation["past_time"]
            )
            confidence = correlation["strength"] * np.exp(
                -temporal_separation / self.temporal_window
            )

            event = RetrocausalEvent(
                event_id=event_id,
                future_time=correlation["future_time"],
                past_influence_time=correlation["past_time"],
                influence_strength=correlation["strength"],
                causal_direction=causal_direction,
                event_type="correlation",
                confidence=confidence,
            )

            return event

        except Exception as e:
            logger.warning(f"Retrocausal event creation failed: {e}")
            return None

    def _detect_preparation_patterns(
        self, current_data: np.ndarray, predicted_future: np.ndarray, timestamp: float
    ) -> List[RetrocausalEvent]:
        """Detect patterns suggesting market preparation for future events"""
        events = []

        try:
            if len(self.past_buffer) < 5:
                return events

            # Extract recent trend
            recent_data = [data for data, _ in list(self.past_buffer)[-5:]]
            recent_trend = self._calculate_trend(recent_data)

            # Compare with predicted future direction
            if len(predicted_future) > 0:
                future_direction = np.mean(predicted_future) - np.mean(current_data)

                # Check if current trend aligns with future direction
                alignment = (
                    np.dot(recent_trend, np.sign(future_direction))
                    if recent_trend.size > 0
                    else 0
                )

                if abs(alignment) > 0.5:  # Strong alignment
                    event_id = f"prep_{timestamp:.0f}"

                    event = RetrocausalEvent(
                        event_id=event_id,
                        future_time=timestamp + self.temporal_window,
                        past_influence_time=timestamp - 300,  # 5 minutes ago
                        influence_strength=abs(alignment),
                        causal_direction="future_to_past",
                        event_type="preparation",
                        confidence=abs(alignment) * 0.8,
                    )

                    events.append(event)

            return events

        except Exception as e:
            logger.warning(f"Preparation pattern detection failed: {e}")
            return []

    def _detect_anticipation_anomalies(
        self, current_data: np.ndarray, timestamp: float
    ) -> List[RetrocausalEvent]:
        """Detect anomalous behavior suggesting anticipation of future events"""
        events = []

        try:
            if len(self.past_buffer) < 10:
                return events

            # Calculate current anomaly score
            historical_data = [data for data, _ in self.past_buffer]
            historical_mean = np.mean(historical_data, axis=0)
            historical_std = np.std(historical_data, axis=0)

            # Z-score anomaly detection
            z_scores = (current_data - historical_mean) / (historical_std + 1e-8)
            anomaly_score = np.max(np.abs(z_scores))

            if anomaly_score > 2.0:  # 2 standard deviations
                event_id = f"anomaly_{timestamp:.0f}"

                event = RetrocausalEvent(
                    event_id=event_id,
                    future_time=timestamp + self.temporal_window,
                    past_influence_time=timestamp,
                    influence_strength=min(anomaly_score / 5.0, 1.0),  # Normalize
                    causal_direction="future_to_past",
                    event_type="anticipation_anomaly",
                    confidence=min(anomaly_score / 3.0, 1.0),
                )

                events.append(event)

            return events

        except Exception as e:
            logger.warning(f"Anticipation anomaly detection failed: {e}")
            return []

    def _calculate_trend(self, data_sequence: List[np.ndarray]) -> np.ndarray:
        """Calculate trend vector from data sequence"""
        try:
            if len(data_sequence) < 2:
                return np.array([])

            data_arr = np.stack(data_sequence)
            diffs = np.diff(data_arr, axis=0)
            trend = np.mean(diffs, axis=0)

            return trend

        except Exception as e:
            logger.warning(f"Trend calculation failed: {e}")
            return np.array([])

    def _calculate_temporal_field_strength(self) -> float:
        """Calculate overall temporal field strength"""
        try:
            if len(self.retrocausality_history) == 0:
                return 0.0

            # Average influence strength from recent events
            recent_states = self.retrocausality_history[-10:]  # Last 10 states

            total_influence = 0.0
            event_count = 0

            for state in recent_states:
                for event in state.active_events:
                    total_influence += event.influence_strength * event.confidence
                    event_count += 1

            if event_count == 0:
                return 0.0

            field_strength = total_influence / event_count
            return min(field_strength, 1.0)

        except Exception as e:
            logger.warning(f"Temporal field strength calculation failed: {e}")
            return 0.0

    def _detect_causal_loops(
        self, events: List[RetrocausalEvent]
    ) -> List[Tuple[str, str]]:
        """Detect causal loops between events"""
        loops = []

        try:
            # Find events that might form causal loops
            for i, event1 in enumerate(events):
                for j, event2 in enumerate(events[i + 1 :], i + 1):
                    # Check if events form a potential loop
                    if (
                        event1.causal_direction == "future_to_past"
                        and event2.causal_direction == "past_to_future"
                    ):
                        # Check temporal overlap
                        time_overlap = min(
                            event1.future_time, event2.future_time
                        ) - max(event1.past_influence_time, event2.past_influence_time)

                        if time_overlap > 0:
                            loops.append((event1.event_id, event2.event_id))

            return loops

        except Exception as e:
            logger.warning(f"Causal loop detection failed: {e}")
            return []

    def _calculate_prediction_confidence(
        self, current_data: np.ndarray, predicted_future: np.ndarray
    ) -> float:
        """Calculate confidence in predictions based on retrocausal consistency"""
        try:
            if len(self.past_buffer) < 5 or len(predicted_future) == 0:
                return 0.5

            # Compare predicted vs actual patterns from history
            historical_predictions = list(self.future_predictions)

            if len(historical_predictions) < 3:
                return 0.5

            # Calculate accuracy of past predictions
            accuracy_scores = []

            for pred_data, pred_time in historical_predictions[-5:]:
                # Find corresponding actual data
                actual_data = None
                for data, time in self.past_buffer:
                    if (
                        abs(time - (pred_time + self.temporal_window)) < 60
                    ):  # Within 1 minute
                        actual_data = data
                        break

                if actual_data is not None:
                    # Calculate prediction accuracy
                    error = np.linalg.norm(pred_data - actual_data)
                    scale = np.linalg.norm(actual_data) + 1e-8
                    accuracy = np.exp(-error / scale)
                    accuracy_scores.append(accuracy)

            if len(accuracy_scores) == 0:
                return 0.5

            confidence = np.mean(accuracy_scores)
            return np.clip(confidence, 0.0, 1.0)

        except Exception as e:
            logger.warning(f"Prediction confidence calculation failed: {e}")
            return 0.5

    def _update_event_registry(
        self, active_events: List[RetrocausalEvent], timestamp: float
    ):
        """Update event registry with current events"""
        try:
            # Add new events
            for event in active_events:
                self.event_registry[event.event_id] = event

            # Remove old events
            events_to_remove = []
            for event_id, event in self.event_registry.items():
                if timestamp - event.future_time > self.max_lookback:
                    events_to_remove.append(event_id)

            for event_id in events_to_remove:
                del self.event_registry[event_id]

        except Exception as e:
            logger.warning(f"Event registry update failed: {e}")

    def _create_initial_state(self, timestamp: float) -> RetrocausalityState:
        """Create initial empty retrocausality state"""
        return RetrocausalityState(
            active_events=[],
            temporal_field_strength=0.0,
            causal_loops=[],
            prediction_confidence=0.5,
            timestamp=timestamp,
            otoc_metric=0.0,
        )

    def is_retrocausally_active(self) -> bool:
        """Check if system shows retrocausal activity"""
        if self.current_state is None:
            return False

        return (
            len(self.current_state.active_events) > 0
            and self.current_state.temporal_field_strength > self.influence_threshold
        )

    def get_retrocausal_summary(self) -> Dict[str, Any]:
        """Get summary of current retrocausal state"""
        if self.current_state is None:
            return {}

        return {
            "is_active": self.is_retrocausally_active(),
            "field_strength": float(self.current_state.temporal_field_strength),
            "active_events": len(self.current_state.active_events),
            "causal_loops": len(self.current_state.causal_loops),
            "prediction_confidence": float(self.current_state.prediction_confidence),
            "total_registered_events": len(self.event_registry),
            "timestamp": self.current_state.timestamp,
        }

    def retrocausal_potential(self) -> float:
        """Return potential influence based on current field strength."""
        if self.current_state is None:
            return 0.0
        return float(
            self.current_state.temporal_field_strength * self.causality_strength
        )

    def calculate_entropy(self) -> float:
        """Return normalized entropy of the TSVF state vector."""

        if (
            self.last_tsvf_output is None
            or self.last_tsvf_output.state_vector.size == 0
        ):
            return 0.0

        vec = self.last_tsvf_output.state_vector
        probs = np.abs(vec) ** 2
        total = probs.sum()
        if total == 0:
            return 0.0
        probs = probs / total
        probs = probs[probs > 0]
        ent = float(np.sum(-probs * np.log(probs)))
        max_ent = np.log(probs.size)
        return ent / max_ent if max_ent > 0 else 0.0

    def get_state_dict(self) -> Dict[str, Any]:
        """Get serializable state dictionary"""
        return {
            "temporal_window": self.temporal_window,
            "influence_threshold": self.influence_threshold,
            "causality_strength": self.causality_strength,
            "current_field_strength": (
                float(self.current_state.temporal_field_strength)
                if self.current_state
                else 0.0
            ),
            "active_events": (
                len(self.current_state.active_events) if self.current_state else 0
            ),
            "registered_events": len(self.event_registry),
            "history_length": len(self.retrocausality_history),
        }

    def causal_loop_ratio(self) -> float:
        """Return ratio of causal loops to active retrocausal events.

        Returns
        -------
        float
            Fraction of detected loops relative to the total number of active
            events. ``0.0`` is returned when no events are present.

        Examples
        --------
        >>> op = RetrocausalityOperator({})
        >>> op.current_state = None
        >>> op.causal_loop_ratio()
        0.0
        """

        if self.current_state is None or len(self.current_state.active_events) == 0:
            return 0.0

        total = len(self.current_state.active_events)
        loops = len(self.current_state.causal_loops)
        return float(loops / total)

    def apply_effects(self, universe: Any, *, threshold: float = 0.7) -> None:
        """Modify universe parameters when retrocausality is strong.

        Parameters
        ----------
        universe
            Instance of :class:`QUALIAQuantumUniverse` to adjust.
        threshold
            Field strength required to trigger adjustments.
        """

        if self.current_state is None:
            return

        if self.current_state.temporal_field_strength > threshold:
            try:
                universe.apply_retrocausal_feedback(
                    self.current_state.temporal_field_strength
                )
            except Exception as exc:  # pragma: no cover - universe missing method
                logger.debug("apply_effects failed: %s", exc)

        if (
            self.last_tsvf_output is not None
            and self.last_tsvf_output.otoc > 0.8
            and hasattr(universe, "lambda_factor_multiplier")
        ):
            prev_val = universe.lambda_factor_multiplier
            universe.lambda_factor_multiplier *= self.entropy_reduction
            logger.info(
                "Retrocausal entropy reduction: lambda_factor_multiplier %.3f -> %.3f",
                prev_val,
                universe.lambda_factor_multiplier,
            )

    async def adjust_envelope(self, envelope: IntentionEnvelope) -> IntentionEnvelope:
        """Ajusta ``envelope`` usando previsões retrocausais simples."""

        signal = self.last_tsvf_output.signal if self.last_tsvf_output else 0.0
        try:
            scale = 1.0 + float(signal)
        except Exception:
            scale = 1.0

        new_target = max(0.0, envelope.profit_target * scale)
        return IntentionEnvelope(
            profit_target=new_target,
            max_drawdown=envelope.max_drawdown,
            horizon_hours=envelope.horizon_hours,
        )

    def tsvf_channel(
        self,
        past_prices: np.ndarray,
        future_prices: np.ndarray,
        *,
        window: int | None = None,
        alpha: float | None = None,
        gamma: float | None = None,
        c_e: float | None = None,
        c_h: float | None = None,
        size: int | None = None,
    ) -> TSVFOutput:
        """Wrapper over :func:`_tsvf_channel_impl` using operator defaults.

        Parameters
        ----------
        past_prices : np.ndarray
            Historical price series.
        future_prices : np.ndarray
            Future price series.
        window : int, optional
            Window size for the TSVF transformation.
        alpha : float, optional
            Forward mixing coefficient.
        gamma : float, optional
            Backward mixing coefficient.
        c_e : float, optional
            Coherence emission factor.
        c_h : float, optional
            Entropy weight.
        size : int, optional
            Vector size for the state representation.

        Returns
        -------
        TSVFOutput
            Aggregated TSVF metrics.
        """
        out = _tsvf_channel_impl(
            past_prices,
            future_prices,
            window=window or self.tsvf_window,
            alpha=self.tsvf_alpha if alpha is None else alpha,
            gamma=self.tsvf_gamma if gamma is None else gamma,
            c_e=self.tsvf_cE if c_e is None else c_e,
            c_h=self.tsvf_cH if c_h is None else c_h,
            size=self.tsvf_size if size is None else size,
        )

        if out.otoc > 0.8 and self.entropy_reduction < 1.0:
            out = TSVFOutput(
                state_vector=out.state_vector,
                coherence=out.coherence,
                entropy=out.entropy * self.entropy_reduction,
                emitted=out.emitted,
                signal=out.signal,
                otoc=out.otoc,
            )

        return out


def apply_retrocausality(
    field: np.ndarray,
    future: np.ndarray | None,
    gamma: float | None = None,
    *,
    decay_rate: float = 0.0,
    shift: int = 0,
    history: Optional[List[np.ndarray]] = None,
) -> np.ndarray:
    """Blend ``field`` with ``future`` using weight ``gamma``.

    Parameters
    ----------
    field
        Present state array.
    future
        Future state array to blend with. If ``None`` no blending is applied.
    gamma
        Blending factor within ``[0, 1]``. If ``None``, the value
        is loaded from ``qualia.config.settings``.
    decay_rate
        Optional smoothing factor for blending with the previous result stored
        in ``history``.
    shift
        Number of positions to circularly shift ``future`` before blending.
    """

    if not isinstance(field, np.ndarray):
        raise TypeError("field must be a numpy array")
    if future is not None:
        if not isinstance(future, np.ndarray):
            raise TypeError("future must be a numpy array when provided")
        if field.shape != future.shape:
            raise ValueError("field and future must have the same shape")
    if gamma is None:
        from ..config.settings import settings

        gamma = settings.retrocausality_gamma
    if not isinstance(gamma, (int, float)):
        raise TypeError("gamma must be a float")
    if not 0.0 <= float(gamma) <= 1.0:
        raise ValueError("gamma must be between 0 and 1")

    if future is None:
        result = field.astype(float, copy=False)
    else:
        if shift:
            future = np.roll(future, int(shift))
        result = (1 - float(gamma)) * field + float(gamma) * future

    if history is not None and decay_rate:
        if history:
            result = (1 - float(decay_rate)) * result + float(decay_rate) * history[-1]

    if history is not None:
        history.append(np.array(result))
        if len(history) > 1000:
            history.pop(0)

    return result


def _tsvf_channel_impl(
    past_prices: np.ndarray,
    future_prices: np.ndarray,
    *,
    window: int | None = None,
    alpha: float = 0.3,
    gamma: float = 0.1,
    c_e: float = 0.1,
    c_h: float = 0.05,
    size: int = 100,
) -> TSVFOutput:
    """Compute TSVF-based retrocausal blending of price windows."""

    if not isinstance(past_prices, np.ndarray) or not isinstance(
        future_prices, np.ndarray
    ):
        raise TypeError("past_prices and future_prices must be numpy arrays")
    if past_prices.size == 0 or future_prices.size == 0:
        raise ValueError("price arrays must be non-empty")

    if window is None:
        window = min(past_prices.size, future_prices.size)

    past_seg = past_prices[-window:]
    future_seg = future_prices[:window]

    psi_in = np.interp(past_seg, (past_seg.min(), past_seg.max()), (-1.0, 1.0))
    psi_fin = np.interp(future_seg, (future_seg.min(), future_seg.max()), (-1.0, 1.0))

    if window < size:
        psi_in = np.pad(psi_in, (0, size - window))
        psi_fin = np.pad(psi_fin, (0, size - window))

    psi_in = psi_in / (np.linalg.norm(psi_in) + 1e-12)
    psi_fin = psi_fin / (np.linalg.norm(psi_fin) + 1e-12)

    otoc_val = compute_otoc(psi_in, psi_fin)

    psi_fwd = (1.0 - alpha) * psi_in + alpha * psi_fin
    psi_bwd = psi_fwd + gamma * (psi_fin - psi_fwd)
    psi = psi_bwd / (np.linalg.norm(psi_bwd) + 1e-12)

    coherence = 1.0 - np.var(psi) / (np.var(psi_in) + 1e-12)
    probs = np.abs(psi) ** 2
    entropy_val = entropy(probs / np.sum(probs))
    entropy_val *= 1.0 + (1.0 - otoc_val)

    emitted = c_e * coherence + c_h * entropy_val

    future_return = future_seg[-1] - past_seg[-1]
    top_k = np.sort(np.abs(psi))[-5:]
    signal = float(np.mean(top_k)) * np.sign(future_return)
    signal *= otoc_val

    return TSVFOutput(
        state_vector=psi,
        coherence=float(coherence),
        entropy=float(entropy_val),
        emitted=float(emitted),
        signal=float(signal),
        otoc=float(otoc_val),
    )


def compute_otoc(state_a: np.ndarray, state_b: np.ndarray) -> float:
    """Calculate an approximate OTOC using the state overlap.

    Parameters
    ----------
    state_a
        Initial quantum state vector.
    state_b
        Quantum state vector at a later time.

    Returns
    -------
    float
        Approximated OTOC value in the ``[0, 1]`` range.
    """

    from qiskit.quantum_info import Statevector

    sv_a = Statevector(state_a / (np.linalg.norm(state_a) + 1e-12))
    sv_b = Statevector(state_b / (np.linalg.norm(state_b) + 1e-12))
    overlap = sv_a.data.conj().dot(sv_b.data)
    return float(np.abs(overlap) ** 2)
