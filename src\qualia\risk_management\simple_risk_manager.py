from __future__ import annotations

"""Implementação simples de um gerenciador de risco."""

from collections import deque
from typing import Any, Deque, Dict, Optional, Tuple

from ..memory.event_bus import SimpleEventBus
from ..events import RiskUpdateEvent

from ..config.settings import get_env
from ..core.qualia_logger import log_event

from ..utils.logger import get_logger
from ..config.feature_flags import feature_toggle
from ..risk_management.risk_manager_base import (
    QUALIARiskManagerBase,
    CapitalUpdateResult,
    PositionSizingResult,
)

logger = get_logger(__name__)

HISTORY_LIMIT = 1000
"""Fallback history length for internal deques."""

try:
    HISTORY_LIMIT = int(
        get_env("QUALIA_RISK_HISTORY_LIMIT", str(HISTORY_LIMIT), warn=False)
        or HISTORY_LIMIT
    )
except ValueError:
    logger.warning("Invalid QUALIA_RISK_HISTORY_LIMIT; using default %s", HISTORY_LIMIT)


class SimpleRiskManager(QUALIARiskManagerBase):
    """Gerenciador de risco básico usado em testes unitários.

    A profundidade do histórico mantido em ``position_history`` e
    ``trade_history`` pode ser ajustada através da variável de ambiente
    ``QUALIA_RISK_HISTORY_LIMIT``. Caso ela não seja definida ou contenha
    valor inválido, utiliza-se o valor ``%s``.
    """ % HISTORY_LIMIT

    def __init__(
        self,
        initial_capital: float,
        risk_profile: str = "balanced",
        risk_per_trade_pct: float = 1.0,
        max_position_size_pct: float = 10.0,
        max_open_positions: int = 5,
        event_bus: Optional[SimpleEventBus] = None,
    ) -> None:
        """Inicializa o gerenciador de risco simples.

        O tamanho do histórico mantido em memória é definido por
        ``QUALIA_RISK_HISTORY_LIMIT`` (vide docstring da classe).

        Args:
            initial_capital: Quantia de capital disponível para iniciar as
                operações.
            risk_profile: Perfil de risco adotado. O valor padrão ``"balanced"``
                busca equilibrar exposição e cautela.
            risk_per_trade_pct: Percentual do capital arriscado em cada trade.
                Utiliza ``1.0`` (1%) por padrão.
            max_position_size_pct: Limite máximo de capital alocado por
                posição. O padrão ``10.0`` representa 10% do capital total.
            max_open_positions: Número máximo de posições abertas
                simultaneamente. Padrão ``5``.
        """

        super().__init__(initial_capital, risk_profile, event_bus=event_bus)
        self.risk_per_trade_pct = risk_per_trade_pct
        self.max_position_size_pct = max_position_size_pct
        self.max_open_positions = max_open_positions
        self.position_history: Deque[Dict[str, Any]] = deque(maxlen=HISTORY_LIMIT)
        self.trade_history: Deque[Dict[str, Any]] = deque(maxlen=HISTORY_LIMIT)
        self.capital_history: Deque[float] = deque(maxlen=HISTORY_LIMIT)
        self.capital_history.append(initial_capital)
        self.drawdown_history: Deque[float] = deque(maxlen=HISTORY_LIMIT)
        self.drawdown_history.append(0.0)

    def calculate_position_size(
        self,
        symbol: str,
        current_price: float,
        stop_loss_price: float,
        confidence: float = 0.5,
        volatility: Optional[float] = None,
        volume: Optional[float] = None,
        min_lot_size: Optional[float] = None,
        informational_mass: Optional[float] = None,
        initial_informational_mass: Optional[float] = None,
        lambda_factor: Optional[float] = None,
        *,
        trace_id: Optional[str] = None,
    ) -> PositionSizingResult:
        """Return recommended position metrics for a trade.

        Parameters
        ----------
        symbol
            Identificador do ativo negociado.
        current_price
            Preço atual em moeda de cotação.
        stop_loss_price
            Nível de stop utilizado para calcular o risco.
        confidence
            Índice de confiança da estratégia.
        volatility
            Volatilidade opcional do ativo (ignorada aqui).
        volume
            Volume negociado no período corrente.
        min_lot_size
            Tamanho mínimo do lote permitido pela exchange.
        informational_mass
            Massa informacional vinda da camada quântica (não utilizada).
        initial_informational_mass
            Massa inicial para escalonamento (não utilizada).

        Returns
        -------
        dict
            Informações de sizing contendo ``position_size`` e ``quantity``.
        """

        if current_price <= 0:
            logger.warning(
                "Invalid current price %.2f for %s | confidence=%.2f volume=%s volatility=%s",
                current_price,
                symbol,
                confidence,
                volume if volume is not None else "n/a",
                volatility if volatility is not None else "n/a",
            )
            return {
                "position_allowed": False,
                "reason": "invalid_price",
                "position_size": 0.0,
                "quantity": 0.0,
                "risk_amount": 0.0,
            }
        factor = lambda_factor if lambda_factor is not None else 1.0
        risk_amount = self.current_capital * (self.risk_per_trade_pct * factor / 100)
        stop_distance_pct = (
            abs((stop_loss_price - current_price) / current_price) * 100
            if stop_loss_price is not None
            else 1.0
        )
        if stop_distance_pct == 0:
            stop_distance_pct = 1.0
        position_size = risk_amount / (stop_distance_pct / 100)
        max_size = self.current_capital * (self.max_position_size_pct * factor / 100)
        if position_size > max_size:
            position_size = max_size
        quantity = position_size / current_price if current_price > 0 else 0
        if min_lot_size and quantity < min_lot_size:
            quantity = min_lot_size
            position_size = quantity * current_price
        self.position_history.append(
            {
                "symbol": symbol,
                "size": position_size,
                "quantity": quantity,
                "risk_amount": risk_amount,
            }
        )
        return {
            "position_allowed": True,
            "position_size": position_size,
            "quantity": quantity,
            "risk_amount": risk_amount,
        }

    def update_capital(self, new_capital: float) -> CapitalUpdateResult:
        """Atualiza o capital interno e publica evento ``risk.update``."""

        self.current_capital = new_capital
        self.capital_history.append(new_capital)
        log_event(
            event_type="risk.update",
            payload={"new_capital": self.current_capital},
            source="risk_management",
            level="info",
        )
        if self.event_bus:
            self.event_bus.publish(
                "risk.update",
                RiskUpdateEvent(new_capital=self.current_capital),
            )
            from ..events import RiskRecalibratedEvent

            self.event_bus.publish(
                "risk.recalibrated",
                RiskRecalibratedEvent(
                    current_capital=self.current_capital,
                    drawdown_pct=0.0,
                ),
            )
            log_event(
                event_type="risk.recalibrated",
                payload={
                    "current_capital": self.current_capital,
                    "drawdown_pct": 0.0,
                },
                source="risk_management",
            )
        return {"current_capital": self.current_capital}

    def can_open_new_position(
        self, current_positions: int, *, trace_id: Optional[str] = None
    ) -> Tuple[bool, str]:
        """Indica se uma nova posição pode ser aberta dado o limite atual."""

        if current_positions >= self.max_open_positions:
            return False, "max open positions reached"
        return True, "ok"

    def process_trade_result(self, trade_info: Dict[str, Any]) -> CapitalUpdateResult:
        """Registra o resultado de uma trade e ajusta o capital."""

        pnl = trade_info.get("realized_pnl", 0)
        self.trade_history.append(trade_info)
        return self.update_capital(self.current_capital + pnl)
