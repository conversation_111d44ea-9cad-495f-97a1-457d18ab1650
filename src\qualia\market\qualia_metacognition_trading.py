"""Compatibility layer for the metacognitive trading component.

This module exists solely to expose ``QUALIAMetacognitionTrading`` and its
data structures from ``src.qualia.metacognition.metacognition_trading`` under
the ``market`` namespace.  It allows other parts of the pipeline
(e.g., the trading engine) to depend on a stable import path without
duplicating logic.

The actual implementation resides in
``src.qualia.metacognition.metacognition_trading``.

Examples
--------
    >>> from ..market.qualia_metacognition_trading import (
    ...     QUALIAMetacognitionTrading,
    ... )
    >>> qmt = QUALIAMetacognitionTrading()
"""

from ..metacognition.metacognition_trading import (
    QUALIAMetacognitionTrading,
    TradeSignal,
    MetacognitiveContext,
    QASTTuningDirectives,
    ACEModulationParameters,
    DecisionContext,
)

__all__ = [
    "QUALIAMetacognitionTrading",
    "TradeSignal",
    "MetacognitiveContext",
    "QASTTuningDirectives",
    "ACEModulationParameters",
    "DecisionContext",
]
