"""
Sistema de Health Check para QUALIA
Verifica o status e readiness de todos os componentes críticos do sistema.
"""

import asyncio
from qualia.utils.logger import get_logger
import time
from dataclasses import dataclass, field
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
import pandas as pd
import numpy as np

logger = get_logger(__name__)


class ComponentStatus(Enum):
    """Status possíveis para um componente do sistema."""

    HEALTHY = "healthy"
    WARNING = "warning"
    ERROR = "error"
    NOT_INITIALIZED = "not_initialized"
    INITIALIZING = "initializing"
    DEGRADED = "degraded"
    OFFLINE = "offline"


class SystemReadiness(Enum):
    """Nível de readiness do sistema completo."""

    READY = "ready"
    NOT_READY = "not_ready"
    DEGRADED_READY = "degraded_ready"
    INITIALIZING = "initializing"
    CRITICAL_ERROR = "critical_error"


@dataclass
class ComponentHealthInfo:
    """Informações detalhadas sobre a saúde de um componente."""

    name: str
    status: ComponentStatus
    message: str
    details: Dict[str, Any] = field(default_factory=dict)
    last_check: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    uptime_seconds: Optional[float] = None
    error_count: int = 0
    warning_count: int = 0

    @property
    def is_healthy(self) -> bool:
        return self.status == ComponentStatus.HEALTHY

    @property
    def is_operational(self) -> bool:
        return self.status in [
            ComponentStatus.HEALTHY,
            ComponentStatus.WARNING,
            ComponentStatus.DEGRADED,
        ]


@dataclass
class ReadinessReport:
    """Relatório completo de readiness do sistema."""

    overall_status: SystemReadiness
    readiness_score: float  # 0.0 a 1.0
    components: Dict[str, ComponentHealthInfo]
    critical_issues: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    @property
    def is_ready_for_trading(self) -> bool:
        """Determina se o sistema está pronto para trading."""
        return self.overall_status in [
            SystemReadiness.READY,
            SystemReadiness.DEGRADED_READY,
        ]

    @property
    def critical_components_healthy(self) -> bool:
        """Verifica se componentes críticos estão saudáveis."""
        critical_components = ["data_collector", "strategies", "risk_manager"]
        return all(
            self.components.get(
                comp, ComponentHealthInfo("", ComponentStatus.ERROR, "")
            ).is_operational
            for comp in critical_components
        )


class SystemHealthChecker:
    """Sistema principal de verificação de saúde dos componentes."""

    def __init__(self, system_components: Optional[Dict[str, Any]] = None):
        self.system_components = system_components or {}
        self.check_history: List[ReadinessReport] = []
        self.component_weights = {
            "data_collector": 0.25,
            "quantum_layer": 0.15,
            "holographic_universe": 0.15,
            "strategies": 0.25,
            "risk_manager": 0.20,
        }

    def register_component(self, name: str, component: Any) -> None:
        """Registra um componente para monitoramento."""
        self.system_components[name] = component
        logger.info(f"Componente '{name}' registrado para health check")

    async def check_component_status(self) -> Dict[str, ComponentHealthInfo]:
        """Verifica o status de todos os componentes registrados."""
        results = {}

        # Verificar componentes críticos
        results["data_collector"] = await self._check_data_collector()
        results["quantum_layer"] = await self._check_quantum_layer()
        results["holographic_universe"] = await self._check_holographic()
        results["strategies"] = await self._check_strategies()
        results["risk_manager"] = await self._check_risk_manager()

        # Verificar componentes adicionais registrados
        for name, component in self.system_components.items():
            if name not in results:
                results[name] = await self._check_generic_component(name, component)

        return results

    async def get_readiness_report(self) -> ReadinessReport:
        """Gera relatório completo de readiness do sistema."""
        components = await self.check_component_status()

        # Calcular score de readiness
        readiness_score = self._calculate_readiness_score(components)

        # Determinar status geral
        overall_status = self._determine_overall_status(components, readiness_score)

        # Coletar issues e recomendações
        critical_issues, warnings, recommendations = self._analyze_issues(components)

        report = ReadinessReport(
            overall_status=overall_status,
            readiness_score=readiness_score,
            components=components,
            critical_issues=critical_issues,
            warnings=warnings,
            recommendations=recommendations,
        )

        self.check_history.append(report)
        return report

    async def _check_data_collector(self) -> ComponentHealthInfo:
        """Verifica o status do coletor de dados."""
        component = self.system_components.get("data_collector")

        if not component:
            return ComponentHealthInfo(
                name="data_collector",
                status=ComponentStatus.NOT_INITIALIZED,
                message="Data collector não encontrado no sistema",
            )

        try:
            details = {}
            status = ComponentStatus.HEALTHY
            message = "Data collector operacional"

            # Verificar se tem dados recentes
            if hasattr(component, "last_update_time"):
                last_update = getattr(component, "last_update_time", None)
                if last_update:
                    time_since_update = time.time() - last_update
                    details["seconds_since_last_update"] = time_since_update

                    if time_since_update > 300:  # 5 minutos
                        status = ComponentStatus.ERROR
                        message = (
                            f"Dados desatualizados há {time_since_update:.0f} segundos"
                        )
                    elif time_since_update > 120:  # 2 minutos
                        status = ComponentStatus.WARNING
                        message = f"Dados podem estar desatualizados ({time_since_update:.0f}s)"

            # Verificar quantidade de dados
            if hasattr(component, "get_data_count"):
                data_count = component.get_data_count()
                details["data_points_available"] = data_count

                if data_count < 10:
                    status = ComponentStatus.ERROR
                    message = (
                        f"Dados insuficientes: apenas {data_count} pontos disponíveis"
                    )
                elif data_count < 20:
                    status = ComponentStatus.WARNING
                    message = f"Poucos dados disponíveis: {data_count} pontos"

            # Verificar conexões ativas
            if hasattr(component, "active_connections"):
                connections = getattr(component, "active_connections", 0)
                details["active_connections"] = connections

                if connections == 0:
                    status = ComponentStatus.ERROR
                    message = "Nenhuma conexão ativa com fontes de dados"

            return ComponentHealthInfo(
                name="data_collector", status=status, message=message, details=details
            )

        except Exception as e:
            logger.error(f"Erro ao verificar data collector: {e}")
            return ComponentHealthInfo(
                name="data_collector",
                status=ComponentStatus.ERROR,
                message=f"Erro na verificação: {str(e)}",
                details={"exception": str(e)},
            )

    async def _check_quantum_layer(self) -> ComponentHealthInfo:
        """Verifica o status da camada quântica."""
        component = self.system_components.get("quantum_layer")

        if not component:
            # Tentar encontrar via metacognition
            metacognition = self.system_components.get("metacognition")
            if metacognition and hasattr(metacognition, "meta_layer"):
                component = metacognition.meta_layer

        if not component:
            return ComponentHealthInfo(
                name="quantum_layer",
                status=ComponentStatus.NOT_INITIALIZED,
                message="QuantumMetacognitionLayer não encontrada",
            )

        try:
            details = {}
            status = ComponentStatus.HEALTHY
            message = "Quantum layer operacional"

            # Verificar inicialização
            if hasattr(component, "is_initialized"):
                if not component.is_initialized():
                    status = ComponentStatus.NOT_INITIALIZED
                    message = "Quantum layer não inicializada"

            # Verificar processamento recente
            if hasattr(component, "last_evaluation_time"):
                last_eval = getattr(component, "last_evaluation_time", None)
                if last_eval:
                    time_since_eval = time.time() - last_eval
                    details["seconds_since_last_evaluation"] = time_since_eval

                    if time_since_eval > 600:  # 10 minutos
                        status = ComponentStatus.WARNING
                        message = "Nenhuma avaliação quântica recente"

            # Verificar cache de padrões
            if hasattr(component, "pattern_cache_size"):
                cache_size = component.pattern_cache_size()
                details["pattern_cache_size"] = cache_size

                if cache_size == 0:
                    status = ComponentStatus.WARNING
                    message = "Cache de padrões quânticos vazio"

            return ComponentHealthInfo(
                name="quantum_layer", status=status, message=message, details=details
            )

        except Exception as e:
            logger.error(f"Erro ao verificar quantum layer: {e}")
            return ComponentHealthInfo(
                name="quantum_layer",
                status=ComponentStatus.ERROR,
                message=f"Erro na verificação: {str(e)}",
                details={"exception": str(e)},
            )

    async def _check_holographic(self) -> ComponentHealthInfo:
        """Verifica o status do universo holográfico."""
        component = self.system_components.get("holographic_universe")

        if not component:
            return ComponentHealthInfo(
                name="holographic_universe",
                status=ComponentStatus.NOT_INITIALIZED,
                message="Holographic universe não encontrado",
            )

        try:
            details = {}
            status = ComponentStatus.HEALTHY
            message = "Universo holográfico operacional"

            # Verificar energia do campo
            if hasattr(component, "get_field_energy"):
                field_energy = component.get_field_energy()
                details["field_energy"] = field_energy

                if field_energy == 0:
                    status = ComponentStatus.WARNING
                    message = "Energia do campo holográfico é zero"
                elif field_energy < 0.01:
                    status = ComponentStatus.WARNING
                    message = f"Energia do campo muito baixa: {field_energy:.6f}"

            # Verificar padrões detectados
            if hasattr(component, "get_pattern_count"):
                pattern_count = component.get_pattern_count()
                details["patterns_detected"] = pattern_count

                if pattern_count == 0:
                    status = ComponentStatus.WARNING
                    message = "Nenhum padrão holográfico detectado"
                elif pattern_count < 5:
                    status = ComponentStatus.WARNING
                    message = f"Poucos padrões detectados: {pattern_count}"

            # Verificar última atualização
            if hasattr(component, "last_analysis_time"):
                last_analysis = getattr(component, "last_analysis_time", None)
                if last_analysis:
                    time_since_analysis = time.time() - last_analysis
                    details["seconds_since_last_analysis"] = time_since_analysis

                    if time_since_analysis > 180:  # 3 minutos
                        status = ComponentStatus.WARNING
                        message = "Análise holográfica desatualizada"

            return ComponentHealthInfo(
                name="holographic_universe",
                status=status,
                message=message,
                details=details,
            )

        except Exception as e:
            logger.error(f"Erro ao verificar holographic universe: {e}")
            return ComponentHealthInfo(
                name="holographic_universe",
                status=ComponentStatus.ERROR,
                message=f"Erro na verificação: {str(e)}",
                details={"exception": str(e)},
            )

    async def _check_strategies(self) -> ComponentHealthInfo:
        """Verifica o status das estratégias de trading."""
        strategies = self.system_components.get("strategies", {})

        if not strategies:
            return ComponentHealthInfo(
                name="strategies",
                status=ComponentStatus.NOT_INITIALIZED,
                message="Nenhuma estratégia carregada",
            )

        try:
            details = {}
            status = ComponentStatus.HEALTHY
            message = "Estratégias operacionais"

            # Contar estratégias ativas
            active_strategies = 0
            error_strategies = 0
            strategy_details = {}

            for symbol, strategy in strategies.items():
                try:
                    if hasattr(strategy, "is_ready"):
                        if strategy.is_ready():
                            active_strategies += 1
                            strategy_details[symbol] = "ready"
                        else:
                            error_strategies += 1
                            strategy_details[symbol] = "not_ready"
                    else:
                        active_strategies += (
                            1  # Assumir que está pronta se não tem método
                        )
                        strategy_details[symbol] = "assumed_ready"

                except Exception as e:
                    error_strategies += 1
                    strategy_details[symbol] = f"error: {str(e)}"

            details["active_strategies"] = active_strategies
            details["error_strategies"] = error_strategies
            details["strategy_details"] = strategy_details

            if error_strategies > 0:
                if active_strategies == 0:
                    status = ComponentStatus.ERROR
                    message = f"Todas as {error_strategies} estratégias com erro"
                else:
                    status = ComponentStatus.WARNING
                    message = f"{error_strategies} estratégias com erro, {active_strategies} operacionais"
            elif active_strategies == 0:
                status = ComponentStatus.ERROR
                message = "Nenhuma estratégia ativa"
            else:
                message = f"{active_strategies} estratégias operacionais"

            return ComponentHealthInfo(
                name="strategies", status=status, message=message, details=details
            )

        except Exception as e:
            logger.error(f"Erro ao verificar strategies: {e}")
            return ComponentHealthInfo(
                name="strategies",
                status=ComponentStatus.ERROR,
                message=f"Erro na verificação: {str(e)}",
                details={"exception": str(e)},
            )

    async def _check_risk_manager(self) -> ComponentHealthInfo:
        """Verifica o status do gerenciador de risco."""
        component = self.system_components.get("risk_manager")

        if not component:
            return ComponentHealthInfo(
                name="risk_manager",
                status=ComponentStatus.NOT_INITIALIZED,
                message="Risk manager não encontrado",
            )

        try:
            details = {}
            status = ComponentStatus.HEALTHY
            message = "Risk manager operacional"

            # Verificar configurações de risco
            if hasattr(component, "max_risk_per_trade"):
                max_risk = getattr(component, "max_risk_per_trade", 0)
                details["max_risk_per_trade"] = max_risk

                if max_risk <= 0:
                    status = ComponentStatus.ERROR
                    message = "Configuração de risco inválida"
                elif max_risk > 0.1:  # 10%
                    status = ComponentStatus.WARNING
                    message = f"Risco por trade muito alto: {max_risk*100:.1f}%"

            # Verificar posições abertas
            if hasattr(component, "get_open_positions"):
                open_positions = component.get_open_positions()
                details["open_positions"] = len(open_positions) if open_positions else 0

                if details["open_positions"] > 10:
                    status = ComponentStatus.WARNING
                    message = f"Muitas posições abertas: {details['open_positions']}"

            # Verificar capital disponível
            if hasattr(component, "available_capital"):
                capital = getattr(component, "available_capital", 0)
                details["available_capital"] = capital

                if capital <= 0:
                    status = ComponentStatus.ERROR
                    message = "Capital indisponível para trading"
                elif capital < 1000:
                    status = ComponentStatus.WARNING
                    message = f"Capital baixo: ${capital:.2f}"

            return ComponentHealthInfo(
                name="risk_manager", status=status, message=message, details=details
            )

        except Exception as e:
            logger.error(f"Erro ao verificar risk manager: {e}")
            return ComponentHealthInfo(
                name="risk_manager",
                status=ComponentStatus.ERROR,
                message=f"Erro na verificação: {str(e)}",
                details={"exception": str(e)},
            )

    async def _check_generic_component(
        self, name: str, component: Any
    ) -> ComponentHealthInfo:
        """Verificação genérica para componentes não específicos."""
        try:
            details = {}
            status = ComponentStatus.HEALTHY
            message = f"Componente {name} disponível"

            # Verificações básicas
            if hasattr(component, "is_healthy"):
                if not component.is_healthy():
                    status = ComponentStatus.WARNING
                    message = f"Componente {name} reporta não estar saudável"

            if hasattr(component, "get_status"):
                comp_status = component.get_status()
                details["component_status"] = comp_status

                if isinstance(comp_status, dict):
                    if comp_status.get("status") == "error":
                        status = ComponentStatus.ERROR
                        message = f"Componente {name} em estado de erro"

            return ComponentHealthInfo(
                name=name, status=status, message=message, details=details
            )

        except Exception as e:
            logger.error(f"Erro ao verificar componente {name}: {e}")
            return ComponentHealthInfo(
                name=name,
                status=ComponentStatus.ERROR,
                message=f"Erro na verificação: {str(e)}",
                details={"exception": str(e)},
            )

    def _calculate_readiness_score(
        self, components: Dict[str, ComponentHealthInfo]
    ) -> float:
        """Calcula score de readiness baseado no status dos componentes."""
        total_weight = 0
        weighted_score = 0

        for name, info in components.items():
            weight = self.component_weights.get(name, 0.05)  # Peso padrão baixo
            total_weight += weight

            # Score baseado no status
            if info.status == ComponentStatus.HEALTHY:
                score = 1.0
            elif info.status == ComponentStatus.WARNING:
                score = 0.7
            elif info.status == ComponentStatus.DEGRADED:
                score = 0.5
            elif info.status == ComponentStatus.INITIALIZING:
                score = 0.3
            else:  # ERROR, NOT_INITIALIZED, OFFLINE
                score = 0.0

            weighted_score += weight * score

        return weighted_score / total_weight if total_weight > 0 else 0.0

    def _determine_overall_status(
        self, components: Dict[str, ComponentHealthInfo], readiness_score: float
    ) -> SystemReadiness:
        """Determina o status geral do sistema."""
        critical_components = ["data_collector", "strategies", "risk_manager"]

        # Verificar componentes críticos
        critical_errors = sum(
            1
            for name in critical_components
            if components.get(
                name, ComponentHealthInfo("", ComponentStatus.ERROR, "")
            ).status
            in [ComponentStatus.ERROR, ComponentStatus.NOT_INITIALIZED]
        )

        if critical_errors > 0:
            return SystemReadiness.CRITICAL_ERROR

        # Verificar se algum componente está inicializando
        initializing = any(
            info.status == ComponentStatus.INITIALIZING for info in components.values()
        )

        if initializing:
            return SystemReadiness.INITIALIZING

        # Baseado no score de readiness
        if readiness_score >= 0.9:
            return SystemReadiness.READY
        elif readiness_score >= 0.6:
            return SystemReadiness.DEGRADED_READY
        else:
            return SystemReadiness.NOT_READY

    def _analyze_issues(self, components: Dict[str, ComponentHealthInfo]) -> tuple:
        """Analisa componentes e gera listas de issues e recomendações."""
        critical_issues = []
        warnings = []
        recommendations = []

        for name, info in components.items():
            if info.status == ComponentStatus.ERROR:
                critical_issues.append(f"{name}: {info.message}")
                recommendations.append(f"Corrigir erro em {name}: {info.message}")
            elif info.status == ComponentStatus.NOT_INITIALIZED:
                critical_issues.append(f"{name}: Componente não inicializado")
                recommendations.append(f"Inicializar componente {name}")
            elif info.status == ComponentStatus.WARNING:
                warnings.append(f"{name}: {info.message}")
                recommendations.append(f"Verificar {name}: {info.message}")
            elif info.status == ComponentStatus.DEGRADED:
                warnings.append(f"{name}: Operando em modo degradado")
                recommendations.append(f"Otimizar performance de {name}")

        # Recomendações gerais
        if len(critical_issues) > 2:
            recommendations.append(
                "Sistema com múltiplos problemas críticos - considerar restart completo"
            )

        if len(warnings) > 3:
            recommendations.append(
                "Múltiplos warnings detectados - revisar configurações"
            )

        return critical_issues, warnings, recommendations

    def get_health_summary(self) -> Dict[str, Any]:
        """Retorna resumo rápido da saúde do sistema."""
        if not self.check_history:
            return {
                "status": "no_checks_performed",
                "message": "Nenhuma verificação realizada",
            }

        latest_report = self.check_history[-1]

        return {
            "overall_status": latest_report.overall_status.value,
            "readiness_score": latest_report.readiness_score,
            "is_ready_for_trading": latest_report.is_ready_for_trading,
            "critical_issues_count": len(latest_report.critical_issues),
            "warnings_count": len(latest_report.warnings),
            "healthy_components": sum(
                1 for info in latest_report.components.values() if info.is_healthy
            ),
            "total_components": len(latest_report.components),
            "last_check": latest_report.timestamp.isoformat(),
        }

    def print_health_report(self, report: ReadinessReport) -> None:
        """Loga relatório de saúde formatado."""
        logger.info("\n" + "=" * 60)
        logger.info("🏥 QUALIA SYSTEM HEALTH REPORT")
        logger.info("=" * 60)

        # Status geral
        status_emoji = {
            SystemReadiness.READY: "✅",
            SystemReadiness.DEGRADED_READY: "⚠️",
            SystemReadiness.NOT_READY: "❌",
            SystemReadiness.INITIALIZING: "🔄",
            SystemReadiness.CRITICAL_ERROR: "🚨",
        }

        logger.info(
            f"\n📊 Overall Status: {status_emoji.get(report.overall_status, '❓')} {report.overall_status.value.upper()}"
        )
        logger.info(f"📈 Readiness Score: {report.readiness_score:.2%}")
        logger.info(
            f"🚀 Ready for Trading: {'YES' if report.is_ready_for_trading else 'NO'}"
        )

        # Componentes
        logger.info(f"\n🔧 COMPONENT STATUS ({len(report.components)} components)")
        logger.info("-" * 40)

        for name, info in report.components.items():
            status_emoji = {
                ComponentStatus.HEALTHY: "✅",
                ComponentStatus.WARNING: "⚠️",
                ComponentStatus.ERROR: "❌",
                ComponentStatus.NOT_INITIALIZED: "⭕",
                ComponentStatus.DEGRADED: "🟡",
                ComponentStatus.INITIALIZING: "🔄",
                ComponentStatus.OFFLINE: "⚫",
            }

            emoji = status_emoji.get(info.status, "❓")
            logger.info(f"{emoji} {name.ljust(20)} | {info.message}")

        # Issues críticos
        if report.critical_issues:
            logger.warning(f"\n🚨 CRITICAL ISSUES ({len(report.critical_issues)})")
            logger.warning("-" * 40)
            for issue in report.critical_issues:
                logger.warning(f"❌ {issue}")

        # Warnings
        if report.warnings:
            logger.warning(f"\n⚠️  WARNINGS ({len(report.warnings)})")
            logger.warning("-" * 40)
            for warning in report.warnings:
                logger.warning(f"⚠️  {warning}")

        # Recomendações
        if report.recommendations:
            logger.info(f"\n💡 RECOMMENDATIONS ({len(report.recommendations)})")
            logger.info("-" * 40)
            for i, rec in enumerate(report.recommendations, 1):
                logger.info(f"{i}. {rec}")

        logger.info("\n" + "=" * 60)


# Função utilitária para uso direto
async def quick_health_check(system_components: Dict[str, Any]) -> ReadinessReport:
    """Realiza verificação rápida de saúde do sistema."""
    checker = SystemHealthChecker(system_components)
    return await checker.get_readiness_report()


# Função para verificação contínua
async def continuous_health_monitoring(
    system_components: Dict[str, Any],
    interval_seconds: int = 60,
    callback: Optional[callable] = None,
) -> None:
    """Monitora continuamente a saúde do sistema."""
    checker = SystemHealthChecker(system_components)

    while True:
        try:
            report = await checker.get_readiness_report()

            if callback:
                await callback(report)

            # Log critical issues
            if report.critical_issues:
                logger.error(
                    f"Critical health issues detected: {report.critical_issues}"
                )

            await asyncio.sleep(interval_seconds)

        except Exception as e:
            logger.error(f"Error in continuous health monitoring: {e}")
            await asyncio.sleep(interval_seconds)
