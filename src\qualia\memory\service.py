"""High level async interface for memory operations."""

from __future__ import annotations

from typing import Any, Dict, Optional, Sequence

import asyncio

import time

import numpy as np
from ..utils.logger import get_logger

from .intent_memory import IntentMemory

from .holographic_memory import HolographicMemory

from .quantum_pattern_memory import Quantum<PERSON>atternMemory
from ..config.settings import qpm_memory_file

from .qpm_loader import get_qpm_instance

from ..risk.manager import create_risk_manager
from .event_bus import MemoryEvicted, MemoryWritten, MemoryReset, SimpleEventBus
from ..event_bus import AsyncEventBus
from ..market.event_bus import MarketPatternRecorded

from typing import TYPE_CHECKING

if TYPE_CHECKING:  # pragma: no cover - for type hints
    from qualia.market.event_bus import MarketPatternDetected
from .monitoring import MemoryMonitor
from ..config.feature_flags import (
    MEM_CACHE_ENABLED,
    MEM_DECAY_FACTOR,
    INTENT_MEMORY_ENABLED,
)
from ..common_types import QuantumSignaturePacket
from datadog import DogStatsd

try:
    from opentelemetry import trace
except ImportError:  # pragma: no cover - optional dependency
    trace = None

logger = get_logger(__name__)


class MemoryService:
    """Asynchronous service built on top of QUALIA memory modules."""

    def __init__(
        self,
        *,
        holographic: Optional[HolographicMemory] = None,
        qpm: Optional[QuantumPatternMemory] = None,
        event_bus: Optional[SimpleEventBus] = None,
        statsd_client: Optional[DogStatsd] = None,
        disk_path: str = "/",
        monitor: Optional[MemoryMonitor] = None,
        intent_memory: Optional[IntentMemory] = None,
        enable_intent_memory: bool = INTENT_MEMORY_ENABLED,
        async_persist: bool = False,
    ) -> None:
        """Create a new memory service.

        Parameters
        ----------
        holographic : HolographicMemory, optional
            Backend used for fast approximate retrieval. A new instance is
            created when ``None`` is provided.
        qpm : QuantumPatternMemory, optional
            Persistent storage backend. A default instance is created when
            ``None`` is provided.
        event_bus : SimpleEventBus, optional
            Bus for publishing memory events.
        statsd_client : DogStatsd, optional
            StatsD client used for metrics emission.
        disk_path : str, default '/'
            Filesystem path used by ``monitor`` to check disk usage.
        monitor : MemoryMonitor, optional
            Monitor responsible for latency and item count reporting.
        intent_memory : IntentMemory, optional
            Instância utilizada para armazenar tokens de intenção.
        enable_intent_memory : bool, optional
            Quando ``True`` ativa ``intent_memory`` automaticamente caso
            nenhuma instância seja fornecida.
        async_persist : bool, default=False
            Quando ``True`` o ``QuantumPatternMemory`` é gravado em uma
            *task* assíncrona, evitando bloquear o loop de eventos.

        Notes
        -----
        When ``event_bus`` is supplied, the service automatically subscribes to
        the ``memory.reset`` event so that external components can trigger a
        full memory purge.
        """
        self.holographic = holographic or HolographicMemory(half_life=MEM_DECAY_FACTOR)
        default_rm = create_risk_manager(
            initial_capital=1000.0, risk_profile="conservative"
        )

        self.qpm = qpm or get_qpm_instance(
            {
                "enable_warmstart": False,
                "event_bus": event_bus,
                "risk_manager": default_rm,
                "persistence_path": qpm_memory_file,
                "auto_persist": True,
            }
        )
        self.event_bus = event_bus
        if isinstance(self.event_bus, AsyncEventBus):
            self.qpm.subscribe_to_async_event_bus(self.event_bus)
        self.statsd = statsd_client
        self.monitor = monitor or MemoryMonitor(
            statsd_client, event_bus=event_bus, disk_path=disk_path
        )
        self.intent_memory = None
        if enable_intent_memory:
            self.intent_memory = intent_memory or IntentMemory()
        self.async_persist = async_persist

        if self.event_bus:
            self.event_bus.subscribe_async("memory.reset", self._on_reset)
            self.event_bus.subscribe("market.pattern_detected", self._on_pattern)

    async def _on_reset(self, _payload: Any) -> None:
        self.clear(publish_event=False)

    def _on_pattern(self, payload: Any) -> None:
        from qualia.market.event_bus import MarketPatternDetected

        if isinstance(payload, MarketPatternDetected):
            vector = payload.vector
            metadata = payload.metadata or {}
        elif isinstance(payload, dict):
            vector = payload.get("vector")
            metadata = payload.get("metadata", {})
        else:
            vector = getattr(payload, "vector", None)
            metadata = getattr(payload, "metadata", {}) or {}
        if vector is None:
            return
        packet = QuantumSignaturePacket(vector=list(vector), metrics={})
        success = self.qpm.store_pattern(
            packet,
            metadata.get("market_snapshot", {}),
            metadata.get("outcome", {}),
            metadata.get("decision_context"),
        )
        if success and self.event_bus:
            self.event_bus.publish(
                "market.pattern_recorded",
                MarketPatternRecorded(pattern_id=packet.id),
            )

    def clear(self, *, publish_event: bool = True) -> None:
        """Remove all entries from the underlying memories.

        Parameters
        ----------
        publish_event : bool, default True
            Whether to emit a ``memory.reset`` event after clearing.

        Notes
        -----
        Used when reacting to a ``memory.reset`` signal coming from the event
        bus.
        """

        self.holographic._data.clear()
        self.qpm.memory.clear()
        if publish_event and self.event_bus:
            self.event_bus.publish("memory.reset", MemoryReset())

    def _schedule_persist(
        self,
        packet: QuantumSignaturePacket,
        market_snapshot: Optional[Dict[str, Any]],
        outcome: Optional[Dict[str, Any]],
        decision_context: Optional[Dict[str, Any]],
        extra_metadata: Optional[Dict[str, Any]],
    ) -> asyncio.Task:
        """Create a persistence task and log any exception.

        Parameters
        ----------
        packet : QuantumSignaturePacket
            Packet to store.
        market_snapshot : dict[str, Any] or None
            Snapshot of market state.
        outcome : dict[str, Any] or None
            Observed outcome for reinforcement learning.
        decision_context : dict[str, Any] or None
            Additional context about the decision environment.
        extra_metadata : dict[str, Any] or None
            Arbitrary fields associated with the pattern.
        """

        task = asyncio.create_task(
            self._persist(
                packet,
                market_snapshot,
                outcome,
                decision_context,
                extra_metadata,
            )
        )

        def _log_exception(t: asyncio.Task) -> None:
            exc = t.exception()
            if exc is not None:
                logger.error("Erro em _persist: %s", exc, exc_info=True)

        task.add_done_callback(_log_exception)
        return task

    async def store(
        self,
        packet: QuantumSignaturePacket,
        market_snapshot: Optional[Dict[str, Any]] = None,
        outcome: Optional[Dict[str, Any]] = None,
        decision_context: Optional[Dict[str, Any]] = None,
        extra_metadata: Optional[Dict[str, Any]] = None,
    ) -> bool:
        """Persist ``packet`` to memory.

        Parameters
        ----------
        packet : QuantumSignaturePacket
            Packet containing the feature vector to be stored.
        market_snapshot : dict[str, Any], optional
            Snapshot of the market state at decision time.
        outcome : dict[str, Any], optional
            Observed result used for reinforcement learning.
        decision_context : dict[str, Any], optional
            Additional metadata describing the decision environment.
        extra_metadata : dict[str, Any], optional
            Arbitrary fields associated with the pattern.

        Returns
        -------
        bool
            ``True`` when the packet was successfully stored.

        Notes
        -----
        When caching is enabled, the packet vector is also inserted into
        :class:`HolographicMemory` to speed up similarity queries. A
        ``memory.written`` event is emitted via ``event_bus`` when available.
        """
        if self.async_persist:
            self._schedule_persist(
                packet,
                market_snapshot,
                outcome,
                decision_context,
                extra_metadata,
            )
            return True

        return await self._persist(
            packet,
            market_snapshot,
            outcome,
            decision_context,
            extra_metadata,
        )

    async def _persist(
        self,
        packet: QuantumSignaturePacket,
        market_snapshot: Optional[Dict[str, Any]],
        outcome: Optional[Dict[str, Any]],
        decision_context: Optional[Dict[str, Any]],
        extra_metadata: Optional[Dict[str, Any]],
    ) -> bool:
        if not packet.vector:
            logger.warning("QuantumSignaturePacket vazio; descartando armazenamento.")
            return False

        vector_size = len(packet.vector) if hasattr(packet, "vector") else 0
        start_time = time.perf_counter()
        if trace:
            tracer = trace.get_tracer(__name__)
            span_cm = tracer.start_as_current_span(
                "memory.store",
                attributes={"vector_size": vector_size, "origin_module": __name__},
            )
        else:
            from contextlib import nullcontext

            span_cm = nullcontext()

        loop = asyncio.get_running_loop()
        with span_cm:
            success = await loop.run_in_executor(
                None,
                lambda: self.qpm.store_pattern(
                    packet,
                    market_snapshot or {},
                    outcome or {},
                    decision_context,
                    extra_metadata=extra_metadata,
                ),
            )
            if success and MEM_CACHE_ENABLED:
                self.holographic.store(
                    np.asarray(packet.vector, dtype=float),
                    {"pattern_id": packet.id},
                )
            if success and self.event_bus:
                await self.event_bus.publish_async(
                    "memory.written", MemoryWritten(pattern_id=packet.id)
                )
                await self.event_bus.publish_async(
                    "market.pattern_recorded",
                    MarketPatternRecorded(pattern_id=packet.id),
                )

        latency_ms = (time.perf_counter() - start_time) * 1000.0
        self.monitor.record_write_latency(latency_ms)
        total_items = sum(len(p) for p in self.qpm.memory.values())
        self.monitor.report_items_count(total_items)

        return success

    def retrieve(self, pattern_id: str) -> Optional[Dict[str, Any]]:
        """Return stored pattern by ``pattern_id``.

        Parameters
        ----------
        pattern_id : str
            Identifier generated when the pattern was stored.

        Returns
        -------
        dict[str, Any] or None
            Stored pattern information or ``None`` when the id is not present.
        """
        result: Optional[Dict[str, Any]] = None
        vector_size = 0
        for patterns in self.qpm.memory.values():
            for pattern in patterns:
                if pattern.get("id") == pattern_id:
                    result = pattern
                    vector_size = pattern.get("vector_dim", 0)
                    qsp = pattern.get("quantum_signature_packet", {})
                    vec = qsp.get("vector")
                    if vec is not None:
                        vector_size = len(vec)
                    break
        if trace:
            tracer = trace.get_tracer(__name__)
            span_cm = tracer.start_as_current_span(
                "memory.retrieve",
                attributes={"vector_size": vector_size, "origin_module": __name__},
            )
        else:
            from contextlib import nullcontext

            span_cm = nullcontext()

        with span_cm:
            return result

    async def query_similarity(
        self, packet: QuantumSignaturePacket, top_n: int = 5
    ) -> Sequence[Dict[str, Any]]:
        """Return patterns similar to ``packet``.

        Parameters
        ----------
        packet : QuantumSignaturePacket
            Query vector used to search the memory.
        top_n : int, default 5
            Maximum number of results to return.

        Returns
        -------
        Sequence[dict[str, Any]]
            Ordered collection of similar patterns.
        """

        if isinstance(self.event_bus, AsyncEventBus):
            return await self.qpm.query_via_async_event_bus(
                self.event_bus, packet, top_n=top_n
            )
        return self.qpm.retrieve_similar_patterns(packet, top_n=top_n)

    def store_intent_sequence(self, tokens: Sequence[str]) -> None:
        """Persist a sequence of tokens into :class:`IntentMemory`."""

        if self.intent_memory is not None:
            self.intent_memory.store_sequence(list(tokens))

    def predict_next_intent(self, tokens: Sequence[str]) -> str:
        """Predict the next token for ``tokens`` using :class:`IntentMemory`."""

        if self.intent_memory is None:
            return ""
        return self.intent_memory.predict_next(list(tokens))

    async def evict(self, pattern_id: str) -> bool:
        """Remove a pattern from memory.

        Parameters
        ----------
        pattern_id : str
            Identifier of the pattern to remove.

        Returns
        -------
        bool
            ``True`` if the pattern existed and was removed.

        Notes
        -----
        The pattern is also removed from :class:`HolographicMemory` and a
        ``memory.evicted`` event is emitted.
        """

        removed = self.qpm.remove_pattern(pattern_id)
        if removed:
            self.holographic._data = [
                item
                for item in self.holographic._data
                if item.metadata.get("pattern_id") != pattern_id
            ]
            if self.event_bus:
                await self.event_bus.publish_async(
                    "memory.evicted", MemoryEvicted(pattern_id=pattern_id)
                )
            self.monitor.increment_evictions("manual")
        return removed


__all__ = ["MemoryService"]
