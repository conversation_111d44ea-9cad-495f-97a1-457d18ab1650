from __future__ import annotations

from flask import Flask

from .utils.state import QualiaState


class QualiaStateExtension:
    """Flask extension providing a :class:`QualiaState` instance."""

    def __init__(self, app: Flask | None = None) -> None:
        self.state = QualiaState()
        if app is not None:
            self.init_app(app)

    def init_app(self, app: Flask) -> None:
        """Attach the extension to *app* and register teardown."""
        app.extensions["qualia_state"] = self.state
        # app.teardown_appcontext(self.teardown)

    # def teardown(self, _: Exception | None) -> None:
    #     """Reset state when the application context tears down."""
    #     self.state.clear()
