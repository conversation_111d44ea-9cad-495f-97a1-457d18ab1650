#!/usr/bin/env python3
"""
HOTFIX DE EMERGÊNCIA - Correção Imediata de Thresholds
Aplica correções críticas de escala sem calibração completa
"""

import asyncio
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent.parent / 'src'))

from qualia.binance_system import QualiaBinanceCorrectedSystem
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

async def emergency_fix():
    """Aplica correções de emergência nos thresholds"""

    print("QUALIA - HOTFIX DE EMERGÊNCIA")
    print("=" * 50)
    print("Aplicando correções críticas de escala...")

    try:
        # Inici<PERSON>zar sistema (já inicializa no construtor)
        trading_system = QualiaBinanceCorrectedSystem()
        
        # Mostrar thresholds atuais
        print("\nTHRESHOLDS ATUAIS:")
        current = trading_system.quantum_thresholds
        for metric, value in current.items():
            print(f"   {metric}: {value:.6f}")

        # Aplicar correções de emergência
        print("\nAPLICANDO CORREÇÕES DE EMERGÊNCIA:")
        
        emergency_fixes = {
            # CRÍTICO: Momentum estava 100x maior que deveria
            'momentum_min': max(current['momentum_min'] * 0.01, 0.0005),
            
            # Reduções significativas para permitir aprovações
            'consciousness': max(current['consciousness'] - 0.08, 0.45),
            'coherence': max(current['coherence'] - 0.08, 0.35),
            'confidence': max(current['confidence'] - 0.06, 0.40),
            
            # Ajustes moderados
            'volume_surge_min': max(current['volume_surge_min'] * 0.8, 0.8),
            'spectral_phi_alignment_min': max(current.get('spectral_phi_alignment_min', 0.5) * 0.8, 0.3),
            'golden_symmetry_min': max(current.get('golden_symmetry_min', 0.5) * 0.8, 0.3)
        }
        
        # Aplicar e mostrar mudanças
        for metric, new_value in emergency_fixes.items():
            old_value = current.get(metric, 0)
            change_pct = ((new_value - old_value) / old_value * 100) if old_value > 0 else 0
            
            print(f"   {metric}: {old_value:.6f} -> {new_value:.6f} ({change_pct:+.1f}%)")
            
            # Atualizar no sistema
            trading_system.quantum_thresholds[metric] = new_value
            
            # Atualizar no adaptive_manager se disponível
            if hasattr(trading_system, 'adaptive_manager') and trading_system.adaptive_manager:
                if hasattr(trading_system.adaptive_manager.current_thresholds, metric):
                    setattr(trading_system.adaptive_manager.current_thresholds, metric, new_value)
        
        print("\nCORREÇÕES APLICADAS COM SUCESSO!")

        # Mostrar thresholds finais
        print("\nTHRESHOLDS CORRIGIDOS:")
        for metric, value in trading_system.quantum_thresholds.items():
            print(f"   {metric}: {value:.6f}")

        print("\nPRÓXIMOS PASSOS:")
        print("1. Executar sistema e monitorar taxa de aprovação")
        print("2. Deve atingir 15-20% de aprovação agora")
        print("3. Se ainda estiver baixo, executar calibração completa")
        print("4. Monitorar qualidade dos sinais gerados")

        # Perguntar se deve salvar
        response = input("\nSalvar correções permanentemente? (s/N): ").lower()
        if response in ['s', 'sim', 'y', 'yes']:
            # Aqui poderia salvar no arquivo de configuração
            print("Correções salvas (aplicadas em runtime)")
        else:
            print("Correções aplicadas apenas em runtime")
        
    except Exception as e:
        logger.error(f"Erro durante hotfix: {e}")
        raise

def main():
    """Wrapper principal"""
    try:
        asyncio.run(emergency_fix())
    except KeyboardInterrupt:
        print("\nHotfix interrompido pelo usuário")
    except Exception as e:
        print(f"\nErro fatal: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
