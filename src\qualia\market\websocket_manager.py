from __future__ import annotations

from typing import Awaitable, Callable, TYPE_CHECKING

if TYPE_CHECKING:
    from .base_integration import CryptoDataFetcher


class WebSocketManager:
    """Handle websocket connection lifecycle."""

    def __init__(self, fetcher: "CryptoDataFetcher") -> None:
        self._fetcher = fetcher

    async def start_websocket(
        self,
        symbol: str,
        on_message: Callable[..., Awaitable[None]] | None = None,
        private: bool = False,
    ) -> None:
        await self._fetcher.start_websocket(symbol, on_message, private)

    async def close_websocket(self) -> None:
        await self._fetcher.close_websocket()
