"""
Metacognition System Module

Provides metacognitive capabilities for self-reflection, strategy adaptation,
and performance optimization.
"""

from .system import MetacognitionSystem
from .metacognition_trading import (
    QUALIAMetacognitionTrading,
    MetacognitiveContext,
)
from .coherence_guard import CoherenceGuard
from .layer import Quantum<PERSON>etacognitionLayer, MetacognitionConfig
from .analyzer import MetacogClusterAnalyzer, ClusterInsights
from .service import get_focus, push_metric, snapshot, get_insights

__all__ = [
    "QuantumMetacognitionLayer",
    "MetacognitionConfig",
    "QUALIAMetacognitionTrading",
    "MetacognitiveContext",
    "CoherenceGuard",
    "MetacogClusterAnalyzer",
    "ClusterInsights",
    "get_insights",
    "get_focus",
    "push_metric",
    "snapshot",
]
