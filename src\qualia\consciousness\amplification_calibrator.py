"""
QUALIA Amplification Calibrator

Sistema adaptativo de calibração para parâmetros de amplificação
dos sinais holográficos. Ajusta dinamicamente baseado em:
- Performance histórica
- Volatilidade do mercado
- Força dos padrões detectados
- Feedback de execução
"""

from __future__ import annotations

import numpy as np
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from collections import deque
import time

from ..utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class AmplificationMetrics:
    """Métricas para calibração de amplificação."""
    timestamp: float
    price_amplification: float
    news_amplification: float
    pattern_detection_rate: float
    signal_quality: float
    execution_success_rate: float
    false_positive_rate: float


class AmplificationCalibrator:
    """
    Calibrador adaptativo para parâmetros de amplificação.
    
    Ajusta dinamicamente os multiplicadores de amplitude para:
    - Eventos de preço
    - Eventos de notícias
    - Padrões holográficos
    """
    
    def __init__(
        self,
        initial_price_amp: float = 5.0,
        initial_news_amp: float = 4.0,
        learning_rate: float = 0.1,
        history_size: int = 100
    ):
        # Parâmetros atuais
        self.price_amplification = initial_price_amp
        self.news_amplification = initial_news_amp
        self.pattern_threshold = 0.3
        
        # Limites de segurança
        self.min_amplification = 1.0
        self.max_amplification = 10.0
        self.min_pattern_threshold = 0.2
        self.max_pattern_threshold = 0.8
        
        # Configuração de aprendizado
        self.learning_rate = learning_rate
        self.history = deque(maxlen=history_size)
        
        # Métricas de performance
        self.total_patterns = 0
        self.successful_signals = 0
        self.false_positives = 0
        self.last_calibration = time.time()
        
        logger.info(
            f"AmplificationCalibrator inicializado: "
            f"price_amp={initial_price_amp}, news_amp={initial_news_amp}"
        )
    
    def record_pattern_detection(
        self,
        patterns_detected: int,
        signals_generated: int,
        signals_executed: int,
        execution_results: List[bool]
    ):
        """Registra resultados de detecção de padrões."""
        
        self.total_patterns += patterns_detected
        
        # Calcula métricas
        detection_rate = signals_generated / max(patterns_detected, 1)
        execution_rate = signals_executed / max(signals_generated, 1)
        success_rate = sum(execution_results) / max(len(execution_results), 1)
        
        # Detecta falsos positivos
        false_positives = sum(1 for r in execution_results if not r)
        self.false_positives += false_positives
        
        # Registra métricas
        metrics = AmplificationMetrics(
            timestamp=time.time(),
            price_amplification=self.price_amplification,
            news_amplification=self.news_amplification,
            pattern_detection_rate=detection_rate,
            signal_quality=detection_rate * execution_rate,
            execution_success_rate=success_rate,
            false_positive_rate=false_positives / max(len(execution_results), 1)
        )
        
        self.history.append(metrics)
        
        # Calibra se necessário
        if self._should_calibrate():
            self._calibrate_parameters()
    
    def _should_calibrate(self) -> bool:
        """Determina se deve recalibrar."""
        
        # Calibra a cada 50 registros ou 5 minutos
        if len(self.history) < 10:
            return False
            
        time_since_last = time.time() - self.last_calibration
        return len(self.history) % 50 == 0 or time_since_last > 300
    
    def _calibrate_parameters(self):
        """Calibra parâmetros baseado no histórico."""
        
        if len(self.history) < 5:
            return
            
        recent_metrics = list(self.history)[-20:]  # Últimas 20 métricas
        
        # Calcula médias
        avg_detection_rate = np.mean([m.pattern_detection_rate for m in recent_metrics])
        avg_signal_quality = np.mean([m.signal_quality for m in recent_metrics])
        avg_success_rate = np.mean([m.execution_success_rate for m in recent_metrics])
        avg_false_positive = np.mean([m.false_positive_rate for m in recent_metrics])
        
        logger.info(
            f"📊 Métricas de calibração: detection={avg_detection_rate:.2f}, "
            f"quality={avg_signal_quality:.2f}, success={avg_success_rate:.2f}, "
            f"false_pos={avg_false_positive:.2f}"
        )
        
        # Ajusta amplificação de preço
        if avg_detection_rate < 0.1:  # Poucos padrões detectados
            self.price_amplification *= (1 + self.learning_rate)
        elif avg_detection_rate > 0.5:  # Muitos padrões (possível ruído)
            self.price_amplification *= (1 - self.learning_rate * 0.5)
        
        # Ajusta amplificação de notícias
        if avg_signal_quality < 0.3:
            self.news_amplification *= (1 + self.learning_rate * 0.5)
        elif avg_false_positive > 0.3:
            self.news_amplification *= (1 - self.learning_rate)
        
        # Ajusta threshold de padrões
        if avg_success_rate < 0.4:
            self.pattern_threshold *= (1 + self.learning_rate * 0.3)
        elif avg_false_positive > 0.2:
            self.pattern_threshold *= (1 - self.learning_rate * 0.2)
        
        # Aplica limites de segurança
        self.price_amplification = np.clip(
            self.price_amplification,
            self.min_amplification,
            self.max_amplification
        )
        self.news_amplification = np.clip(
            self.news_amplification,
            self.min_amplification,
            self.max_amplification
        )
        self.pattern_threshold = np.clip(
            self.pattern_threshold,
            self.min_pattern_threshold,
            self.max_pattern_threshold
        )
        
        self.last_calibration = time.time()
        
        logger.info(
            f"✅ Parâmetros calibrados: price_amp={self.price_amplification:.2f}, "
            f"news_amp={self.news_amplification:.2f}, threshold={self.pattern_threshold:.2f}"
        )
    
    def get_calibrated_amplification(self, event_type: str) -> float:
        """Retorna amplificação calibrada para tipo de evento."""
        
        if event_type == "price":
            return self.price_amplification
        elif event_type == "news":
            return self.news_amplification
        else:
            return (self.price_amplification + self.news_amplification) / 2
    
    def get_pattern_threshold(self) -> float:
        """Retorna threshold calibrado para detecção de padrões."""
        return self.pattern_threshold
    
    def get_calibration_report(self) -> Dict[str, Any]:
        """Gera relatório de calibração."""
        
        if not self.history:
            return {
                "status": "no_data",
                "price_amplification": self.price_amplification,
                "news_amplification": self.news_amplification,
                "pattern_threshold": self.pattern_threshold
            }
        
        recent_metrics = list(self.history)[-20:]
        
        return {
            "status": "calibrated",
            "price_amplification": self.price_amplification,
            "news_amplification": self.news_amplification,
            "pattern_threshold": self.pattern_threshold,
            "metrics": {
                "avg_detection_rate": np.mean([m.pattern_detection_rate for m in recent_metrics]),
                "avg_signal_quality": np.mean([m.signal_quality for m in recent_metrics]),
                "avg_success_rate": np.mean([m.execution_success_rate for m in recent_metrics]),
                "avg_false_positive": np.mean([m.false_positive_rate for m in recent_metrics])
            },
            "total_patterns": self.total_patterns,
            "successful_signals": self.successful_signals,
            "false_positives": self.false_positives,
            "last_calibration": self.last_calibration,
            "history_size": len(self.history)
        }
    
    def reset_calibration(self):
        """Reseta calibração para valores iniciais."""
        
        self.price_amplification = 5.0
        self.news_amplification = 4.0
        self.pattern_threshold = 0.3
        self.history.clear()
        self.total_patterns = 0
        self.successful_signals = 0
        self.false_positives = 0
        self.last_calibration = time.time()
        
        logger.info("Calibração resetada para valores iniciais") 