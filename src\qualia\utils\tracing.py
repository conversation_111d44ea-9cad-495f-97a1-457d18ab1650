"""Utilities for configuring OpenTelemetry tracing.

This module functions even when the OpenTelemetry packages are not
installed. In that scenario a lightweight tracer is provided and a
single warning is logged. Install ``opentelemetry-api`` e
``opentelemetry-sdk`` (e ``opentelemetry-exporter-otlp`` para exportação
OTLP) para habilitar o tracing completo.
"""

from __future__ import annotations

from typing import Any, Optional
import logging
import warnings
from threading import Lock
import os

try:  # pragma: no cover - optional dependency
    from opentelemetry import trace
    from opentelemetry.sdk.resources import Resource
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import (
        BatchSpanProcessor,
        ConsoleSpanExporter,
    )
    from opentelemetry.exporter.otlp.proto.http.trace_exporter import (
        OTLPSpanExporter,
    )
except Exception:  # pragma: no cover - optional dependency
    trace = None  # type: ignore
    OTLPSpanExporter = None  # type: ignore


_INITIALIZED = False
_missing_trace_warned = False
_missing_trace_lock = Lock()


def configure_tracing(
    service_name: str = "qualia", exporter: Optional[str] = None
) -> bool:
    """Configure OpenTelemetry tracing.

    Parameters
    ----------
    service_name
        Value for the ``service.name`` resource attribute.
    exporter
        Name of the exporter to use. ``"console"`` (padrão) ou ``"otlp"``.
        When ``None``, the value of ``QUALIA_TRACING_EXPORTER`` is used.

    Returns
    -------
    bool
        ``True`` if tracing was configured, ``False`` otherwise.
    """
    global _INITIALIZED
    if exporter is None:
        exporter = os.getenv("QUALIA_TRACING_EXPORTER", "console")
    if trace is None:
        warnings.warn(
            "OpenTelemetry packages not installed; tracing desativado.",
            RuntimeWarning,
        )
        return False
    if _INITIALIZED:
        return False

    resource = Resource.create({"service.name": service_name})
    provider = TracerProvider(resource=resource)
    if exporter.lower() == "otlp":
        if OTLPSpanExporter is None:
            warnings.warn(
                "Exportador OTLP solicitado, mas 'opentelemetry-exporter-otlp' ausente.",
                RuntimeWarning,
            )
            return False
        exporter_inst = OTLPSpanExporter()
    else:
        exporter_inst = ConsoleSpanExporter()
    span_processor = BatchSpanProcessor(exporter_inst)
    provider.add_span_processor(span_processor)
    trace.set_tracer_provider(provider)

    _INITIALIZED = True
    return True


def get_tracer(name: Optional[str] = None) -> Any:
    """Return an OpenTelemetry tracer or a no-op tracer.

    When the OpenTelemetry packages are missing, a warning is emitted and
    a lightweight tracer is returned that simply provides a ``nullcontext``
    for spans. This preserves compatibility with ``with`` blocks even
    quando o tracing n\u00e3o est\u00e1 habilitado.
    """
    if trace:
        return trace.get_tracer(name or __name__)

    global _missing_trace_warned
    with _missing_trace_lock:
        if not _missing_trace_warned:
            warnings.warn(
                "OpenTelemetry n\u00e3o instalado; spans ser\u00e3o ignorados.",
                RuntimeWarning,
            )
            _missing_trace_warned = True

    from contextlib import nullcontext

    class _NullTracer:
        def start_as_current_span(
            self, *_args: Any, **_kwargs: Any
        ):  # pragma: no cover - simple wrapper
            return nullcontext()

    return _NullTracer()


class TraceIdLogFilter(logging.Filter):
    """Inject ``trace_id`` into log records when tracing is active."""

    def filter(
        self, record: logging.LogRecord
    ) -> bool:  # pragma: no cover - simple passthrough
        if is_tracing_enabled():
            span = trace.get_current_span()
            if span:
                span_ctx = span.get_span_context()
                if span_ctx and span_ctx.trace_id:
                    trace_id = format(span_ctx.trace_id, "032x")
                    record.msg = f"[trace_id={trace_id}] {record.getMessage()}"
        return True


def is_tracing_enabled() -> bool:
    """Return ``True`` when OpenTelemetry tracing is active."""

    return trace is not None and _INITIALIZED


def instrument_logger(logger: logging.Logger) -> None:
    """Attach ``TraceIdLogFilter`` to ``logger`` if tracing is enabled."""

    if not is_tracing_enabled():
        return
    for filt in logger.filters:
        if isinstance(filt, TraceIdLogFilter):
            return
    logger.addFilter(TraceIdLogFilter())
