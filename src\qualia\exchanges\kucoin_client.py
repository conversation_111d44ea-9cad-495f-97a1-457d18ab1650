"""KuCoin exchange client wrapper."""

from __future__ import annotations

from typing import Any, Dict, Optional

from .ccxt_client import CCXTExchangeClient

# Removida importação circular
# from ..market.kucoin_integration import KucoinIntegration


class KuCoinClient(CCXTExchangeClient):
    """Concrete client for the KuCoin exchange."""

    def __init__(self, config: Dict[str, Any]) -> None:
        # Garantir que o exchange_id esteja definido
        config["exchange_id"] = "kucoin"
        super().__init__(config)
        
        # A integração será importada e configurada quando necessária
        self._integration = None

    @property
    def integration(self):
        """Lazy loading da integração para evitar importação circular."""
        if self._integration is None:
            # Importação tardia para evitar ciclo
            from ..market.kucoin_integration import KucoinIntegration
            
            # Configurar a integração com as mesmas credenciais do cliente
            self._integration = KucoinIntegration(
                api_key=self.api_key, 
                api_secret=self.api_secret,
                password=self.password,
                config=self.config
            )
        return self._integration

    async def get_order_book(
        self, symbol: str, limit: int = 100
    ) -> Optional[Dict[str, Any]]:
        """Delegates to :class:`CCXTExchangeClient`."""

        return await super().get_order_book(symbol, limit)
