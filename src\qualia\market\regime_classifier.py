"""Market regime classification utilities."""

from __future__ import annotations

from typing import Optional

from ..utils.logger import get_logger

logger = get_logger(__name__)


def determine_market_regime(
    volatility: float,
    threshold_low: float,
    threshold_high: float,
    complexity_level: Optional[str] = None,
) -> str:
    """Classify the current market regime.

    Parameters
    ----------
    volatility:
        Current implied volatility metric.
    threshold_low:
        Threshold below which the regime is considered ``"calm"``.
    threshold_high:
        Threshold above which the regime is considered ``"volatile"``.
    complexity_level:
        Optional complexity level from the ``AdaptiveConsciousnessEvolution``.

    Returns
    -------
    str
        ``"calm"``, ``"normal"`` or ``"volatile"``.
    """
    try:
        if complexity_level:
            if complexity_level == "calm":
                return "calm"
            if complexity_level == "volatile":
                return "volatile"
            return "normal"

        if volatility < threshold_low:
            return "calm"
        if volatility > threshold_high:
            return "volatile"
        return "normal"
    except Exception as exc:  # pragma: no cover - unexpected errors
        logger.error("Erro ao determinar regime de mercado: %s", exc)
        return "normal"


def _get_regime_multiplier(
    market_regime: str,
    calm_multiplier: float,
    normal_multiplier: float,
    volatile_multiplier: float,
) -> float:
    """Return the multiplier associated with a market regime."""
    multipliers = {
        "calm": calm_multiplier,
        "normal": normal_multiplier,
        "volatile": volatile_multiplier,
    }
    return multipliers.get(market_regime, 1.0)
