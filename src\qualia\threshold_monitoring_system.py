#!/usr/bin/env python3
"""
Sistema de Monitoramento e Validação de Thresholds QUALIA

Responsabilidades:
- KPIs em tempo real (approval rate, false positives/negatives)
- Detecção de anomalias nos thresholds
- Sistema de rollback automático
- Alertas para intervenção manual

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import time
import json
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, asdict
from enum import Enum

from .utils.logger import get_logger

logger = get_logger(__name__)

class AlertLevel(Enum):
    """Níveis de alerta do sistema"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"

class MonitoringMetric(Enum):
    """Métricas monitoradas"""
    APPROVAL_RATE = "approval_rate"
    FALSE_POSITIVE_RATE = "false_positive_rate"
    FALSE_NEGATIVE_RATE = "false_negative_rate"
    SIGNAL_QUALITY = "signal_quality"
    THRESHOLD_STABILITY = "threshold_stability"
    SYSTEM_PERFORMANCE = "system_performance"

@dataclass
class KPISnapshot:
    """Snapshot de KPIs em um momento específico"""
    timestamp: float
    approval_rate: float
    false_positive_rate: float
    false_negative_rate: float
    signal_quality_score: float
    total_signals: int
    profitable_signals: int
    threshold_values: Dict[str, float]
    regime: str = "normal"

@dataclass
class Alert:
    """Alerta do sistema de monitoramento"""
    timestamp: float
    level: AlertLevel
    metric: MonitoringMetric
    message: str
    current_value: float
    threshold_value: float
    suggested_action: str
    auto_action_taken: bool = False

class ThresholdMonitoringSystem:
    """
    Sistema de monitoramento em tempo real dos thresholds QUALIA
    
    Funcionalidades:
    - Coleta KPIs continuamente
    - Detecta anomalias e degradação de performance
    - Executa rollback automático quando necessário
    - Gera alertas para intervenção manual
    """
    
    def __init__(self, config_manager, trading_system=None):
        self.config_manager = config_manager
        self.trading_system = trading_system
        
        # Histórico de KPIs
        self.kpi_history: List[KPISnapshot] = []
        self.alerts: List[Alert] = []
        
        # Configurações de monitoramento
        self.config = self._load_monitoring_config()
        
        # Estado do sistema
        self.monitoring_active = False
        self.last_rollback_time = 0
        self.rollback_count = 0
        
        # Thresholds de backup para rollback
        self.backup_thresholds: Optional[Dict[str, float]] = None
        self.backup_timestamp: Optional[float] = None
        
        # Métricas em tempo real
        self.current_kpis = {
            'approval_rate': 0.0,
            'false_positive_rate': 0.0,
            'false_negative_rate': 0.0,
            'signal_quality_score': 0.0
        }
        
        logger.info("Sistema de Monitoramento de Thresholds inicializado")

    def _load_monitoring_config(self) -> Dict[str, Any]:
        """Carrega configurações de monitoramento"""
        return {
            # Limites de alerta
            'alert_thresholds': {
                'approval_rate_min': 0.10,      # < 10% = crítico
                'approval_rate_max': 0.30,      # > 30% = warning
                'false_positive_max': 0.15,     # > 15% = warning
                'false_negative_max': 0.25,     # > 25% = warning
                'signal_quality_min': 0.60      # < 60% = warning
            },
            
            # Configurações de rollback automático
            'auto_rollback': {
                'enabled': True,
                'approval_rate_emergency': 0.05,  # < 5% = rollback
                'false_positive_emergency': 0.25, # > 25% = rollback
                'min_time_between_rollbacks': 3600,  # 1 hora
                'max_rollbacks_per_day': 3
            },
            
            # Configurações de coleta
            'collection': {
                'snapshot_interval_seconds': 300,  # 5 minutos
                'history_retention_hours': 168,    # 7 dias
                'alert_retention_hours': 72        # 3 dias
            },
            
            # Configurações de persistência
            'persistence': {
                'kpi_history_file': 'data/monitoring/kpi_history.json',
                'alerts_file': 'data/monitoring/alerts.json',
                'backup_thresholds_file': 'data/monitoring/backup_thresholds.json'
            }
        }

    async def start_monitoring(self):
        """Inicia o monitoramento contínuo"""
        if self.monitoring_active:
            logger.warning("Monitoramento já está ativo")
            return
        
        self.monitoring_active = True
        logger.info(" Iniciando monitoramento contínuo de thresholds...")
        
        # Criar backup inicial dos thresholds
        await self._create_threshold_backup()
        
        # Loop principal de monitoramento
        while self.monitoring_active:
            try:
                # Coletar KPIs atuais
                snapshot = await self._collect_kpi_snapshot()
                self.kpi_history.append(snapshot)
                
                # Analisar métricas e gerar alertas
                alerts = await self._analyze_metrics(snapshot)
                self.alerts.extend(alerts)
                
                # Verificar se rollback automático é necessário
                if self.config['auto_rollback']['enabled']:
                    await self._check_auto_rollback(snapshot)
                
                # Limpeza de histórico antigo
                await self._cleanup_old_data()
                
                # Persistir dados
                await self._persist_monitoring_data()
                
                # Aguardar próximo ciclo
                await asyncio.sleep(self.config['collection']['snapshot_interval_seconds'])
                
            except Exception as e:
                logger.error(f"Erro no monitoramento: {e}")
                await asyncio.sleep(60)  # Aguardar 1 minuto em caso de erro

    async def stop_monitoring(self):
        """Para o monitoramento"""
        self.monitoring_active = False
        logger.info(" Monitoramento de thresholds parado")

    async def _collect_kpi_snapshot(self) -> KPISnapshot:
        """Coleta snapshot atual dos KPIs"""
        current_time = time.time()
        
        # Obter thresholds atuais
        current_thresholds = {}
        if self.trading_system and hasattr(self.trading_system, 'quantum_thresholds'):
            current_thresholds = self.trading_system.quantum_thresholds.copy()
        
        # Calcular métricas (simulado - seria integrado com sistema real)
        approval_rate = await self._calculate_approval_rate()
        false_positive_rate = await self._calculate_false_positive_rate()
        false_negative_rate = await self._calculate_false_negative_rate()
        signal_quality = await self._calculate_signal_quality()
        
        # Detectar regime atual
        regime = await self._detect_current_regime()
        
        snapshot = KPISnapshot(
            timestamp=current_time,
            approval_rate=approval_rate,
            false_positive_rate=false_positive_rate,
            false_negative_rate=false_negative_rate,
            signal_quality_score=signal_quality,
            total_signals=self._get_recent_signal_count(),
            profitable_signals=self._get_recent_profitable_signals(),
            threshold_values=current_thresholds,
            regime=regime
        )
        
        # Atualizar KPIs atuais
        self.current_kpis.update({
            'approval_rate': approval_rate,
            'false_positive_rate': false_positive_rate,
            'false_negative_rate': false_negative_rate,
            'signal_quality_score': signal_quality
        })
        
        logger.debug(f"KPI Snapshot: aprovação={approval_rate:.1%}, FP={false_positive_rate:.1%}, "
                    f"FN={false_negative_rate:.1%}, qualidade={signal_quality:.1%}")
        
        return snapshot

    async def _analyze_metrics(self, snapshot: KPISnapshot) -> List[Alert]:
        """Analisa métricas e gera alertas quando necessário"""
        alerts = []
        thresholds = self.config['alert_thresholds']
        
        # Verificar taxa de aprovação
        if snapshot.approval_rate < thresholds['approval_rate_min']:
            alerts.append(Alert(
                timestamp=snapshot.timestamp,
                level=AlertLevel.CRITICAL,
                metric=MonitoringMetric.APPROVAL_RATE,
                message=f"Taxa de aprovação crítica: {snapshot.approval_rate:.1%}",
                current_value=snapshot.approval_rate,
                threshold_value=thresholds['approval_rate_min'],
                suggested_action="Relaxar thresholds ou executar recalibração"
            ))
        elif snapshot.approval_rate > thresholds['approval_rate_max']:
            alerts.append(Alert(
                timestamp=snapshot.timestamp,
                level=AlertLevel.WARNING,
                metric=MonitoringMetric.APPROVAL_RATE,
                message=f"Taxa de aprovação alta: {snapshot.approval_rate:.1%}",
                current_value=snapshot.approval_rate,
                threshold_value=thresholds['approval_rate_max'],
                suggested_action="Considerar tornar thresholds mais restritivos"
            ))
        
        # Verificar false positives
        if snapshot.false_positive_rate > thresholds['false_positive_max']:
            alerts.append(Alert(
                timestamp=snapshot.timestamp,
                level=AlertLevel.WARNING,
                metric=MonitoringMetric.FALSE_POSITIVE_RATE,
                message=f"Taxa de falsos positivos alta: {snapshot.false_positive_rate:.1%}",
                current_value=snapshot.false_positive_rate,
                threshold_value=thresholds['false_positive_max'],
                suggested_action="Tornar thresholds mais restritivos"
            ))
        
        # Verificar false negatives
        if snapshot.false_negative_rate > thresholds['false_negative_max']:
            alerts.append(Alert(
                timestamp=snapshot.timestamp,
                level=AlertLevel.WARNING,
                metric=MonitoringMetric.FALSE_NEGATIVE_RATE,
                message=f"Taxa de falsos negativos alta: {snapshot.false_negative_rate:.1%}",
                current_value=snapshot.false_negative_rate,
                threshold_value=thresholds['false_negative_max'],
                suggested_action="Relaxar thresholds para capturar mais oportunidades"
            ))
        
        # Verificar qualidade dos sinais
        if snapshot.signal_quality_score < thresholds['signal_quality_min']:
            alerts.append(Alert(
                timestamp=snapshot.timestamp,
                level=AlertLevel.WARNING,
                metric=MonitoringMetric.SIGNAL_QUALITY,
                message=f"Qualidade dos sinais baixa: {snapshot.signal_quality_score:.1%}",
                current_value=snapshot.signal_quality_score,
                threshold_value=thresholds['signal_quality_min'],
                suggested_action="Revisar calibração ou ajustar parâmetros"
            ))
        
        # Log dos alertas
        for alert in alerts:
            if alert.level == AlertLevel.CRITICAL:
                logger.error(f" ALERTA CRÍTICO: {alert.message}")
            elif alert.level == AlertLevel.WARNING:
                logger.warning(f" ALERTA: {alert.message}")
        
        return alerts

    async def _check_auto_rollback(self, snapshot: KPISnapshot):
        """Verifica se rollback automático é necessário"""
        rollback_config = self.config['auto_rollback']
        current_time = time.time()

        # Verificar se já passou tempo suficiente desde último rollback
        if (current_time - self.last_rollback_time) < rollback_config['min_time_between_rollbacks']:
            return

        # Verificar limite diário de rollbacks
        if self.rollback_count >= rollback_config['max_rollbacks_per_day']:
            return

        # Critérios para rollback automático
        should_rollback = False
        rollback_reason = ""

        if snapshot.approval_rate < rollback_config['approval_rate_emergency']:
            should_rollback = True
            rollback_reason = f"Taxa de aprovação crítica: {snapshot.approval_rate:.1%}"

        elif snapshot.false_positive_rate > rollback_config['false_positive_emergency']:
            should_rollback = True
            rollback_reason = f"Taxa de falsos positivos crítica: {snapshot.false_positive_rate:.1%}"

        if should_rollback and self.backup_thresholds:
            logger.warning(f" EXECUTANDO ROLLBACK AUTOMÁTICO: {rollback_reason}")
            await self._execute_rollback(rollback_reason)

    async def _execute_rollback(self, reason: str):
        """Executa rollback para thresholds de backup"""
        if not self.backup_thresholds:
            logger.error(" Rollback solicitado mas não há backup disponível")
            return

        try:
            # Aplicar thresholds de backup
            if self.trading_system and hasattr(self.trading_system, 'quantum_thresholds'):
                self.trading_system.quantum_thresholds.update(self.backup_thresholds)

                # Também atualizar adaptive_manager se disponível
                if hasattr(self.trading_system, 'adaptive_manager'):
                    adaptive_thresholds = self.trading_system.adaptive_manager.current_thresholds
                    for metric, value in self.backup_thresholds.items():
                        if hasattr(adaptive_thresholds, metric):
                            setattr(adaptive_thresholds, metric, value)

            # Registrar rollback
            self.last_rollback_time = time.time()
            self.rollback_count += 1

            # Criar alerta de rollback
            rollback_alert = Alert(
                timestamp=time.time(),
                level=AlertLevel.EMERGENCY,
                metric=MonitoringMetric.SYSTEM_PERFORMANCE,
                message=f"Rollback automático executado: {reason}",
                current_value=0.0,
                threshold_value=0.0,
                suggested_action="Investigar causa raiz e recalibrar sistema",
                auto_action_taken=True
            )
            self.alerts.append(rollback_alert)

            logger.warning(f" Rollback executado com sucesso. Razão: {reason}")
            logger.info(f" Thresholds restaurados para backup de {datetime.fromtimestamp(self.backup_timestamp)}")

        except Exception as e:
            logger.error(f" Erro executando rollback: {e}")

    async def _create_threshold_backup(self):
        """Cria backup dos thresholds atuais"""
        if self.trading_system and hasattr(self.trading_system, 'quantum_thresholds'):
            self.backup_thresholds = self.trading_system.quantum_thresholds.copy()
            self.backup_timestamp = time.time()

            logger.info(f" Backup de thresholds criado: {len(self.backup_thresholds)} métricas")

            # Persistir backup
            backup_file = Path(self.config['persistence']['backup_thresholds_file'])
            backup_file.parent.mkdir(parents=True, exist_ok=True)

            backup_data = {
                'timestamp': self.backup_timestamp,
                'thresholds': self.backup_thresholds
            }

            with open(backup_file, 'w') as f:
                json.dump(backup_data, f, indent=2)

    # Métodos de cálculo de métricas (simulados - integrar com sistema real)
    async def _calculate_approval_rate(self) -> float:
        """Calcula taxa de aprovação atual"""
        # TODO: Integrar com sistema real de trading
        # Por enquanto, simular baseado em dados históricos
        return 0.175  # 17.5% - valor exemplo

    async def _calculate_false_positive_rate(self) -> float:
        """Calcula taxa de falsos positivos"""
        # TODO: Integrar com análise de resultados de trades
        return 0.12  # 12% - valor exemplo

    async def _calculate_false_negative_rate(self) -> float:
        """Calcula taxa de falsos negativos"""
        # TODO: Integrar com análise de oportunidades perdidas
        return 0.18  # 18% - valor exemplo

    async def _calculate_signal_quality(self) -> float:
        """Calcula score de qualidade dos sinais"""
        # TODO: Integrar com métricas de performance dos sinais
        return 0.75  # 75% - valor exemplo

    async def _detect_current_regime(self) -> str:
        """Detecta regime de mercado atual"""
        # TODO: Integrar com detector de regime do calibrador
        return "normal"

    def _get_recent_signal_count(self) -> int:
        """Obtém contagem de sinais recentes"""
        # TODO: Integrar com sistema de sinais
        return 25

    def _get_recent_profitable_signals(self) -> int:
        """Obtém contagem de sinais lucrativos recentes"""
        # TODO: Integrar com análise de resultados
        return 18

    async def _cleanup_old_data(self):
        """Remove dados antigos do histórico"""
        current_time = time.time()
        retention_seconds = self.config['collection']['history_retention_hours'] * 3600
        alert_retention_seconds = self.config['collection']['alert_retention_hours'] * 3600

        # Limpar histórico de KPIs
        self.kpi_history = [
            kpi for kpi in self.kpi_history
            if (current_time - kpi.timestamp) < retention_seconds
        ]

        # Limpar alertas antigos
        self.alerts = [
            alert for alert in self.alerts
            if (current_time - alert.timestamp) < alert_retention_seconds
        ]

    async def _persist_monitoring_data(self):
        """Persiste dados de monitoramento"""
        try:
            # Persistir histórico de KPIs
            kpi_file = Path(self.config['persistence']['kpi_history_file'])
            kpi_file.parent.mkdir(parents=True, exist_ok=True)

            kpi_data = [asdict(kpi) for kpi in self.kpi_history[-100:]]  # Últimos 100 snapshots
            with open(kpi_file, 'w') as f:
                json.dump(kpi_data, f, indent=2)

            # Persistir alertas
            alerts_file = Path(self.config['persistence']['alerts_file'])
            alerts_data = [asdict(alert) for alert in self.alerts[-50:]]  # Últimos 50 alertas
            with open(alerts_file, 'w') as f:
                json.dump(alerts_data, f, indent=2)

        except Exception as e:
            logger.error(f"Erro persistindo dados de monitoramento: {e}")

    def get_current_status(self) -> Dict[str, Any]:
        """Retorna status atual do sistema de monitoramento"""
        recent_alerts = [alert for alert in self.alerts if (time.time() - alert.timestamp) < 3600]

        return {
            'monitoring_active': self.monitoring_active,
            'current_kpis': self.current_kpis,
            'recent_alerts_count': len(recent_alerts),
            'critical_alerts_count': len([a for a in recent_alerts if a.level == AlertLevel.CRITICAL]),
            'rollback_count_today': self.rollback_count,
            'last_rollback_time': self.last_rollback_time,
            'backup_available': self.backup_thresholds is not None,
            'kpi_history_size': len(self.kpi_history),
            'total_alerts': len(self.alerts)
        }

    def get_performance_report(self) -> Dict[str, Any]:
        """Gera relatório de performance do sistema"""
        if not self.kpi_history:
            return {'error': 'Nenhum dado histórico disponível'}

        recent_kpis = self.kpi_history[-24:]  # Últimas 24 medições (2 horas se coletando a cada 5min)

        avg_approval_rate = sum(kpi.approval_rate for kpi in recent_kpis) / len(recent_kpis)
        avg_false_positive = sum(kpi.false_positive_rate for kpi in recent_kpis) / len(recent_kpis)
        avg_signal_quality = sum(kpi.signal_quality_score for kpi in recent_kpis) / len(recent_kpis)

        return {
            'period': 'Últimas 2 horas',
            'measurements': len(recent_kpis),
            'average_approval_rate': avg_approval_rate,
            'average_false_positive_rate': avg_false_positive,
            'average_signal_quality': avg_signal_quality,
            'target_approval_rate': '15-20%',
            'performance_status': 'OK' if 0.15 <= avg_approval_rate <= 0.20 else 'NEEDS_ATTENTION',
            'recommendations': self._generate_recommendations(avg_approval_rate, avg_false_positive, avg_signal_quality)
        }

    def _generate_recommendations(self, approval_rate: float, false_positive_rate: float, signal_quality: float) -> List[str]:
        """Gera recomendações baseadas nas métricas"""
        recommendations = []

        if approval_rate < 0.15:
            recommendations.append("Taxa de aprovação baixa - considerar relaxar thresholds")
        elif approval_rate > 0.20:
            recommendations.append("Taxa de aprovação alta - considerar tornar thresholds mais restritivos")

        if false_positive_rate > 0.15:
            recommendations.append("Taxa de falsos positivos alta - aumentar seletividade")

        if signal_quality < 0.70:
            recommendations.append("Qualidade dos sinais baixa - revisar calibração")

        if not recommendations:
            recommendations.append("Sistema operando dentro dos parâmetros esperados")

        return recommendations
