from __future__ import annotations

"""CoherenceGuard module for QUALIA.

Listens to ``nexus.cross_modal_coherence`` events and publishes
``nexus.coherence_guard.freeze`` or ``nexus.coherence_guard.unfreeze``
when the coherence level crosses a configurable threshold.
Optionally forwards metrics to :class:`QUALIAConsciousness` via
:func:`~qualia.core.consciousness.QUALIAConsciousness.process_quantum_state`.
"""

from typing import Any, Dict, Optional, List

from qualia.memory.event_bus import SimpleEventBus
from qualia.utils.logger import get_logger
from qualia.config.settings import settings
from qualia.core.consciousness import QUALIAConsciousness
from qualia.events import (
    CrossModalCoherenceEvent,
    CoherenceGuardFreeze,
    CoherenceGuardUnfreeze,
)

logger = get_logger(__name__)


class CoherenceGuard:
    """Monitor coherence and freeze/unfreeze decision engine."""

    def __init__(
        self,
        event_bus: SimpleEventBus,
        *,
        threshold: Optional[float] = None,
        qconsciousness: Optional[QUALIAConsciousness] = None,
    ) -> None:
        self.event_bus = event_bus
        self.threshold = (
            threshold if threshold is not None else settings.low_coherence_threshold
        )
        self.qconsciousness = qconsciousness
        self._frozen = False
        event_bus.subscribe("nexus.cross_modal_coherence", self._on_coherence)
        logger.info("CoherenceGuard initialized with threshold %.3f", self.threshold)

    # ------------------------------------------------------------------
    @property
    def frozen(self) -> bool:
        return self._frozen

    # ------------------------------------------------------------------
    def _on_coherence(
        self, payload: Dict[str, Any] | CrossModalCoherenceEvent
    ) -> None:  # pragma: no cover - simple pass-through
        if hasattr(payload, "coherence"):
            coherence = float(payload.coherence)
            timestamp = payload.timestamp
        else:
            coherence = float(payload.get("coherence", 0.0))
            timestamp = payload.get("timestamp")
        metrics: Dict[str, Any] = {}
        if "entropy" in payload:
            metrics["entropy"] = payload["entropy"]
        metrics["coherence"] = coherence
        if "mutual_info" in payload:
            metrics["mutual_info"] = payload["mutual_info"]
        if self.qconsciousness is not None:
            try:
                self.qconsciousness.process_quantum_state([], metrics)
            except Exception:  # pragma: no cover - best effort
                logger.warning(
                    "CoherenceGuard failed to forward metrics", exc_info=True
                )
        if coherence < self.threshold and not self._frozen:
            self._frozen = True
            self.event_bus.publish(
                "nexus.coherence_guard.freeze",
                CoherenceGuardFreeze(coherence=coherence, timestamp=timestamp),
            )
            logger.info("CoherenceGuard triggered freeze at %.3f", coherence)
        elif coherence >= self.threshold and self._frozen:
            self._frozen = False
            self.event_bus.publish(
                "nexus.coherence_guard.unfreeze",
                CoherenceGuardUnfreeze(coherence=coherence, timestamp=timestamp),
            )
            logger.info("CoherenceGuard triggered unfreeze at %.3f", coherence)
