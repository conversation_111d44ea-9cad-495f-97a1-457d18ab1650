"""Metrics helpers for Symbolic Modulation (SIM-08).

Wraps DogStatsd but degrades gracefully when the package is unavailable.
"""
from __future__ import annotations

from typing import List, Optional

try:
    from datadog import DogStatsd  # type: ignore
except ImportError:  # pragma: no cover
    # Fallback no-op client when datadog is missing in env (e.g. CI)
    class _NoOpStatsd:  # pylint: disable=too-few-public-methods
        def incr(self, *_, **__):  # noqa: D401
            pass

        def gauge(self, *_, **__):  # noqa: D401
            pass

    DogStatsd = _NoOpStatsd  # type: ignore

# Create global client (unix socket / localhost defaults)
statsd = DogStatsd()  # type: ignore[call-arg]


# ---------------------------------------------------------------------------
# Public helpers
# ---------------------------------------------------------------------------

def statsd_increment(metric: str, value: int = 1, tags: Optional[List[str]] = None) -> None:  # noqa: D401
    """Increment StatsD counter when client available."""
    try:
        statsd.incr(metric, value, tags=tags or [])  # type: ignore[arg-type]
    except Exception:  # pragma: no cover
        # Ignore metrics errors in production path
        pass


def statsd_gauge(metric: str, value: float, tags: Optional[List[str]] = None) -> None:  # noqa: D401
    """Send StatsD gauge when client available."""
    try:
        statsd.gauge(metric, value, tags=tags or [])  # type: ignore[arg-type]
    except Exception:
        pass
