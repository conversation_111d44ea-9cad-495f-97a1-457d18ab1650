"""Persistence helpers for trader open positions."""

from __future__ import annotations

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Any

from ..common_types import QuantumSignaturePacket
from ..core.position import OpenPosition
from ..utils.logger import get_logger
from ..utils.persistence import save_positions_json, load_positions_json

logger = get_logger(__name__)


def save_open_positions(trader: Any) -> None:
    """Persist trader open positions to JSON file."""

    try:
        from ..config import config

        os.makedirs(config.cache_dir, exist_ok=True)
        positions_file = config.open_positions_file
        save_positions_json(trader.open_positions, positions_file)
        total_pos = sum(len(p) for p in trader.open_positions.values())
        logger.info(
            "Posições abertas salvas em %s. Total: %s", positions_file, total_pos
        )
    except OSError as exc:
        logger.error(
            "Erro ao salvar posições abertas em %s: %s",
            positions_file,
            exc,
            exc_info=True,
        )


def load_open_positions(trader: Any) -> None:
    """Load open positions from JSON file into ``trader``."""

    try:
        from ..config import config

        json_file = config.open_positions_file

        if os.path.exists(json_file):
            trader.open_positions = load_positions_json(json_file)
            for symbol, positions in list(trader.open_positions.items()):
                if positions and isinstance(positions[0], dict):
                    converted = []
                    for data in positions:
                        ts = data.get("timestamp")
                        if isinstance(ts, str):
                            try:
                                timestamp = datetime.fromisoformat(ts)
                            except ValueError:
                                timestamp = datetime.fromtimestamp(float(ts))
                            data["timestamp"] = timestamp
                        qsp = data.get("captured_quantum_signature_packet")
                        if isinstance(qsp, dict):
                            try:
                                data["captured_quantum_signature_packet"] = (
                                    QuantumSignaturePacket(**qsp)
                                )
                            except (TypeError, ValueError):
                                data["captured_quantum_signature_packet"] = None
                        converted.append(OpenPosition(**data))
                    trader.open_positions[symbol] = converted
        else:
            logger.info("Arquivo de posições %s não encontrado.", json_file)
            return

        if trader.open_positions:
            total_pos = sum(len(p) for p in trader.open_positions.values())
            logger.info("Carregadas %s posições abertas de %s", total_pos, json_file)

            positions_value = sum(
                p.entry_price * p.size
                for positions in trader.open_positions.values()
                for p in positions
            )
            if getattr(trader, "wallet_state", None):
                trader.wallet_state["positions_value"] = positions_value
                trader.wallet_state["available_cash"] = (
                    trader.wallet_state["current_capital"] - positions_value
                )
                logger.info(
                    "Wallet atualizada com posições carregadas. Valor total: $%.2f",
                    positions_value,
                )
    except (OSError, json.JSONDecodeError, KeyError) as exc:
        backup_path = Path(f"{json_file}.bak")
        try:
            Path(json_file).rename(backup_path)
            logger.warning(
                "Falha ao carregar %s: %s. Arquivo movido para %s",
                json_file,
                exc,
                backup_path,
            )
        except OSError as move_exc:
            logger.error(
                "Erro ao mover arquivo corrompido %s: %s",
                json_file,
                move_exc,
                exc_info=True,
            )
        trader.open_positions = {}


def handle_open_positions_on_shutdown(trader: Any) -> None:
    """Persist open positions when some remain after shutdown."""

    if any(trader.open_positions.values()):
        logger.info("Algumas posições não foram fechadas. Salvando para persistência.")
        save_open_positions(trader)
