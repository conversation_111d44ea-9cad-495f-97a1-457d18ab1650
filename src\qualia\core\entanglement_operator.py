"""Simplified entanglement operator.

The :class:`EntanglementOperator` estimates an entanglement depth
from the correlation matrix of a set of states and keeps a history of
all computed results.

Examples
--------
>>> op = EntanglementOperator({"weight": 1.0})
>>> state = await op.entangle(np.eye(2), timestamp=0.0)
>>> state.entanglement_depth
1.0
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import List, Any
import logging

import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class EntanglementState:
    """Result of an entanglement computation."""

    entanglement_depth: float
    correlation_matrix: np.ndarray
    timestamp: float


class EntanglementOperator:
    """Operator implementing simplified entanglement dynamics."""

    def __init__(
        self, config: dict | None = None, *, history_maxlen: int = 1000
    ) -> None:
        cfg = config or {}
        self.weight = float(cfg.get("weight", 1.0))
        self.current_state: EntanglementState | None = None
        self.entanglement_history: List[EntanglementState] = []
        self.history_maxlen = int(history_maxlen)

    async def entangle(self, states: np.ndarray, timestamp: float) -> EntanglementState:
        """Compute entanglement depth for ``states``."""
        arr = np.atleast_2d(states.astype(float))
        corr = np.corrcoef(arr)
        corr[np.isnan(corr)] = 0.0
        depth = float(np.mean(np.abs(corr))) * self.weight
        state = EntanglementState(depth, corr, timestamp)
        self.current_state = state
        self.entanglement_history.append(state)
        if len(self.entanglement_history) > self.history_maxlen:
            self.entanglement_history.pop(0)
        return state

    def entanglement_level(self) -> float:
        """Return current entanglement depth."""
        return self.current_state.entanglement_depth if self.current_state else 0.0

    def get_state_dict(self) -> dict[str, Any]:
        """Return a serializable representation of the operator state."""
        return {
            "weight": self.weight,
            "history_length": len(self.entanglement_history),
            "current_entanglement": (
                self.current_state.entanglement_depth if self.current_state else 0.0
            ),
        }
