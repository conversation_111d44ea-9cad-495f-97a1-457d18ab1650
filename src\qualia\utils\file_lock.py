"""Cross-platform file locking utilities.

This module provides thin wrappers around the platform specific locking
primitives available on each operating system.  The functions return
``True`` when the lock or unlock operation succeeds and ``False`` when it
fails.  They never raise exceptions directly so that callers can decide on
retry or fall back strategies.

In addition to the ``lock_file`` and ``unlock_file`` helpers a small context
manager :class:`FileLock` is provided to make usage ergonomic.
"""

from __future__ import annotations

from contextlib import AbstractContextManager
from typing import IO, Any

import sys

if sys.platform == "win32":
    import msvcrt

    def lock_file(fd: IO[Any]) -> bool:
        """Acquire an exclusive lock on ``fd`` using ``msvcrt``."""
        try:
            msvcrt.locking(fd.fileno(), msvcrt.LK_NBLCK, 1)
            return True
        except (IOError, OSError):
            return False

    def unlock_file(fd: IO[Any]) -> bool:
        """Release a lock acquired with :func:`lock_file`."""
        try:
            msvcrt.locking(fd.fileno(), msvcrt.LK_UNLCK, 1)
            return True
        except (IOError, OSError):
            return False

else:
    import fcntl

    def lock_file(fd: IO[Any]) -> bool:
        """Acquire an exclusive lock on ``fd`` using ``fcntl``."""
        try:
            fcntl.flock(fd, fcntl.LOCK_EX | fcntl.LOCK_NB)
            return True
        except (IOError, OSError):
            return False

    def unlock_file(fd: IO[Any]) -> bool:
        """Release a lock acquired with :func:`lock_file`."""
        try:
            fcntl.flock(fd, fcntl.LOCK_UN)
            return True
        except (IOError, OSError):
            return False


class FileLock(AbstractContextManager[IO[Any]]):
    """Context manager to hold an exclusive lock on a file handle."""

    def __init__(self, fd: IO[Any]) -> None:
        self.fd = fd
        self.acquired = False

    def acquire(self) -> bool:
        """Attempt to acquire the lock."""

        self.acquired = lock_file(self.fd)
        return self.acquired

    def release(self) -> bool:
        """Release the lock if held."""

        if self.acquired:
            self.acquired = not unlock_file(self.fd)
        return not self.acquired

    # AbstractContextManager requires __exit__ only
    def __enter__(self) -> IO[Any]:
        if not self.acquire():
            raise RuntimeError("Unable to acquire file lock")
        return self.fd

    def __exit__(self, exc_type, exc, tb) -> None:
        self.release()


__all__ = ["lock_file", "unlock_file", "FileLock"]
