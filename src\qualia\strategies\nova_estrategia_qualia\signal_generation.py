from __future__ import annotations

from typing import Any, Dict

from ...intentions import IntentionEnvelope
from ...retroselector import RetroSelector
from ...utils.simulator import simulate_returns

import numpy as np
import pandas as pd

from ...utils.logger import get_logger
from ...trading.data_utils import is_data_empty

from .indicators import compute_sma, compute_rsi

logger = get_logger(__name__)


def generate_signals(strategy: Any, data_1h: pd.DataFrame) -> Dict[str, Any]:
    """Generate basic TSVF strategy signals."""
    prices = data_1h["close"]
    num_candles = len(prices)

    envelope = IntentionEnvelope(
        profit_target=strategy.parameters.get("profit_target", 5.0),
        max_drawdown=strategy.parameters.get("max_drawdown", 1.0),
        horizon_hours=strategy.parameters.get("horizon", 24),
    )
    retro_selector = RetroSelector()

    returns_hist = prices.pct_change().dropna()
    vol_window = strategy.parameters.get("vol_window", 20)

    recent_vol = (
        returns_hist[-vol_window:].std() if not is_data_empty(returns_hist) else 0.0
    )
    mc_paths = simulate_returns(
        mu=0.0,
        sigma=float(recent_vol),
        horizon=envelope.horizon_hours,
        num_paths=100,
    )

    positions_s1 = np.zeros(num_candles)
    if num_candles > strategy.s1_tsvf_window + 1:
        vals = prices.values
        rolling = np.lib.stride_tricks.sliding_window_view(
            vals, strategy.s1_tsvf_window
        )
        n_pairs = num_candles - strategy.s1_tsvf_window - 1
        win_in = rolling[:n_pairs]
        win_fin = rolling[1 : n_pairs + 1]
        strengths = [
            strategy._calculate_tsvf_outputs(
                i,
                f,
                envelope=envelope,
                mc_paths=mc_paths,
                retro_selector=retro_selector,
            )["strength"]
            for i, f in zip(win_in, win_fin)
        ]
        positions_s1[strategy.s1_tsvf_window + 1 :] = np.where(
            np.abs(strengths) > strategy.s1_strength_threshold,
            np.sign(strengths),
            0,
        )

    sma_short = compute_sma(prices, strategy.s2_sma_short_period).shift(1)
    sma_long = compute_sma(prices, strategy.s2_sma_long_period).shift(1)
    rsi = compute_rsi(prices, strategy.s2_rsi_period).shift(1)

    cond_buy = (
        (positions_s1 > 0) & (sma_short > sma_long) & (rsi < strategy.s2_rsi_overbought)
    )
    cond_sell = (
        (positions_s1 < 0) & (sma_short < sma_long) & (rsi > strategy.s2_rsi_oversold)
    )
    positions_s2 = np.where(cond_buy, 1, np.where(cond_sell, -1, 0))
    positions_s2[0] = 0

    market_returns = prices.pct_change().fillna(0).values

    return {
        "positions_s1": positions_s1,
        "positions_s2": positions_s2,
        "positions_s3": np.zeros(num_candles),
        "returns_s1": np.roll(positions_s1, 1) * market_returns,
        "returns_s2": np.roll(positions_s2, 1) * market_returns,
        "returns_s3": np.zeros(num_candles),
        "market_returns": market_returns,
    }
