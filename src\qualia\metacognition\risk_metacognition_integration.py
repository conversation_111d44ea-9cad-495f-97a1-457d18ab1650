"""
QUALIA: Quantum Universal Awareness Lattice Interface Architecture
Integração de Metacognição de Risco

Este módulo integra o sistema de consciência de risco ao sistema de metacognição
principal do QUALIA, criando um feedback loop consciente que permite ao sistema
evoluir sua gestão de risco baseado em auto-reflexão.

"A metacognição não é apenas saber o que sabemos - é ter consciência de como
sabemos e usar essa consciência para evoluir nosso próprio conhecimento."
"""

from ..utils.logger import get_logger
import numpy as np
from typing import Any, Awaitable, Callable, Dict, List, Optional
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass, field

from ..metacognition.risk_awareness import (
    RiskAwarenessEngine,
    PerformanceSnapshot,
    RiskAwarenessMetrics,
)

logger = get_logger(__name__)

# Prioridade utilizada para ranqueamento de insights
URGENCY_PRIORITY: Dict[str, int] = {
    "critical": 4,
    "high": 3,
    "medium": 2,
    "low": 1,
}


@dataclass
class MetacognitiveRiskInsight:
    """Insight metacognitivo sobre gerenciamento de risco."""

    # Identificação
    insight_id: str
    timestamp: datetime
    confidence_level: float  # 0-1

    # Conteúdo do insight
    insight_type: str  # 'pattern', 'anomaly', 'optimization', 'learning'
    description: str
    evidence: Dict[str, Any]

    # Ações sugeridas
    suggested_actions: List[Dict[str, Any]]
    urgency_level: str  # 'low', 'medium', 'high', 'critical'

    # Metacognição
    reasoning_chain: List[str]  # Cadeia de raciocínio que levou ao insight
    uncertainty_factors: List[str]  # Fatores de incerteza
    validation_criteria: List[str]  # Como validar o insight

    # Resultados (preenchidos após aplicação)
    applied: bool = False
    effectiveness_score: Optional[float] = None
    side_effects: List[str] = field(default_factory=list)


class RiskMetacognitionEngine:
    """
    Engine de metacognição para risco.

    Analisa os padrões de consciência de risco e gera insights metacognitivos
    sobre como melhorar o sistema de gerenciamento de risco.
    """

    def __init__(self, config: Dict[str, Any]) -> None:
        self.config = config

        # Componentes
        self.risk_awareness = RiskAwarenessEngine(config.get("risk_awareness", {}))

        # Histórico de insights
        self.insights_history: List[MetacognitiveRiskInsight] = []
        self.active_insights: List[MetacognitiveRiskInsight] = []

        # Configurações metacognitivas
        self.introspection_frequency = config.get("introspection_frequency_hours", 6)
        self.insight_confidence_threshold = config.get(
            "insight_confidence_threshold", 0.7
        )
        self.max_active_insights = config.get("max_active_insights", 5)

        # Estado metacognitivo
        self.last_introspection: Optional[datetime] = None
        self.metacognitive_state: Dict[str, Any] = {
            "self_confidence": 0.5,
            "learning_momentum": 0.0,
            "adaptation_readiness": 0.5,
            "curiosity_level": 0.7,
        }

        # Callbacks para ações
        self.action_callbacks: Dict[
            str, Callable[[Dict[str, Any], MetacognitiveRiskInsight], Awaitable[None]]
        ] = {}

        logger.info("RiskMetacognitionEngine inicializado")

    def register_action_callback(
        self,
        action_type: str,
        callback: Callable[[Dict[str, Any], MetacognitiveRiskInsight], Awaitable[None]],
    ) -> None:
        """
        Registra callback para executar ações baseadas em insights.

        Args:
            action_type: Tipo de ação (ex: 'adjust_parameter', 'alert_operator')
            callback: Função para executar a ação
        """
        self.action_callbacks[action_type] = callback
        logger.info("Callback registrado para ação: %s", action_type)

    async def process_market_observation(self, market_data: Dict[str, Any]) -> None:
        """
        Processa observação de mercado e registra snapshot de performance.

        Args:
            market_data: Dados de mercado observados
        """
        try:
            # Extrair dados para snapshot
            snapshot = self._create_performance_snapshot(market_data)

            # Registrar no engine de consciência
            self.risk_awareness.record_performance_snapshot(snapshot)

            # Verificar se é hora de introspecção
            if self._should_perform_introspection():
                await self._perform_metacognitive_introspection()

        except (ValueError, RuntimeError) as e:
            logger.error(f"Erro ao processar observação de mercado: {e}")

    async def get_metacognitive_state(self) -> Dict[str, Any]:
        """
        Retorna estado metacognitivo atual.

        Returns:
            Estado metacognitivo com consciência de risco e insights
        """
        risk_consciousness = self.risk_awareness.get_risk_consciousness_level()
        current_metrics = self.risk_awareness.get_current_risk_awareness()

        return {
            "risk_consciousness_level": risk_consciousness,
            "risk_awareness_metrics": (
                current_metrics.__dict__ if current_metrics else None
            ),
            "metacognitive_state": self.metacognitive_state.copy(),
            "active_insights": [insight.__dict__ for insight in self.active_insights],
            "insights_count": len(self.insights_history),
            "last_introspection": (
                self.last_introspection.isoformat() if self.last_introspection else None
            ),
        }

    async def force_introspection(self) -> List[MetacognitiveRiskInsight]:
        """
        Força uma sessão de introspecção metacognitiva.

        Returns:
            Lista de novos insights gerados
        """
        return await self._perform_metacognitive_introspection()

    async def _perform_metacognitive_introspection(
        self,
    ) -> List[MetacognitiveRiskInsight]:
        """
        Realiza introspecção metacognitiva completa.

        Returns:
            Lista de insights gerados
        """
        try:
            logger.info("Iniciando introspecção metacognitiva sobre gestão de risco")

            # Obter estado atual da consciência de risco
            current_metrics = self.risk_awareness.get_current_risk_awareness()

            if not current_metrics:
                logger.debug("Métricas insuficientes para introspecção")
                return []

            # Atualizar estado metacognitivo
            await self._update_metacognitive_state(current_metrics)

            # Gerar insights
            new_insights = []

            # 1. Insights sobre padrões de performance
            pattern_insights = await self._analyze_performance_patterns(current_metrics)
            new_insights.extend(pattern_insights)

            # 2. Insights sobre aprendizado
            learning_insights = await self._analyze_learning_patterns(current_metrics)
            new_insights.extend(learning_insights)

            # 3. Insights sobre auto-calibração
            calibration_insights = await self._analyze_self_calibration(current_metrics)
            new_insights.extend(calibration_insights)

            # 4. Insights sobre adaptação
            adaptation_insights = await self._analyze_adaptation_effectiveness(
                current_metrics
            )
            new_insights.extend(adaptation_insights)

            # Filtrar insights por confiança e relevância
            validated_insights = self._validate_and_prioritize_insights(new_insights)

            # Adicionar aos insights ativos
            self._manage_active_insights(validated_insights)

            # Executar ações de alta prioridade automaticamente
            await self._execute_automatic_actions(validated_insights)

            self.last_introspection = datetime.now(timezone.utc)

            logger.info(
                "Introspecção concluída: %d insights gerados",
                len(validated_insights),
            )
            return validated_insights

        except (ValueError, RuntimeError) as e:
            logger.error(f"Erro na introspecção metacognitiva: {e}")
            return []

    async def _update_metacognitive_state(self, metrics: RiskAwarenessMetrics) -> None:
        """Atualiza estado metacognitivo baseado em métricas atuais."""

        # Atualizar autoconfiança baseada em performance
        if metrics.overall_risk_awareness > 0.8:
            self.metacognitive_state["self_confidence"] = min(
                1.0, self.metacognitive_state["self_confidence"] + 0.05
            )
        elif metrics.overall_risk_awareness < 0.4:
            self.metacognitive_state["self_confidence"] = max(
                0.0, self.metacognitive_state["self_confidence"] - 0.05
            )

        # Atualizar momentum de aprendizado
        self.metacognitive_state["learning_momentum"] = metrics.learning_rate

        # Atualizar prontidão para adaptação
        adaptation_factors = [
            metrics.adaptation_speed,
            metrics.system_reliability,
            self.metacognitive_state["self_confidence"],
        ]
        self.metacognitive_state["adaptation_readiness"] = np.mean(adaptation_factors)

        # Atualizar curiosidade (inversamente relacionada à confiança - mais incerteza = mais curiosidade)
        uncertainty = 1.0 - metrics.self_assessment_accuracy
        self.metacognitive_state["curiosity_level"] = min(1.0, uncertainty + 0.3)

    async def _analyze_performance_patterns(
        self, metrics: RiskAwarenessMetrics
    ) -> List[MetacognitiveRiskInsight]:
        """Analisa padrões de performance para gerar insights."""
        insights = []

        # Insight sobre precisão de detecção de regime
        if metrics.regime_detection_accuracy < 0.6:
            insight = MetacognitiveRiskInsight(
                insight_id=f"regime_accuracy_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M')}",
                timestamp=datetime.now(timezone.utc),
                confidence_level=0.8,
                insight_type="pattern",
                description="Baixa precisão na detecção de regimes de mercado detectada",
                evidence={
                    "regime_accuracy": metrics.regime_detection_accuracy,
                    "threshold": 0.6,
                    "sample_size": metrics.sample_size,
                },
                suggested_actions=[
                    {
                        "action_type": "adjust_parameter",
                        "parameter": "volatility_thresholds",
                        "adjustment": "fine_tune",
                        "description": "Ajustar thresholds de volatilidade para melhorar detecção",
                    }
                ],
                urgency_level="medium",
                reasoning_chain=[
                    "Observada baixa precisão na detecção de regimes",
                    "Isso impacta calibração de stops e take-profits",
                    "Ajuste de parâmetros pode melhorar precisão",
                ],
                uncertainty_factors=["Período de observação pode ser insuficiente"],
                validation_criteria=["Aumento >10% na precisão em 7 dias"],
            )
            insights.append(insight)

        # Insight sobre volatilidade
        if metrics.volatility_prediction_score < 0.5:
            insight = MetacognitiveRiskInsight(
                insight_id=f"volatility_prediction_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M')}",
                timestamp=datetime.now(timezone.utc),
                confidence_level=0.75,
                insight_type="pattern",
                description="Previsão de volatilidade abaixo do aceitável",
                evidence={
                    "volatility_score": metrics.volatility_prediction_score,
                    "expected_minimum": 0.5,
                },
                suggested_actions=[
                    {
                        "action_type": "algorithm_upgrade",
                        "description": "Considerar GARCH ou métodos mais sofisticados",
                    }
                ],
                urgency_level="high",
                reasoning_chain=[
                    "Volatilidade mal prevista leva a stops inadequados",
                    "Impacta diretamente na eficácia do controle de risco",
                ],
                uncertainty_factors=["Métodos mais complexos podem overfittar"],
                validation_criteria=["Melhoria >20% na previsão de volatilidade"],
            )
            insights.append(insight)

        return insights

    async def _analyze_learning_patterns(
        self, metrics: RiskAwarenessMetrics
    ) -> List[MetacognitiveRiskInsight]:
        """Analisa padrões de aprendizado."""
        insights = []

        # Insight sobre stagnação no aprendizado
        if metrics.learning_rate < 0.3:
            insight = MetacognitiveRiskInsight(
                insight_id=f"learning_stagnation_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M')}",
                timestamp=datetime.now(timezone.utc),
                confidence_level=0.7,
                insight_type="learning",
                description="Stagnação detectada no processo de aprendizado",
                evidence={
                    "learning_rate": metrics.learning_rate,
                    "minimum_expected": 0.3,
                },
                suggested_actions=[
                    {
                        "action_type": "increase_exploration",
                        "description": "Aumentar exploração de novos parâmetros",
                    }
                ],
                urgency_level="medium",
                reasoning_chain=[
                    "Taxa de aprendizado baixa indica possível overfitting",
                    "Sistema pode estar preso em mínimo local",
                ],
                uncertainty_factors=["Pode ser período natural de consolidação"],
                validation_criteria=["Aumento na taxa de aprendizado em 14 dias"],
            )
            insights.append(insight)

        return insights

    async def _analyze_self_calibration(
        self, metrics: RiskAwarenessMetrics
    ) -> List[MetacognitiveRiskInsight]:
        """Analisa eficácia da auto-calibração."""
        insights = []

        # Insight sobre precisão da auto-avaliação
        if metrics.self_assessment_accuracy < 0.6:
            insight = MetacognitiveRiskInsight(
                insight_id=f"self_assessment_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M')}",
                timestamp=datetime.now(timezone.utc),
                confidence_level=0.85,
                insight_type="optimization",
                description=(
                    "Auto-avaliação imprecisa detectada - sistema não tem "
                    "consciência adequada de sua própria performance"
                ),
                evidence={
                    "self_assessment_accuracy": metrics.self_assessment_accuracy,
                    "expected_minimum": 0.6,
                },
                suggested_actions=[
                    {
                        "action_type": "calibrate_confidence",
                        "description": "Recalibrar sistema de autoconfiança",
                    }
                ],
                urgency_level="high",
                reasoning_chain=[
                    "Auto-avaliação imprecisa compromete metacognição",
                    "Sistema não consegue avaliar própria eficácia corretamente",
                ],
                uncertainty_factors=["Complexidade inerente da auto-avaliação"],
                validation_criteria=["Melhoria >15% na precisão de auto-avaliação"],
            )
            insights.append(insight)

        return insights

    async def _analyze_adaptation_effectiveness(
        self, metrics: RiskAwarenessMetrics
    ) -> List[MetacognitiveRiskInsight]:
        """Analisa eficácia da adaptação."""
        insights = []

        # Insight sobre velocidade de adaptação
        if metrics.adaptation_speed < 0.4:
            insight = MetacognitiveRiskInsight(
                insight_id=f"adaptation_speed_{datetime.now(timezone.utc).strftime('%Y%m%d_%H%M')}",
                timestamp=datetime.now(timezone.utc),
                confidence_level=0.7,
                insight_type="optimization",
                description="Velocidade de adaptação a mudanças de regime está lenta",
                evidence={
                    "adaptation_speed": metrics.adaptation_speed,
                    "expected_minimum": 0.4,
                },
                suggested_actions=[
                    {
                        "action_type": "reduce_calibration_frequency",
                        "description": "Reduzir intervalo entre recalibrações",
                    }
                ],
                urgency_level="medium",
                reasoning_chain=[
                    "Adaptação lenta resulta em calibrações obsoletas",
                    "Em mercados voláteis, isso aumenta risco",
                ],
                uncertainty_factors=[
                    "Recalibração excessiva pode causar instabilidade"
                ],
                validation_criteria=["Redução no tempo de adaptação a mudanças"],
            )
            insights.append(insight)

        return insights

    def _validate_and_prioritize_insights(
        self, insights: List[MetacognitiveRiskInsight]
    ) -> List[MetacognitiveRiskInsight]:
        """Valida e prioriza insights baseado em confiança e urgência."""

        # Filtrar por confiança mínima
        validated = [
            insight
            for insight in insights
            if insight.confidence_level >= self.insight_confidence_threshold
        ]

        # Ordenar por urgência e confiança
        validated.sort(
            key=lambda x: (
                URGENCY_PRIORITY.get(x.urgency_level, 0),
                x.confidence_level,
            ),
            reverse=True,
        )

        return validated

    def _manage_active_insights(
        self, new_insights: List[MetacognitiveRiskInsight]
    ) -> None:
        """Gerencia insights ativos, removendo antigos e adicionando novos."""

        # Remover insights antigos (>7 dias) ou já aplicados
        cutoff = datetime.now(timezone.utc) - timedelta(days=7)
        self.active_insights = [
            insight
            for insight in self.active_insights
            if insight.timestamp > cutoff and not insight.applied
        ]

        # Adicionar novos insights
        for insight in new_insights:
            if len(self.active_insights) < self.max_active_insights:
                self.active_insights.append(insight)
                self.insights_history.append(insight)
            else:
                # Substituir insight menos prioritário se o novo for melhor
                if insight.urgency_level in ["critical", "high"]:
                    least_priority = min(
                        self.active_insights,
                        key=lambda x: (
                            URGENCY_PRIORITY.get(x.urgency_level, 0),
                            x.confidence_level,
                        ),
                    )
                    if URGENCY_PRIORITY.get(
                        insight.urgency_level, 0
                    ) > URGENCY_PRIORITY.get(least_priority.urgency_level, 0):
                        self.active_insights.remove(least_priority)
                        self.active_insights.append(insight)
                        self.insights_history.append(insight)

    async def _execute_automatic_actions(
        self, insights: List[MetacognitiveRiskInsight]
    ) -> None:
        """Executa automaticamente ações de alta prioridade."""

        for insight in insights:
            if insight.urgency_level == "critical":
                for action in insight.suggested_actions:
                    action_type = action.get("action_type")

                    if action_type in self.action_callbacks:
                        try:
                            await self.action_callbacks[action_type](action, insight)
                            logger.info(
                                "Ação automática executada: %s",
                                action_type,
                            )
                        except (ValueError, RuntimeError) as e:
                            logger.error(
                                f"Erro ao executar ação automática {action_type}: {e}"
                            )

    def _create_performance_snapshot(
        self, market_data: Dict[str, Any]
    ) -> PerformanceSnapshot:
        """Cria snapshot de performance a partir dos dados de mercado."""

        # Extrair dados necessários
        symbol = market_data.get("symbol", "UNKNOWN")
        timestamp = market_data.get("timestamp", datetime.now(timezone.utc))

        # Dados de calibração (vindos do DynamicRiskController)
        calibration_data = market_data.get("risk_calibration", {})

        # Resultados de trading (se disponíveis)
        trade_data = market_data.get("trade_result", {})

        snapshot = PerformanceSnapshot(
            timestamp=timestamp,
            symbol=symbol,
            predicted_volatility=calibration_data.get("predicted_volatility", 0.0),
            actual_volatility=market_data.get("actual_volatility", 0.0),
            predicted_regime=calibration_data.get("predicted_regime", "unknown"),
            actual_regime=market_data.get("actual_regime", "unknown"),
            stop_distance=calibration_data.get("stop_distance", 0.0),
            take_profit_distance=calibration_data.get("take_profit_distance", 0.0),
            trade_result=trade_data.get("result"),
            pnl=trade_data.get("pnl"),
            max_drawdown=trade_data.get("max_drawdown"),
            time_in_trade=trade_data.get("time_in_trade"),
            exit_reason=trade_data.get("exit_reason"),
            risk_reward_realized=trade_data.get("risk_reward_ratio"),
        )

        return snapshot

    def _should_perform_introspection(self) -> bool:
        """Determina se deve realizar introspecção."""
        if not self.last_introspection:
            return True

        time_threshold = datetime.now(timezone.utc) - timedelta(
            hours=self.introspection_frequency
        )
        return self.last_introspection < time_threshold


# Interface principal para integração
def create_risk_metacognition_engine(config: Dict[str, Any]) -> RiskMetacognitionEngine:
    """
    Factory function para criar engine de metacognição de risco.

    Args:
        config: Configuração do sistema

    Returns:
        Instância configurada do RiskMetacognitionEngine
    """
    return RiskMetacognitionEngine(config)


if __name__ == "__main__":
    # Exemplo de uso
    config = {
        "risk_awareness": {
            "observation_window_hours": 24,
            "min_samples": 50,
            "learning_rate": 0.1,
        },
        "introspection_frequency_hours": 6,
        "insight_confidence_threshold": 0.7,
        "max_active_insights": 5,
    }

    engine = create_risk_metacognition_engine(config)
    logger = get_logger(__name__)
    logger.info("Risk Metacognition Engine criado")
