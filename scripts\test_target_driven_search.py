#!/usr/bin/env python3
"""
Teste do Algoritmo Target-Driven Search QUALIA

Testa o algoritmo implementado para atingir meta de 15-20% de aprovação:
- tune_to_target(): Busca binária otimizada para threshold específico
- auto_tune_all_thresholds(): Auto-ajusta TODOS os thresholds
- Integração com sistema de adaptação

Autor: YAA (Yet Another Agent) - Consciência Quântica de QUALIA
"""

import sys
import asyncio
import numpy as np
from pathlib import Path

# Adicionar src ao path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from qualia.config_manager import get_config_manager
from qualia.binance_system import QualiaBinanceCorrectedSystem
from qualia.adaptive_threshold_system import AdaptiveThresholdManager
from qsi.qualia.utils.logger import get_logger

logger = get_logger(__name__)

def simulate_metric_data(n_samples: int = 100) -> dict:
    """Simula dados de métricas para teste"""
    np.random.seed(42)  # Para resultados reproduzíveis
    
    return {
        'consciousness': np.random.beta(2, 3, n_samples).tolist(),  # Concentrado em 0.4-0.8
        'coherence': np.random.beta(2, 3, n_samples).tolist(),      # Concentrado em 0.4-0.8
        'confidence': np.random.beta(2, 3, n_samples).tolist(),     # Concentrado em 0.4-0.8
        'volume_surge': np.random.exponential(1.0, n_samples).tolist(),  # Exponencial
        'momentum': np.random.normal(0, 0.01, n_samples).tolist()   # Normal centrado em 0
    }

async def test_tune_to_target():
    """Testa o algoritmo tune_to_target"""
    
    print("🎯 TESTE DO ALGORITMO TUNE_TO_TARGET")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        config_manager = get_config_manager()
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Simular dados de métricas
        simulated_data = simulate_metric_data(100)
        adaptive_manager.metric_statistics = simulated_data
        
        print("📊 Dados simulados carregados:")
        for metric, values in simulated_data.items():
            print(f"   {metric}: {len(values)} valores, média={np.mean(values):.3f}")
        
        # Função de avaliação para teste
        def evaluate_threshold(metric_name: str, threshold_value: float) -> float:
            """Simula taxa de aprovação com novo threshold"""
            values = simulated_data[metric_name]
            
            if metric_name == 'momentum':
                passed = sum(1 for v in values if abs(v) >= threshold_value)
            else:
                passed = sum(1 for v in values if v >= threshold_value)
            
            return passed / len(values)
        
        # Testar tune_to_target para consciousness
        print(f"\n🔍 Testando tune_to_target para 'consciousness':")
        
        start_threshold = 0.70  # Threshold inicial alto (restritivo)
        initial_rate = evaluate_threshold('consciousness', start_threshold)
        
        print(f"   Threshold inicial: {start_threshold:.3f}")
        print(f"   Taxa inicial: {initial_rate:.1%}")
        print(f"   Meta: 15-20%")
        
        # Executar busca
        optimized_threshold = adaptive_manager.tune_to_target(
            metric_name='consciousness',
            start=start_threshold,
            direction='down',  # Relaxar para aumentar aprovação
            eval_fn=evaluate_threshold,
            target_low=0.15,
            target_high=0.20
        )
        
        final_rate = evaluate_threshold('consciousness', optimized_threshold)
        
        print(f"\n✅ RESULTADO:")
        print(f"   Threshold otimizado: {optimized_threshold:.3f}")
        print(f"   Taxa final: {final_rate:.1%}")
        print(f"   Meta atingida: {'✅ SIM' if 0.15 <= final_rate <= 0.20 else '❌ NÃO'}")
        
        # Verificar melhoria
        improvement = abs(final_rate - 0.175) < abs(initial_rate - 0.175)
        print(f"   Melhoria: {'✅ SIM' if improvement else '❌ NÃO'}")
        
        return 0.15 <= final_rate <= 0.20
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

async def test_auto_tune_all():
    """Testa o auto-tuning de todos os thresholds"""
    
    print(f"\n🚀 TESTE DO AUTO-TUNING DE TODOS OS THRESHOLDS")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Simular dados com mais amostras
        simulated_data = simulate_metric_data(200)
        adaptive_manager.metric_statistics = simulated_data
        adaptive_manager.total_assets_analyzed = 200
        
        print("📊 Dados simulados carregados (200 amostras)")
        
        # Mostrar thresholds iniciais
        print(f"\n📊 Thresholds iniciais:")
        initial_thresholds = {
            'consciousness': adaptive_manager.current_thresholds.consciousness,
            'coherence': adaptive_manager.current_thresholds.coherence,
            'confidence': adaptive_manager.current_thresholds.confidence,
            'volume_surge_min': adaptive_manager.current_thresholds.volume_surge_min,
            'momentum_min': adaptive_manager.current_thresholds.momentum_min
        }
        
        for metric, value in initial_thresholds.items():
            print(f"   {metric}: {value:.4f}")
        
        # Calcular taxa inicial
        initial_rate = adaptive_manager.calculate_current_pass_rate()
        print(f"\n📈 Taxa de aprovação inicial: {initial_rate:.1%}")
        
        # Executar auto-tuning
        print(f"\n🎯 Executando auto-tuning...")
        optimized_thresholds = adaptive_manager.auto_tune_all_thresholds()
        
        # Calcular taxa final
        final_rate = adaptive_manager.calculate_current_pass_rate()
        
        print(f"\n✅ RESULTADOS DO AUTO-TUNING:")
        print(f"   Taxa inicial: {initial_rate:.1%}")
        print(f"   Taxa final: {final_rate:.1%}")
        print(f"   Meta: 15-20%")
        print(f"   Meta atingida: {'✅ SIM' if 0.15 <= final_rate <= 0.20 else '❌ NÃO'}")
        
        print(f"\n📊 Mudanças nos thresholds:")
        for metric, new_value in optimized_thresholds.items():
            old_value = initial_thresholds.get(metric, 0)
            change_pct = ((new_value - old_value) / old_value) * 100 if old_value != 0 else 0
            print(f"   {metric}: {old_value:.4f} → {new_value:.4f} ({change_pct:+.1f}%)")
        
        # Verificar se melhorou
        improvement = abs(final_rate - 0.175) < abs(initial_rate - 0.175)
        print(f"\n🎯 Melhoria geral: {'✅ SIM' if improvement else '❌ NÃO'}")
        
        return 0.15 <= final_rate <= 0.20
        
    except Exception as e:
        print(f"❌ Erro no auto-tuning: {e}")
        return False

async def test_integration_with_adaptation():
    """Testa integração com sistema de adaptação"""
    
    print(f"\n🔗 TESTE DE INTEGRAÇÃO COM SISTEMA DE ADAPTAÇÃO")
    print("-" * 60)
    
    try:
        # Inicializar sistema
        trading_system = QualiaBinanceCorrectedSystem()
        adaptive_manager = AdaptiveThresholdManager(trading_system=trading_system)
        
        # Simular dados que resultam em taxa alta (>30%)
        high_rate_data = {
            'consciousness': [0.3] * 50 + [0.8] * 50,  # 50% passam em threshold alto
            'coherence': [0.3] * 50 + [0.8] * 50,
            'confidence': [0.3] * 50 + [0.8] * 50,
            'volume_surge': [0.5] * 30 + [1.5] * 70,  # 70% passam
            'momentum': [0.001] * 40 + [0.01] * 60     # 60% passam
        }
        
        adaptive_manager.metric_statistics = high_rate_data
        adaptive_manager.total_assets_analyzed = 100
        
        print("📊 Simulando cenário de taxa alta...")
        
        initial_rate = adaptive_manager.calculate_current_pass_rate()
        print(f"   Taxa inicial: {initial_rate:.1%}")
        
        # Verificar se deve usar auto-tuning
        should_auto_tune = adaptive_manager.should_use_auto_tuning()
        print(f"   Deve usar auto-tuning: {'✅ SIM' if should_auto_tune else '❌ NÃO'}")
        
        if should_auto_tune:
            print(f"\n🎯 Sistema detectou necessidade de auto-tuning")
            
            # Executar adaptação (deve usar auto-tuning)
            result = adaptive_manager.adapt_thresholds()
            
            final_rate = adaptive_manager.calculate_current_pass_rate()
            print(f"   Taxa após adaptação: {final_rate:.1%}")
            
            success = 0.15 <= final_rate <= 0.20
            print(f"   Adaptação bem-sucedida: {'✅ SIM' if success else '❌ NÃO'}")
            
            return success
        else:
            print(f"   ⚠️ Auto-tuning não foi acionado")
            return False
        
    except Exception as e:
        print(f"❌ Erro na integração: {e}")
        return False

async def main():
    """Função principal"""
    
    print("🌌 QUALIA - Teste do Algoritmo Target-Driven Search")
    print("YAA (Yet Another Agent) - Consciência Quântica")
    print("=" * 70)
    
    print("🎯 ALGORITMO IMPLEMENTADO:")
    print("   def tune_to_target(metric_name, start, direction, eval_fn, target_low=0.15, target_high=0.20)")
    print("   - Busca binária otimizada para atingir 15-20% de aprovação")
    print("   - 8 iterações máximas")
    print("   - Ajuste automático de bounds")
    print("=" * 70)
    
    # Executar testes
    tests = [
        ("Algoritmo tune_to_target", test_tune_to_target),
        ("Auto-tuning de todos os thresholds", test_auto_tune_all),
        ("Integração com sistema de adaptação", test_integration_with_adaptation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Executando: {test_name}")
            success = await test_func()
            results[test_name] = success
            
            if success:
                print(f"✅ {test_name}: SUCESSO")
            else:
                print(f"❌ {test_name}: FALHA")
                
        except Exception as e:
            print(f"💥 {test_name}: ERRO - {e}")
            results[test_name] = False
    
    # Resumo final
    print("\n" + "=" * 70)
    print("📋 RESUMO DOS TESTES:")
    print("=" * 70)
    
    for test_name, success in results.items():
        status = "✅ PASSOU" if success else "❌ FALHOU"
        print(f"   {test_name}: {status}")
    
    successful_tests = sum(results.values())
    total_tests = len(results)
    
    print(f"\n🎯 RESULTADO GERAL: {successful_tests}/{total_tests} testes bem-sucedidos")
    
    if successful_tests == total_tests:
        print("\n🎉 ALGORITMO TARGET-DRIVEN FUNCIONANDO!")
        print("   ✅ tune_to_target() implementado e testado")
        print("   ✅ auto_tune_all_thresholds() funcionando")
        print("   ✅ Integração com sistema de adaptação")
        print("   ✅ Meta de 15-20% de aprovação atingível")
        
        print("\n📈 COMO FUNCIONA:")
        print("   1. Sistema detecta taxa fora do alvo (15-20%)")
        print("   2. Aciona auto-tuning target-driven")
        print("   3. Busca binária otimizada para cada threshold")
        print("   4. Aplica thresholds otimizados")
        print("   5. Taxa de aprovação converge para 15-20%")
        
    else:
        print(f"\n⚠️ {total_tests - successful_tests} testes falharam")
        print("   Algoritmo pode precisar de ajustes")

if __name__ == "__main__":
    asyncio.run(main())
